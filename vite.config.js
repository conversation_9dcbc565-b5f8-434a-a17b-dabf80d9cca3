import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import inject from "@rollup/plugin-inject";


import fs from "fs";
import path from "path";

const particleWasmPlugin= {
  name: "particle-wasm",
  apply: (_, env) => {
    return env.mode === "development";
  },
  buildStart: () => {
    const copiedPath = path.join(
      __dirname,
      "node_modules/@particle-network/thresh-sig/wasm/thresh_sig_wasm_bg.wasm"
    );
    const dir = path.join(__dirname, "node_modules/.vite/wasm");
    const resultPath = path.join(dir, "thresh_sig_wasm_bg.wasm");
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.copyFileSync(copiedPath, resultPath);
  },
};


// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), particleWasmPlugin],
  build: {
    rollupOptions: {
      plugins: [inject({ Buffer: ["buffer", "Buffer"] })],
    },
  },
  server: {
    proxy: {
      "/api": {
        target: "https://dapp.yc365.io",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
        configure: (proxy) => {
          proxy.on("proxyRes", (proxyRes) => {
            proxyRes.headers["Access-Control-Allow-Origin"] = "*";
            proxyRes.headers["Access-Control-Allow-Methods"] =
              "GET, POST, PUT, DELETE, OPTIONS";
            proxyRes.headers["Access-Control-Allow-Headers"] =
              "Content-Type, Authorization";
          });
        },
      },
    },
  },
  optimizeDeps: {
    esbuildOptions: {
      target: "esnext", // you can also use 'es2020' here
    },
  },
});
