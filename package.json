{"name": "gd-new", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@noble/curves": "^1.9.2", "@noble/hashes": "^1.8.0", "@particle-network/aa": "^2.0.2", "@particle-network/auth": "^1.3.1", "@particle-network/auth-core": "^2.0.6", "@particle-network/authkit": "^2.0.19", "@particle-network/connect": "^1.2.1", "@particle-network/connectkit": "^2.0.16", "@reown/appkit": "^1.7.8", "@reown/appkit-adapter-wagmi": "^1.7.8", "@tanstack/react-query": "^5.80.6", "@types/react-responsive": "^8.0.8", "@web3modal/wagmi": "^5.1.11", "antd": "^5.25.1", "axios": "^1.9.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "ethers": "^6.14.1", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-i18next": "^13.5.0", "react-responsive": "^10.0.1", "react-router-dom": "^6.30.0", "styled-components": "^6.1.18", "viem": "^2.31.0", "wagmi": "^2.15.6", "web3": "^4.16.0", "web3modal": "^1.9.12"}, "devDependencies": {"@eslint/js": "^9.25.0", "@rollup/plugin-inject": "^5.0.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.3", "@vitejs/plugin-react": "^4.5.2", "buffer": "^6.0.3", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.7", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}