{"root": ["./vite.config.ts", "./src/app.tsx", "./src/connectkit.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/connectwalletbutton.tsx", "./src/components/comments/commentsection.tsx", "./src/components/charts/duallinechart.tsx", "./src/components/charts/linechart.tsx", "./src/components/shared/menucomponents.tsx", "./src/context/appcontainer.tsx", "./src/context/appcontext.tsx", "./src/context/usercontext.tsx", "./src/context/usercontext_copy.tsx", "./src/hooks/usemediaquery.ts", "./src/i18n/index.ts", "./src/layout/header.tsx", "./src/layout/layout.tsx", "./src/pages/dashboard.tsx", "./src/pages/home.tsx", "./src/pages/marketdetail.tsx", "./src/pages/notfound.tsx", "./src/pages/orders.tsx", "./src/pages/profile.tsx", "./src/pages/setting.tsx", "./src/routes/index.tsx", "./src/services/apipaths.ts", "./src/services/request.ts", "./src/theme/themeprovider.tsx", "./src/theme/index.ts", "./src/utils/index.ts"], "version": "5.8.3"}