import React, { createContext, useState, useContext, useEffect, startTransition, Suspense } from 'react';
import type { ReactNode } from 'react';
import { ConfigProvider, Spin } from 'antd';
import type { ThemeType, ThemeContextType } from '.';
import { lightTheme, darkTheme } from '.';
import styled from 'styled-components';
import ErrorBoundary from '../components/ErrorBoundary';

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType>({
  theme: darkTheme,
  toggleTheme: () => {}
});

// 使用主题的钩子
export const useTheme = () => useContext(ThemeContext);

// 主题提供者属性
interface ThemeProviderProps {
  children: ReactNode;
}

// 创建一个带有主题样式的容器
const ThemedContainer = styled.div<{ theme: ThemeType }>`
  background-color: ${props => props.theme.backgroundColor};
  color: ${props => props.theme.textColor};
  min-height: 100vh;
  transition: all 0.3s ease;
`;

// 加载中组件
const LoadingFallback = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    backgroundColor: '#1D2B39'
  }}>
    <Spin size="large" />
  </div>
);

// 主题提供者组件
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // 主题状态
  const [currentTheme, setCurrentTheme] = useState<ThemeType>(() => {
    // 从本地存储中获取主题设置
    const savedTheme = localStorage.getItem('theme');
    return savedTheme === 'light' ? lightTheme : darkTheme;
  });

  // 切换主题函数
  const toggleTheme = () => {
    startTransition(() => {
    const newTheme = currentTheme.name === 'light' ? darkTheme : lightTheme;
    setCurrentTheme(newTheme);
    localStorage.setItem('theme', newTheme.name);
    });
  };

  // 当主题变化时，更新body类名
  useEffect(() => {
    startTransition(() => {
    // 移除所有主题相关的类名
    document.body.classList.remove('light-theme', 'dark-theme');
    // 添加当前主题的类名
    document.body.classList.add(`${currentTheme.name}-theme`);
    });
  }, [currentTheme.name]);

  return (
    <ErrorBoundary>
    <ThemeContext.Provider value={{ theme: currentTheme, toggleTheme }}>
      <ConfigProvider theme={currentTheme.antdTheme}>
        <ThemedContainer theme={currentTheme}>
            <Suspense fallback={<LoadingFallback />}>
          {children}
            </Suspense>
        </ThemedContainer>
      </ConfigProvider>
    </ThemeContext.Provider>
    </ErrorBoundary>
  );
};