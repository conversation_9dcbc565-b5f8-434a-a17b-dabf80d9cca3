import type { ThemeConfig } from 'antd';

// 定义主题接口
export interface ThemeType {
  name: string;
  antdTheme: ThemeConfig;
  backgroundColor: string;
  textColor: string;
  textColor1: string;
  primaryColor: string;
  secondaryTextColor: string;
  borderColor: string;
  cardBackgroundColor: string;
  componentBackgroundColor: string;
}

// 浅色主题配置
export const lightTheme: ThemeType = {
  name: 'light',
  antdTheme: {
    token: {
      colorPrimary: '#0852F0',
      colorBgContainer: '#ffffff',
      colorTextBase: '#000000',
    },
  },
  backgroundColor: '#ffffff',
  textColor: '#000000',
  textColor1: '#77808D',
  primaryColor: '#0852F0',
  secondaryTextColor: '#777E8C',
  borderColor: '#E7E7E7',
  cardBackgroundColor: '#ffffff',
  componentBackgroundColor: '#ECECEC',
};

// 深色主题配置
export const darkTheme: ThemeType = {
  name: 'dark',
  antdTheme: {
    token: {
      colorPrimary: '#1668dc',
      colorBgContainer: '#141414',
      colorTextBase: '#ffffff',
    },
  },
  backgroundColor: '#1D2B39',
  textColor: '#FFFFFF',
  textColor1: '#435363',
  primaryColor: '#2C9CDC',
  secondaryTextColor: '#97A0A4',
  borderColor: '#435363',
  cardBackgroundColor: '#375066',
  componentBackgroundColor: '#2C3F50',
};

// 主题上下文类型
export interface ThemeContextType {
  theme: ThemeType;
  toggleTheme: () => void;
}





