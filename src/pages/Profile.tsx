import React, { useState, useEffect } from 'react';
import { 
  Layout, 
  Typography, 
  Button, 
  Avatar, 
  Row, 
  Col, 
  Drawer, 
  Grid,
  Menu,
  Modal,
  message,
  Empty
} from 'antd';
import { 
  CloseOutlined,
  HomeOutlined,
  UnorderedListOutlined,
  UploadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useMediaQuery } from 'react-responsive';
import { useTheme } from '../theme/ThemeProvider';
import './Profile.css';
import { useTranslation } from 'react-i18next';
import { DrawerContent } from '../components/shared/MenuComponents';
// import AppHeader from '../layout/Header';
import { useNavigate } from 'react-router-dom';
import Web3 from 'web3';
import { ethers } from 'ethers';
import { dappApi } from '../services/request';
import { DAPP_API } from '../services/apiPaths';
// import toast, { Toaster } from 'react-hot-toast';
// import usdcAbi from '../contracts/mocks/MockERC20.sol/MockERC20.json';
// import vaultAbi from '../contracts/Vault.sol/Vault.json';
import { useUser } from '../context/UserContext';
import type { Eip1193Provider } from 'ethers';


// 导入资产图标
import userAvatarImage from '../assets/figma_images/eventTitleImg.png';
import iconDepositLight from '../assets/figma_images/icon-depositL.svg';
import iconDeposit from '../assets/figma_images/icon-deposit.svg';
import iconGasLight from '../assets/figma_images/icon-gasL.svg';
import iconGas from '../assets/figma_images/icon-gas.svg';
import iconRecentLight from '../assets/figma_images/darningL.svg';
import iconRecent from '../assets/figma_images/darning.svg';
import iconSevenDayLight from '../assets/figma_images/icon-7dayL.svg';
import iconSevenDay from '../assets/figma_images/icon-7day.svg';
import iconTotalLight from '../assets/figma_images/icon-totalL.svg';
import iconTotal from '../assets/figma_images/icon-total.svg';

const {  Content, Footer } = Layout;
const { Title, Text } = Typography;
const { useBreakpoint } = Grid;

const Profile: React.FC = () => {
  const { t } = useTranslation();
  const { theme, toggleTheme } = useTheme();
  const screens = useBreakpoint();
  const isMobileMediaQuery = useMediaQuery({ maxWidth: 768 });
  const isMobile = !screens.md || isMobileMediaQuery;
  const [isLoggedIn, setIsLoggedIn] = useState(true);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const navigate = useNavigate();
  const [walletAddress, setWalletAddress] = useState('');
  const [balance, setBalance] = useState('0');
  const [balanceData, setBalanceData] = useState<any>(null);
  // const [profileData, setProfileData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  // const [vaultBalance, setVaultBalance] = useState(0);
  // const [usdcBalance, setUsdcBalance] = useState(0);
  const contractAddress = import.meta.env.VITE_VAULT_ADDRESS || 'Not Found';
  // const [usdcContract, setUsdcContract] = useState<ethers.Contract | null>(null);
  // const [vaultContract, setVaultContract] = useState<ethers.Contract | null>(null);
  // const [signer, setSigner] = useState<ethers.Signer | null>(null);
  const { profileData, usdcContract, vaultContract,networkName,address,provider,isWallet,signer } = useUser();
  const [usdcBalance, setUsdcBalance] = useState<string>('0');
  const [vaultBalance, setVaultBalance] = useState<string>('0');
  // const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);

  // Deposit/Withdraw Modal状态
  const [depositModalVisible, setDepositModalVisible] = useState(false);
  const [currentTab, setCurrentTab] = useState("deposit");
  const [amount, setAmount] = useState<string>("");


  // 主题相关
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';
  const [messageApi, contextHolder] = message.useMessage();
  
  // 通用消息提示方法，可以传入消息类型
  const showMessage = (msg: String, type: 'success' | 'error' | 'warning' ) => {
    messageApi.open({
      type: type,
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  
  // 保留原有方法，但使用通用方法实现
  const errorMsg = (msg: String) => showMessage(msg, 'error');
  // const warningMsg = (msg: String) => showMessage(msg, 'warning');
  const successMsg = (msg: String) => showMessage(msg, 'success');

  // 获取用户数据
  const fetchUserData = async (address: string) => {
    try {
      setIsLoading(true);
      // 获取余额概览
      const balanceOverview = await dappApi.get(`${DAPP_API.GET_OVERVIEW}/${address}`);
      setBalanceData(balanceOverview.data);
      console.log("balanceOverview:",balanceOverview);
      
      // 获取个人资料
      // const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`);
      // setProfileData(profile.data);
      // console.log("profile:",profile);
    } catch (error) {
      console.error('获取用户数据失败:', error);
      // errorMsg('获取用户数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取钱包地址和初始化Web3
  useEffect(() => {
    const getWalletAddress = async () => {
      try {
        if (window.ethereum) {
          const web3 = new Web3(window.ethereum);
          // await(window.ethereum as any).request({ method: 'eth_requestAccounts' });
          // const accounts = await web3.eth.getAccounts();
          // const address = accounts[0];
          setWalletAddress(address);
          
          // 获取余额
          // const balance = await web3.eth.getBalance(address);

          if(isWallet==="1"){
            const providerMain = new ethers.BrowserProvider(provider as Eip1193Provider, "any");
            const balance = await providerMain.getBalance(address);
            setBalance(web3.utils.fromWei(balance, 'ether'));
          }else if(isWallet==="2"){
            const balance = await web3.eth.getBalance(address);
            setBalance(web3.utils.fromWei(balance, 'ether'));
          }

          // const providerMain = new ethers.BrowserProvider(provider as Eip1193Provider, "any");
          // const balance = await providerMain.getBalance(address);
          // setBalance(web3.utils.fromWei(balance, 'ether'));
          
          // 获取用户数据
          if (address) {
            await fetchUserData(address);
          }
        } else {
          // warningMsg('请安装MetaMask或其他以太坊钱包');
        }
      } catch (error) {
        console.error('获取钱包地址失败:', error);
        errorMsg('获取钱包地址失败');
      }
    };
    getWalletAddress();
  }, []);

  // 初始化 signer
  // const initializeSigner = async () => {
  //   if (window.ethereum) {
  //     try {
  //       // 1. 获取 provider
  //       const provider = new BrowserProvider(window.ethereum);
  //       // setProvider(provider);
        
  //       // 2. 请求用户授权
  //       await provider.send('eth_requestAccounts', []);

  //       // 3. 获取 signer
  //       const signer = await provider.getSigner();
  //       setSigner(signer);

  //       // 4. 获取地址
  //       const address = await signer.getAddress();
  //       console.log('address:', address);
  //       console.log('signer状态:', signer ? '已初始化' : '未初始化');
  //     } catch (error) {
  //       console.error('初始化 signer 失败:', error);
  //       message.error('初始化钱包失败');
  //     }
  //   } else {
  //     message.warning('请安装 MetaMask 或其他以太坊钱包');
  //   }
  // };

  // 初始化合约
  // useEffect(() => {
  //   const initializeContracts = async () => {
  //     if (signer) {
  //       try {
  //         const usdc = new ethers.Contract(
  //           import.meta.env.VITE_USDC_ADDRESS,
  //           usdcAbi.abi,
  //           signer
  //         );
  //         const vault = new ethers.Contract(
  //           import.meta.env.VITE_VAULT_ADDRESS,
  //           vaultAbi.abi,
  //           signer
  //         );
          
  //         setUsdcContract(usdc);
  //         setVaultContract(vault);
  //       } catch (error) {
  //         console.error('初始化合约失败:', error);
  //       }
  //     }
  //   };

  //   initializeContracts();
  // }, [signer]);



  

  // 监听钱包变化
  // useEffect(() => {
  //   if (window.ethereum) {
  //     const handleAccountsChanged = async (accounts: string[]) => {
  //       if (accounts.length === 0) {
  //         setSigner(null);
  //         message.info('钱包已断开连接');
  //       } else {
  //         await initializeSigner();
  //       }
  //     };

  //     const handleChainChanged = () => {
  //       window.location.reload();
  //     };

  //     window.ethereum.on('accountsChanged', handleAccountsChanged);
  //     window.ethereum.on('chainChanged', handleChainChanged);

  //     // 初始化
  //     initializeSigner();

  //     return () => {
  //       window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
  //       window.ethereum.removeListener('chainChanged', handleChainChanged);
  //     };
  //   }
  // }, []);

  // 刷新数据函数
  const refreshData = async () => {
    if (walletAddress) {
      await fetchUserData(walletAddress);
    }
  };

  // 资产数据
  const assetData = {
    depositBalance: {
      title: t('overview.depositBalance'),
      amount: balanceData?.assets?.deposit || "0",
      path: '/'
    },
    gasBalance: {
      title: t('overview.gasBalance'),
      amount: balance?.toString() || "0",
      path: '/'
    },
    vaultBalance: {
      title: t('overview.vaultBalance'),
      amount: balanceData?.assets?.vault || "0",
      path: '/'
    }
  };

  // 收益数据
  const earningsData = {
    recent: {
      title: t('overview.recentEarning'),
      amount: balanceData?.earning?.recent || "0", // 只存数字字符串
      path: '/market/1'
    },
    sevenDay: {
      title: t('overview.7day'),
      amount: balanceData?.earning?.recent_7_days || "0",
      path: '/market/2'
    },
    total: {
      title: t('overview.totalEarning'),
      amount: balanceData?.assets?.total_earning || "0",
      path: '/market/3'
    }
  };

  // 处理函数
  const handleConnectWallet = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  const onCloseDrawer = () => {
    setDrawerVisible(false);
  };

  // 弹窗相关函数
  const showDepositModal = () => {
    setDepositModalVisible(true);
    setCurrentTab("deposit");
  };

  const showWithdrawModal = () => {
    setDepositModalVisible(true);
    setCurrentTab("withdraw");
  };

  const handleDepositModalClose = () => {
    setDepositModalVisible(false);
  };
  
  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };
  
  const handleAmountChange = (value: string) => {
    setAmount(value);
  };
  
  const handleSetMaxAmount = () => {
    const valueMax = currentTab === 'deposit' ? Math.floor(Number(usdcBalance)) : vaultBalance;
    setAmount(valueMax?.toString() || '0');
  };

      // 格式化数字函数
      const formatNumber = (num: number): string => {
        if (num >= 1000000000) {
          return (num / 1000000000).toFixed(2) + 'B';
        }
        if (num >= 1000000) {
          return (num / 1000000).toFixed(2) + 'M';
        }
        if (num >= 1000) {
          return (num / 1000).toFixed(2) + 'k';
        }
        return num.toFixed(2);
      };

  // 处理存款
  const handleDeposit = async () => {
    if (!walletAddress || !usdcContract || !vaultContract) {
      console.error('Web3 not ready');
      return;
    }

    // const gasConfig = {
    //   maxFeePerGas: ethers.parseUnits('150', 'gwei'),
    //   maxPriorityFeePerGas: ethers.parseUnits('100', 'gwei'),
    //   gasLimit: 3000000,
    // };

    if (!amount || parseFloat(amount.toString()) <= 0) {
      errorMsg(t('default.PleaseEnterTheValidAmount'));
      return;
    }

    try {
      const amountWei = ethers.parseUnits(amount.toString(), 18);
      // 先授权 Vault 合约
      const approveTx = await usdcContract.approve(vaultContract.target, amountWei);
      await approveTx.wait();

      // 调用 Vault 合约的存款方法
      const depositTx = await vaultContract.deposit(amountWei);
      await depositTx.wait();

      setAmount('');
      successMsg(t('default.AmountDepositSuccessfully'));
      refreshData();
      handleDepositModalClose();
    } catch (err: any) {
      console.error('❌ Error:', err.message);
      errorMsg(err.message);
    }
  };

  // 处理提现
  const handleWithdrawal = async () => {
    if (!walletAddress || !usdcContract || !vaultContract) {
      console.error('Web3 not ready');
      return;
    }

    // const gasConfig = {
    //   maxFeePerGas: ethers.parseUnits('150', 'gwei'),
    //   maxPriorityFeePerGas: ethers.parseUnits('100', 'gwei'),
    //   gasLimit: 3000000,
    // };

    if (!amount || parseFloat(amount.toString()) <= 0) {
      errorMsg(t('default.PleaseEnterTheValidAmount'));
      return;
    }

    try {
      const amountWei = ethers.parseUnits(amount.toString(), 18);
      const withdrawTx = await vaultContract.withdraw(amountWei);
      await withdrawTx.wait();
      
      setAmount('');
      successMsg(t('default.amountSuccessfull'));
      refreshData();
      handleDepositModalClose();
    } catch (err: any) {
      console.error('❌ Error:', err.message);
      // errorMsg(t('default.handleSubmitTransactionError'), err.message);
    }
  };

  // 处理交易提交
  const handleSubmitTransaction = async () => {
    if (isLoading) return;

    if (Number(amount)) {
      setIsLoading(true);
      try {
        if (currentTab === 'deposit') {
          await handleDeposit();
        } else if (currentTab === 'withdraw') {
          await handleWithdrawal();
        }
      } catch (err) {
        console.error(t('default.handleSubmitTransactionError'), err);
      } finally {
        setIsLoading(false);
      }
    } else {
      errorMsg(t('default.enterVaildAmount'));
    }
  };

  // 获取合约余额
  useEffect(() => {
    const getVaultBalance = async () => {
      if (vaultContract && walletAddress) {
        try {
          const balance = await vaultContract.balanceOf(walletAddress);
          setVaultBalance((Number(balance.toString()) / 1000000000000000000).toString());
          console.log('Vault balance:', balance)
          const Ubalance = await usdcContract?.balanceOf(walletAddress);
          setUsdcBalance((Number(Ubalance.toString()) / 1000000000000000000).toString());
        } catch (error) {
          console.error('获取金库余额失败:', error);
        }
      }
    };

    getVaultBalance();
  }, [vaultContract, walletAddress, depositModalVisible, refreshData, usdcContract]);

  // 渲染资产卡片
  const renderAssetCard = (title: string, amount: string, icon: string, path: string) => (
    <div 
      className={`asset-card ${themeClass}`} 
      onClick={() =>console.log(path) }
      style={{marginLeft: '16px'}}
    >
      <div className="asset-icon-container">
        <img src={icon} alt={title} />
      </div>
      <div className="asset-info">
        <Text className="asset-title">{title}</Text>
        <Text className="asset-amount">{amount}</Text>
      </div>
    </div>
  );
   // 渲染资产卡片
   const renderAssetCardRight = (title: string, amount: string, icon: string, path: string) => (
    <div 
      className={`asset-card ${themeClass}`} 
      onClick={() =>console.log(path) }
      style={{marginRight: '16px'}}
    >
      <div className="asset-icon-container">
        <img src={icon} alt={title} />
      </div>
      <div className="asset-info">
        <Text className="asset-title">{title}</Text>
        <Text className="asset-amount">{amount}</Text>
      </div>
    </div>
  );

  // 渲染收益卡片
  const renderEarningCard = (title: string, amount: string, icon: string, path: string) => (
    <div 
      className={`earning-card ${themeClass}`} 
      onClick={() =>console.log(path) }
      style={{marginLeft: '16px'}}
    >
      <div className="earning-icon-container">
        <img src={icon} alt={title} />
      </div>
      <div className="earning-info">
        <Text className="earning-title">{title}</Text>
        <Text className="earning-amount">{amount}</Text>
      </div>
    </div>
  );
  const renderEarningCardRight = (title: string, amount: string, icon: string, path: string) => (
    <div 
      className={`earning-card ${themeClass}`} 
      onClick={() =>console.log(path) }
      style={{marginRight: '16px'}}
    >
      <div className="earning-icon-container">
        <img src={icon} alt={title} />
      </div>
      <div className="earning-info">
        <Text className="earning-title">{title}</Text>
        <Text className="earning-amount">{amount}</Text>
      </div>
    </div>
  );

  // const handleEditProfile = () => {
  //   // 实现编辑个人资料逻辑
  //   console.log('Edit profile clicked');
  // };

  return (
    <Layout className={`profile-layout ${themeClass}`} style={{marginTop: isMobile? '-70px':'' }}>
      {/* <AppHeader
        isLoggedIn={isLoggedIn}
        handleConnectWallet={handleConnectWallet}
        handleLogout={handleLogout}
        showDrawer={showDrawer}
        showDepositModal={showDepositModal}
        renderDropdownMenu={renderProfileDropdownMenu}
      /> */}
      {contextHolder}
      <Content className="profile-content" style={{ 
        paddingBottom: isMobile ? '60px' : '0',
      }}>
        {!address || !signer ? (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: '100vh',
            color: theme.textColor
          }}>
            <Empty
              description={
                <div>
                  {/* <p style={{ color: theme.textColor }}>{t('default.noData')}</p> */}
                  <p style={{ color: theme.textColor }}>{t('comments.loginRequired')}</p>
                </div>
              }
              image={null}
              style={{ color: theme.textColor }}
            />
          </div>
        ) : (
          <>
            {/* 用户资料部分 */}
            <div style={{display: 'flex', alignItems: 'center', marginBottom: '0px',marginLeft: '30px' ,marginTop:isMobile ? '30px' : '0px'}}>
              <Avatar src={profileData && profileData.profile_image ? profileData.profile_image : userAvatarImage} size={80} className="user-avatar" />
              <div className="profile-info" style={{marginLeft: '16px'}}>
                <Title level={3} className="profile-name">{profileData?.username || 'Anonymous'}</Title>
                <div className="profile-id">{walletAddress}</div>
                {!isMobile && <div className="profile-join-date">{profileData?.joinDate || ''}</div>}
              </div>
            </div>

            {/* 存款/提现按钮 */}
            {isMobile ? ( 
              <div className="action-buttons" style={{marginLeft: '16px',marginRight: '16px'}}>
                <Button 
                  type="primary" 
                  className={`deposit-btn ${themeClass}`} 
                  icon={<DownloadOutlined />}
                  onClick={showDepositModal}
                >
                  {t('default.deposit')}
                </Button>
                <Button 
                  className={`withdraw-btn ${themeClass}`} 
                  icon={<UploadOutlined />}
                  onClick={showWithdrawModal}
                >
                  {t('default.withdraw')}
                </Button>
              </div>
            ):(
              <div className="action-buttons" style={{marginLeft: '100px',marginRight: '16px',width: '40%'}}>
                <Button 
                  type="primary" 
                  className={`deposit-btn ${themeClass}`} 
                  icon={<DownloadOutlined />}
                  onClick={showDepositModal}
                >
                  {t('default.deposit')}
                </Button>
                <Button 
                  className={`withdraw-btn ${themeClass}`} 
                  icon={<UploadOutlined />}
                  onClick={showWithdrawModal}
                >
                  {t('default.withdraw')}
                </Button>
              </div>
            )}

            {/* 资产部分 */}
            <div className="section-container">
              <Title level={4} className="section-title">{t('default.assets')}</Title>
              <Row gutter={[16, 16]}>
                <Col xs={12} md={8} >
                  {renderAssetCard(
                    assetData.depositBalance.title, 
                    Number(assetData.depositBalance.amount).toFixed(2),
                    theme.name === 'dark' ? iconDeposit : iconDepositLight,
                    assetData.depositBalance.path
                  )}
                </Col>
                <Col xs={12} md={8}>
                  {isMobile?renderAssetCardRight(
                    assetData.gasBalance.title, 
                    Number(assetData.gasBalance.amount).toFixed(2), 
                    theme.name === 'dark' ? iconGas : iconGasLight,
                    assetData.gasBalance.path
                  ):
                  renderAssetCard(
                    assetData.gasBalance.title, 
                    Number(assetData.gasBalance.amount).toFixed(2), 
                    theme.name === 'dark' ? iconGas : iconGasLight,
                    assetData.gasBalance.path
                  )}
                </Col>
                <Col xs={12} md={8} >
                  {renderAssetCard(
                    assetData.vaultBalance.title, 
                    Number(assetData.vaultBalance.amount).toFixed(2),
                    theme.name === 'dark' ? iconDeposit : iconDepositLight,
                    assetData.vaultBalance.path
                  )}
                </Col>
              </Row>
            </div>

            {/* 收益部分 */}
            <div className="section-container">
              <Title level={4} className="section-title">{t('overview.earning')}</Title>
              <Row gutter={[16, 16]}>
                <Col xs={12} md={8}>
                  {renderEarningCard(
                    earningsData.recent.title, 
                    Number(earningsData.recent.amount).toFixed(2),
                    theme.name === 'dark' ? iconRecent : iconRecentLight,
                    earningsData.recent.path
                  )}
                </Col>
                <Col xs={12} md={8}>
                  {isMobile?renderEarningCardRight(
                    earningsData.sevenDay.title, 
                    Number(earningsData.sevenDay.amount).toFixed(2),
                    theme.name === 'dark' ? iconSevenDay : iconSevenDayLight,
                    earningsData.sevenDay.path
                  ):
                  renderEarningCard(
                    earningsData.sevenDay.title, 
                    Number(earningsData.sevenDay.amount).toFixed(2),
                    theme.name === 'dark' ? iconSevenDay : iconSevenDayLight,
                    earningsData.sevenDay.path
                  )}
                </Col>
                <Col xs={12} md={8}>
                  {renderEarningCard(
                    earningsData.total.title, 
                    Number(earningsData.total.amount).toFixed(2),
                    theme.name === 'dark' ? iconTotal : iconTotalLight,
                    earningsData.total.path
                  )}
                </Col>
              </Row>
            </div>
          </>
        )}
      </Content>

      {/* 移动端底部导航 */}
      {isMobile && (
        <Footer 
          className="mobile-footer" 
          style={{ 
            backgroundColor: theme.name === 'dark' ? '#1D2B39' : '#FFFFFF', 
            borderTop: `1px solid ${theme.name === 'dark' ? '#435363' : '#E8E8E8'}`,
            position: 'fixed',
            bottom: 0,
            left: 0,
            width: '100%',
            zIndex: 10,
            padding: '0',
            boxShadow: '0 -2px 8px rgba(0,0,0,0.15)'
          }}
        >
          <Menu mode="horizontal" defaultSelectedKeys={['person']} className={`mobile-tab-bar ${themeClass}`} theme={theme.name as any}>
            <Menu.Item key="home" icon={<HomeOutlined style={{ fontSize: '20px' }} />} onClick={() => navigate('/')}>
              <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.home')}</span>
            </Menu.Item>
            <Menu.Item key="orders" icon={<UnorderedListOutlined style={{ fontSize: '20px' }} />} onClick={() => navigate('/orders')}>
              <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.orders')}</span>
            </Menu.Item>
            <Menu.Item key="person" icon={<Avatar src={userAvatarImage} size={20} />}>
              <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.person')}</span>
            </Menu.Item>
          </Menu>
        </Footer>
      )}

      {/* 抽屉菜单 */}
      <Drawer
        title={isLoggedIn ? <Avatar src={userAvatarImage} /> : t('drawer.menuTitle')}
        placement="left"
        onClose={onCloseDrawer}
        open={drawerVisible}
        closable={true}
        closeIcon={<CloseOutlined style={{color: theme.textColor}}/>}
        bodyStyle={{ 
          padding: 0, 
          backgroundColor: theme.backgroundColor,
          height: '100%'
        }}
        headerStyle={{ 
          backgroundColor: theme.backgroundColor, 
          borderBottom: `1px solid ${theme.borderColor}`
        }}
        className={`dark-drawer ${themeClass}`}
        style={{ background: theme.backgroundColor }}
      >
        <DrawerContent
          theme={theme}
          isLoggedIn={isLoggedIn}
          themeClass={themeClass}
          handleConnectWallet={handleConnectWallet}
          handleLogout={handleLogout}
          toggleTheme={toggleTheme}
        />
      </Drawer>

      {/* Deposit/Withdraw Modal */}
      <Modal
        open={depositModalVisible}
        onCancel={handleDepositModalClose}
        footer={null}
        width={isMobile? '100%' : '50%'}
        closable={false}
        destroyOnClose
        centered
        className={`deposit-withdraw-modal ${themeClass}`}
        maskClosable={true}
      >
        <div className={`deposit-withdraw-container ${themeClass}`}>
          <div style={{width: '100%', height: '44px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}> 
            <CloseOutlined 
              className="ant-modal-close" 
              onClick={handleDepositModalClose}
              style={{ 
                color: theme.name === 'dark' ? '#fff' : '#777E8C' 
              }}
            /> 
          </div>

          {/* Tab切换按钮 */}
          <div className="modal-tabs">
            <div 
              className={`tab-button ${currentTab === 'deposit' ? 'active' : ''}`} 
              onClick={() => handleTabChange('deposit')}
              style={{
                backgroundColor: currentTab === 'deposit' 
                  ? (theme.name === 'dark' ? '#243546' : '#1677FF') 
                  : (theme.name === 'dark' ? '#1A2531' : '#F5F5F5'),
                color: currentTab === 'deposit'
                  ? '#FFFFFF'
                  : (theme.name === 'dark' ? '#97A0A4' : '#777E8C')
              }}
            >
              {t('default.deposit')}
            </div>
            <div 
              className={`tab-buttonleft ${currentTab === 'withdraw' ? 'active' : ''}`}
              onClick={() => handleTabChange('withdraw')}
              style={{
                backgroundColor: currentTab === 'withdraw' 
                  ? (theme.name === 'dark' ? '#243546' : '#1677FF') 
                  : (theme.name === 'dark' ? '#1A2531' : '#F5F5F5'),
                color: currentTab === 'withdraw'
                  ? '#FFFFFF'
                  : (theme.name === 'dark' ? '#97A0A4' : '#777E8C')
              }}
            >
              {t('default.withdraw')}
            </div>
          </div>
          
          {/* 表单内容 */}
          <div className="modal-form">
            {/* Network & Currency */}
            <div className="form-field" >
              <div className="field-label" style={{ 
                  color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
                  width: '100%',  // 固定宽度为50%
                  textAlign: 'left' , // 文字左对齐
                  // backgroundColor:'#00ff00'
                }}>
                {t('default.network')}
              </div>
              <div className="field-value" style={{ 
                color: theme.textColor,
                width: '50%',  // 固定宽度为50%
                textAlign: 'right',  // 文字右对齐
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                paddingLeft: '10px'
               }}>
                {networkName}/USDT
              </div>
            </div>
            
            {/* Amount */}
            <div className="form-field">
              <div className="field-label" style={{ 
    color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
    width: '50%',  // 固定宽度为50%
    textAlign: 'left'  // 文字左对齐
  }}>
                {currentTab === 'deposit' ? t('default.depositAmount') : t('default.withdrawAmount')}
              </div>
              <div className="field-value available-balance" style={{ color: '#1677FF',width: '100%', textAlign: 'right', whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    paddingLeft: '10px'  }}>
                {currentTab === 'deposit' ?formatNumber( Number(usdcBalance)) : formatNumber(Number(vaultBalance))} USDT 
              </div>
            </div>
            
            {/* Amount Input */}
            <div className="amount-input-container">
              <input
                type="text"
                placeholder={t('default.enterAmount')}
                value={amount}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="amount-input"
                style={{
                  backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                  color: theme.textColor,
                  border: 'none'
                }}
              />
              <button
                className="max-button"
                onClick={handleSetMaxAmount}
                style={{
                  backgroundColor: theme.name === 'dark' ? '#3B4754' : '#FFFFFF',
                  color: theme.name === 'dark' ? '#FFFFFF' : '#000000'
                }}
              >
                {t('default.max')}
              </button>
            </div>
            
            {/* Address Fields */}
            <div className="form-field" >
              <div className="field-label" style={{ 
    color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
    width: '100%',  // 固定宽度为50%
    textAlign: 'left'  // 文字左对齐
  }}>
                {currentTab === 'deposit' ? t('default.paymentAddress') : t('default.receivingAddress')}
              </div>
            </div>
            <input
              type="text"
              value={walletAddress}
              readOnly
              className="amount-input"
              style={{
                backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                color: theme.textColor,
                border: 'none'
              }}
            />
            
            <div className="form-field">
              <div className="field-label" style={{ 
    color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
    width: '100%',  // 固定宽度为50%
    textAlign: 'left'  // 文字左对齐
  }}>
                {t('default.contractAddress')}
              </div>
            </div>
            <input
              type="text"
              value={contractAddress}
              readOnly
              className="amount-input"
              style={{
                backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                color: theme.textColor,
                border: 'none'
              }}
            />
            
            {/* Submit Button */}
            <button
              className="submit-button"
              onClick={handleSubmitTransaction}
              disabled={isLoading}
              style={{
                backgroundColor: '#1677FF',
                marginTop: '50px',
              }}
            >
              {isLoading ? t('default.processing') : t('default.submitTrans')}
            </button>
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

export default Profile; 