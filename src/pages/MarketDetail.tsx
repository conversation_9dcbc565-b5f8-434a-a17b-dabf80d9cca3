import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useParams, useSearchParams, useLocation } from 'react-router-dom';
import { 
  Layout, 
  Typography, 
  Button, 
  Tabs, 
  Row, 
  Col, 
  Input, 
  Avatar, 
  Drawer,
  Tooltip,
  Spin,message,Flex
} from 'antd';
import { useMediaQuery } from 'react-responsive';
import { 
  CloseOutlined,
  StarOutlined,
  StarFilled,
  TwitterOutlined
} from '@ant-design/icons';
import { useTheme } from '../theme/ThemeProvider.js';
import './MarketDetail.css';
import userAvatarImage from '../assets/figma_images/user_avatar_image.png';
import {  DrawerContent } from '../components/shared/MenuComponents.js';
import { LineChart } from '../components/charts/LineChart.js';
import { useTranslation } from 'react-i18next';
import { ethers } from 'ethers';
import { useUser } from '../context/UserContext.js';  
import CommentSection from '../components/Comments/CommentSection.js';
import { useConnect, useConnectors} from '@particle-network/connectkit'; 
import { useEthereum } from "@particle-network/authkit";
import { eventBus } from '../utils/eventBus';
import { dappApi } from '../services/request.js';
// Add interface for market prices response
interface MarketPricesResponse {
  yesBuyPrice: number;
  yesSellPrice: number;
  noBuyPrice: number;
  noSellPrice: number;
}

// 获取市场价格的函数
// Function to get market prices
const getOrderBookMarketPrices = async (id: string) => {
  const baseUrl = import.meta.env.VITE_SECOND_BASE_URL;
  if (!baseUrl) {
    throw new Error("VITE_SECOND_BASE_URL environment variable is not defined");
  }
  
  const url = `${baseUrl}/marketPrices/${id}`;
  
  return {
    unwrap: async () => {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`API responded with status: ${response.status}`);
        }
        
        const data = await response.json();
        if (data && data.code === 0 && data.data) {
          return data.data as MarketPricesResponse;
        } else {
          throw new Error(`Invalid API response: ${data?.message || 'Unknown error'}`);
        }
      } catch (error) {
        console.error("Error fetching market prices:", error);
        throw error;
      }
    }
  };
};

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;


// Define proper interfaces for the API data structure
interface Condition {
  cid: string;
  coin_name: string;
  description: string;
  expire_time: number;
  game_id: string;
  icon_url: string;
  id: string;
  no_amount: number;
  no_coin_price: number;
  no_order_id: string;
  result: number;
  status: number;
  stop_time: number;
  transaction_hash: string;
  yes_amount: number;
  yes_coin_price: number;
  yes_order_id: string;
}

interface ApiMarketData {
  chance: number;
  cid: string;
  conditions: Condition[];
  description: string;
  expire_time: number;
  game_type: string;
  icon_url: string;
  id: string;
  isGameCondition: boolean;
  rules: string;
  status: number;
  stop_time: number;
  type_id: number;
  vol:number;
}

// Keep the existing interfaces for the mock data
interface MarketOutcome {
  id: number;
  name: string;
  chance: string;
  yesPrice: string;
  noPrice: string;
  result: number;
}

interface MarketData {
  id: number;
  title: string;
  date: string;
  outcomes: MarketOutcome[];
  rules: string[];
  comments: any[]; // Keep this for backward compatibility
  vol: number;
}

// Define interface for the API response
interface OrderBookItem {
  price: string;
  quantity: string;
  tvl: string;
}

interface OrderBookData {
  asks: OrderBookItem[];
  bids: OrderBookItem[];
}

interface OrderBookApiResponse {
  code: number;
  data: OrderBookData;
  message: string;
}

// Define interface for the price history API response
interface PriceHistoryItem {
  id: number;
  condition_id: number;
  interval: string;
  open_price: number;
  close_price: number;
  high_price: number;
  low_price: number;
  volume?: number;
  timestamp?: number;
  start_time?: number;
  end_time?: number;
}

interface PriceHistoryResponse {
  status?: string;
  message?: string;
  data?: PriceHistoryItem[];
}

// Sample data for the market detail
const marketDetail: MarketData = {
  id: 1,
  title: "Which Trump picks will be confirmed?",
  date: "Jun 30, 2025 00:00:00",
  vol: 0 ,
  outcomes: [
    {
      id: 1,
      name: "Ethereum price on June 27",
      chance: "28%",
      yesPrice: "92¢",
      noPrice: "92¢",
      result:0,
    },
  ],
  rules: [
    "This Market Will Resolve According To The Final \"Close\" Price Of The Binance 1 Minute Candle For ETHUSDT 02 June '25 12:00 In The ET Timezone (Noon). Otherwise, This Market Will Resolve To No.",
    "The Resolution Source For This Market Is Binance. Specifically The ETHUSDT \"Close\" Prices Currently Available At Https://Www.Binance.Com/En/Trade/ETH_USDT With \"1m\" And \"Candles\" Selected On The Top Bar. If The Reported Value Falls Exactly Between Two Brackets, Then This Market Will Resolve To The Higher Range Bracket. Please Note That This Market Is About The Price According To Binance ETHUSDT, Not According To Other Sources Or Spot Markets"
  ],
  comments: [
    {
      id: 1,
      author: "Robert Fox",
      avatar: userAvatarImage,
      time: "1d ago",
      content: "atleast be honest, we both know youre going to gamble whatever anyone gives you!",
      replies: []
    }
  ]
};

// Default order book data as fallback
const defaultOrderBookData = {
  asks: [
    { price: "10¢", shares: "890.46", total: "$171.06" },
    { price: "10¢", shares: "890.46", total: "$171.06" },
    { price: "9¢", shares: "890.46", total: "$171.06" },
    { price: "8¢", shares: "890.46", total: "$171.06" },
    { price: "7¢", shares: "890.46", total: "$171.06" }
  ],
  bids: [
    { price: "5¢", shares: "890.46", total: "$171.06" },
    { price: "4¢", shares: "890.46", total: "$171.06" },
    { price: "3¢", shares: "890.46", total: "$171.06" },
    { price: "2¢", shares: "890.46", total: "$171.06" },
    { price: "1¢", shares: "890.46", total: "$171.06" }
  ]
};

// Add a new interface for outcome-specific order book data
interface OutcomeOrderBookData {
  outcomeId: number;
  asks: Array<{ price: string; shares: string; total: string }>;
  bids: Array<{ price: string; shares: string; total: string }>;
  isLoading: boolean;
  error: string | null;
}

const MarketDetail: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme, toggleTheme } = useTheme();
  const { marketId } = useParams<{ marketId: string }>();
  const [searchParams] = useSearchParams();
  // const location = useLocation();
  // const passedMarketData = location.state?.marketData as ApiMarketData;
  const [passedMarketData, setPassedMarketData] = useState<ApiMarketData | null>(null);
  const initialOption = searchParams.get('option') || null;
  const { connect } = useConnect();
  const connectors = useConnectors();
  // 将 useRef 移到组件顶层，以符合 React Hooks 规则
  const orderBookRequestSentRef = useRef(false);
  const priceHistoryRequestSentRef = useRef(false);
  const priceRefreshRef = useRef(false);
  // 为每个outcome创建一个ref追踪是否已获取数据
  const outcomeDataFetchedRef = useRef<Record<number, boolean>>({});
  
  // Add favorite state
  const [isFavorite, setIsFavorite] = useState(false);
  
  // Add new state for market data
  // const [apiMarketData, setApiMarketData] = useState<ApiMarketData | null>(null);
  
  // 将API数据转换为UI格式
  // Convert API data to UI format if available
  const convertApiDataToUiFormat = (apiData: ApiMarketData): MarketData => {
    // Convert conditions to outcomes format
    const outcomes = apiData.conditions.map((condition, index) => ({
      id: index + 1,
      name: condition.description,
      chance: apiData.chance?.toString() + "%",
      yesPrice: condition.yes_coin_price?.toString() + "¢",
      noPrice: condition.no_coin_price?.toString() + "¢",
      result:condition.result,
    }));
    
    // Format the date from timestamp with proper validation
    let formattedDate = '';
    try {
      if (apiData.stop_time && !isNaN(apiData.stop_time)) {
        // Check if timestamp is in seconds or milliseconds
        const timestamp = apiData.stop_time.toString().length === 10 
          ? apiData.stop_time * 1000 
          : apiData.stop_time;
        const date = new Date(timestamp);
        if (!isNaN(date.getTime())) {
          formattedDate = date.toLocaleString();
        }
      }
    } catch (error) {
      console.warn('Invalid date format:', apiData.stop_time);
    }
    
    // Fallback to default date if conversion fails
    if (!formattedDate) {
      formattedDate = "Jun 30, 2025 00:00:00";
    }
    
    return {
      id: parseInt(apiData.id) || 1,
      title: apiData.description,
      date: formattedDate,
      outcomes: outcomes,
      rules: [apiData.rules],
      comments: marketDetail.comments,
      vol: apiData.vol || 0 // Ensure vol has a fallback value
    };
  };
  
  // Use passed market data if available, otherwise use the sample data
  const [marketData, setMarketData] = useState<MarketData>({} as MarketData);
  const [isMarketDataLoading, setIsMarketDataLoading] = useState(true);
  const { betMessageContract,betTokenContract,address,vaultContract} = useUser();
  const [activeTab, setActiveTab] = useState('order-book');
  const [selectedOutcome, setSelectedOutcome] = useState<MarketOutcome>(() => {
    if (passedMarketData) {
      const outcomes = convertApiDataToUiFormat(passedMarketData).outcomes;
      return outcomes.length > 0 ? outcomes[0] : marketDetail.outcomes[0];
    }
    return marketDetail.outcomes[0];
  });
  
  const [buyOrSell, setBuyOrSell] = useState(initialOption === 'no' ? 'sell' : 'buy');
  const [selectedYesNo, setSelectedYesNo] = useState(initialOption === 'no' ? 'No' : 'Yes');
  const [price, setPrice] = useState(() => {
    const initialPrice = selectedYesNo === 'Yes' ? selectedOutcome.yesPrice : selectedOutcome.noPrice;
    return initialPrice;
  });
  const [shares, setShares] = useState('0');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [marketPrice, setMarketPrice] = useState<{ [key: string]: number }>({
    yes: 0,
    no: 0,
  })
  const [marketPriceActive, setMarketPriceActive] = useState(false);
 
  const [tradeLoading, setTradeLoading] = useState(false)
  // 跟踪每个outcome的OrderBook展开状态
  const [expandedOrderBooks, setExpandedOrderBooks] = useState<number[]>([]);
  
  // 添加移动设备检测和交易界面弹窗状态
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const [tradeModalVisible, setTradeModalVisible] = useState(false);
  
  // Add state for chart data
  const [chartData, setChartData] = useState<number[]>([10, 15, 20, 18, 25, 28, 22, 35, 38, 40, 42]);
  const [chartDates, setChartDates] = useState<string[]>(['08-01', '08-02', '08-03', '08-04', '08-05', 
                                         '08-06', '08-07', '08-08', '08-09', '08-10', '08-11']);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState<string | null>(null);
  
  // Replace the single orderBookData with a map of outcome-specific data
  const [orderBookDataMap, setOrderBookDataMap] = useState<Record<number, OutcomeOrderBookData>>({});
  const [vaultBalance, setVaultBalance] = useState(0)
  const [betBalance, setBetBalance] = useState(0)
  const [messageApi, contextHolder] = message.useMessage();
  // Still keep these for loading states, but they'll be used per outcome
  // const [orderBookLoading, setOrderBookLoading] = useState(false);
  // const [orderBookError, setOrderBookError] = useState<string | null>(null);
  
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';

  const showMessage = (msg: String, type: 'success' | 'error' | 'warning' ) => {
    messageApi.open({
      type: type,
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  
  // 保留原有方法，但使用通用方法实现
  const errorMsg = (msg: String) => showMessage(msg, 'error');
  // const warningMsg = (msg: String) => showMessage(msg, 'warning');
  const successMsg = (msg: String) => showMessage(msg, 'success');
  const { address: socialAddress, provider } = useEthereum();
  // 在组件初始化时设置 message 的全局配置
useEffect(() => {
  // 设置消息出现的位置和样式
  message.config({
    top: 80,         // 距离顶部的距离，确保在 Header 下方
    duration: 3,     // 显示时间，单位秒
    maxCount: 3,     // 最大显示数量
    rtl: false,      // 从右到左展示
    getContainer: () => document.body  // 确保消息挂载在 body 上
  });
}, []);

  // Add useEffect to fetch market data
  useEffect(() => {
    const fetchMarketData = async () => {
      if (!marketId) return;
      
      setIsMarketDataLoading(true);
      try {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/game?id=${marketId}&lang=${i18n.language}`);
        if (!response.ok) {
          throw new Error(`API responded with status: ${response.status}`);
        }
        
        const data = await response.json();
        if (data && data.code === 200 && data.data) {
          
          if (marketId) {
            const conditionsUrl = `${import.meta.env.VITE_API_URL}/v1/game/conditions/released?id=${marketId}`;
            const conditionsResponse = await fetch(conditionsUrl);
            
            if (!conditionsResponse.ok) {
              throw new Error(`Conditions API responded with status: ${conditionsResponse.status}`);
            }
            
            const conditionsData = await conditionsResponse.json();
            
            if (conditionsData && conditionsData.code === 200 && Array.isArray(conditionsData.data)) {
              // 合并游戏数据和条件数据
              const mergedData = conditionsData.data;
              data.data.conditions = mergedData;
              // 更新显示数据
              setPassedMarketData(data.data);
            } else {
              data.data.conditions = [];
              data.data.isGameCondition=false;
              data.data.chance=0;
              data.data.yesBuyPrice=0;
              // setDisplayedData(prev => [...prev, ...mergedData]);
              setPassedMarketData(data.data);
             } // setHasMore(false);
             const formattedData = convertApiDataToUiFormat(data.data);
            setMarketData(formattedData);
            if (formattedData.outcomes.length > 0) {
              setSelectedOutcome(formattedData.outcomes[0]);
              const newPrice = selectedYesNo === 'Yes' ? formattedData.outcomes[0].yesPrice : formattedData.outcomes[0].noPrice;
              setPrice(newPrice);
            }
          }
          const url = `${import.meta.env.VITE_SECOND_BASE_URL}/volume/conditions/${data.data.cid}`;
          fetch(url)
            .then(res => res.json())
            .then(data => {
              if (data && data.code === 0 && data.data) {
                setOutcomeVolMap(data.data);
              }
            })
            .catch(() => {});
        }
      } catch (error) {
        console.error('获取市场数据失败:', error);
        errorMsg('Failed to fetch market data');
      } finally {
        setIsMarketDataLoading(false);
      }
    };

    // If we have passed market data, use it
    // if (passedMarketData) {
    //   // setApiMarketData(passedMarketData);
    //   const formattedData = convertApiDataToUiFormat(passedMarketData);
    //   setMarketData(formattedData);
    //   if (formattedData.outcomes.length > 0) {
    //     setSelectedOutcome(formattedData.outcomes[0]);
    //     const newPrice = selectedYesNo === 'Yes' ? formattedData.outcomes[0].yesPrice : formattedData.outcomes[0].noPrice;
    //     setPrice(newPrice);
    //   }
    // } else {
      // Otherwise fetch from API
      fetchMarketData();
    // }
  }, [marketId, i18n.language]);

  // useEffect(() => {
  //   // In a real app, fetch the market details using marketId if not passed
  //   console.log(`Fetching details for market ${marketId}`);
  //   console.log("activeTab:", activeTab);
    
  //   // If we have market data passed, use it
  //   if (passedMarketData) {
  //     console.log("Using passed market data:", passedMarketData);
  //     const formattedData = convertApiDataToUiFormat(passedMarketData);
  //     setMarketData(formattedData);
  //     if (formattedData.outcomes.length > 0) {
  //       setSelectedOutcome(formattedData.outcomes[0]);
  //       const newPrice = selectedYesNo === 'Yes' ? formattedData.outcomes[0].yesPrice : formattedData.outcomes[0].noPrice;
  //       setPrice(newPrice);
  //     }
  //   }
    
  //   // Use the option parameter to pre-select Yes or No
  //   if (initialOption === 'yes') {
  //     setSelectedYesNo('Yes');
  //     if (selectedOutcome) {
  //       setPrice(selectedOutcome.yesPrice);
  //     }
  //   } else if (initialOption === 'no') {
  //     setSelectedYesNo('No');
  //     if (selectedOutcome) {
  //       setPrice(selectedOutcome.noPrice);
  //     }
  //   }
  //   fetchVaultBalance();
  //   refreshMarketPrices();
  // }, [marketId, initialOption, passedMarketData]);

  // 处理连接钱包的函数
  // Handle wallet connection
  const handleConnectWallet = () => {
    setIsLoggedIn(true);
  };

  // 处理登出的函数
  // Handle logout
  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  // 关闭抽屉组件的函数
  // Close drawer component
  const onCloseDrawer = () => {
    setDrawerVisible(false);
  };

  // 选择市场结果的函数
  // Select market outcome
  const selectOutcome = (outcome: MarketOutcome, yesNoParam?: string) => {
    if (!outcome) return;
    const yesNoToUse = yesNoParam || selectedYesNo;
    setSelectedOutcome(outcome);
    setPrice(yesNoToUse === 'Yes' ? formatPrice(outcome.yesPrice) : formatPrice(outcome.noPrice));
    
    // Fetch order book data for the selected outcome
    if (passedMarketData) {
      try {
        // Find the corresponding condition for this outcome
        if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
          // First try to find condition by matching description to outcome name
          let condition = passedMarketData.conditions.find(c => 
            c && c.description === outcome.name
          );
          
          // If not found, try matching by ID
          if (!condition && typeof outcome.id === 'number') {
            condition = passedMarketData.conditions.find(c => 
              c && c.id === outcome.id.toString()
            );
          }
          
          // If still not found, use the first condition as fallback
          if (!condition && passedMarketData.conditions.length > 0) {
            condition = passedMarketData.conditions[0];
          }
          
          // Only fetch if we have a valid condition with a cid
          if (condition && condition.cid) {
            console.log(`Selecting outcome: ${outcome.name}, fetching order book for condition: ${condition.description} (${condition.cid})`);
            console.log("fetchOrderBookData4");
            fetchOrderBookData(outcome.id, condition.cid, yesNoToUse.toLowerCase());
          }
        }
      } catch (error) {
        console.error("Error in selectOutcome:", error);
      }
    }
  };

  // 切换买入/卖出状态的函数
  // Toggle between buy and sell states
  const toggleBuySell = (value: string) => {
    if (value === buyOrSell) return; // 如果没有实际变化，直接返回
    console.log("切换买/卖状态:", value);
    setBuyOrSell(value);
    // 价格切换后会通过useEffect触发refreshMarketPrices
    refreshMarketPrices();
  };

  // 选择是/否的函数
  // Select Yes or No option
  const selectYesNo = async (value: string) => {
    if (!selectedOutcome || value === selectedYesNo) return; // 如果没有实际变化，直接返回
    
    console.log("切换Yes/No状态:", value);
    setSelectedYesNo(value);
     // 添加这一行，确保在切换Yes/No时更新余额
   await  refreshMarketPrices()
  // 添加这一行，确保在切换买/卖状态时更新相应的余额
  if (value === 'sell') {
    fetchBetBalance();
  } else {
    fetchVaultBalance();
  }
    // 使用当前已有的价格数据更新显示价格，避免等待API
    setPrice(value === 'Yes' ? formatPrice(selectedOutcome.yesPrice) : formatPrice(selectedOutcome.noPrice));
    
    // Fetch order book data with the new yes/no selection
    if (passedMarketData) {
      try {
        // Find the corresponding condition for the selected outcome
        if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
          // First try to find condition by matching description to outcome name
          let condition = passedMarketData.conditions.find(c => 
            c && c.description === selectedOutcome.name
          );
          
          // If not found, try matching by ID
          if (!condition && typeof selectedOutcome.id === 'number') {
            condition = passedMarketData.conditions.find(c => 
              c && c.id === selectedOutcome.id.toString()
            );
          }
          
          // If still not found, use the first condition as fallback
          if (!condition && passedMarketData.conditions.length > 0) {
            condition = passedMarketData.conditions[0];
          }
          
          // Only fetch if we have a valid condition with a cid
          if (condition && condition.cid) {
            console.log(`Toggling Yes/No to ${value}, fetching order book for condition: ${condition.description} (${condition.cid})`);
            console.log("fetchOrderBookData5");
            fetchOrderBookData(selectedOutcome.id, condition.cid, value.toLowerCase());
          }
        }
      } catch (error) {
        console.error("Error in selectYesNo:", error);
      }
    }
  };

  // 计算总额的函数
  // Calculate total amount
  const calculateTotal = () => {
    // Extract numeric price by removing ¢ and converting to decimal
    const numericPrice = parseFloat(price.replace('¢', '')) / 100;
    const numericShares = parseFloat(shares) || 0;
    return (numericPrice * numericShares).toFixed(2);
  };

  // 计算潜在回报的函数 - 买入时潜在收益是股数，卖出时潜在收益是价格乘以股数
  // Calculate potential return - for buy, return is shares; for sell, return is price * shares
  const calculatePotentialReturn = () => {
    const numericShares = parseFloat(shares) || 0;
    const numericPrice = parseFloat(price.replace('¢', '')) / 100;
    
    // 根据买入/卖出状态计算不同的潜在收益
    const returnValue = buyOrSell === 'buy' 
      ? numericShares.toFixed(2) 
      : (numericPrice * numericShares).toFixed(2);
      
    return {
      value: returnValue,
      percentage: '0.00(0.00%)'
    };
  };

  // 切换评论回复显示状态的函数
  // 切换OrderBook展开/折叠状态的函数
  // Toggle OrderBook expand/collapse state
  const toggleOrderBook = (outcomeId: number) => {
    if (expandedOrderBooks.includes(outcomeId)) {
      // 如果当前已展开，则完全关闭
      setExpandedOrderBooks([]);
    } else {
      // 展开当前，关闭其他所有
      setExpandedOrderBooks([outcomeId]);
    }
  };

// 处理变化的函数
// Handle change
// const onChange =()=>{

// }

// 深度条组件，用于显示买卖深度，添加安全检查
// DepthBar component with safety checks
const DepthBar = ({ width, color, type }: { width: number; color: string; type: string }) => {
  // 确保 width 是有效数值且在合理范围内
  const safeWidth = !isNaN(width) && isFinite(width) ? Math.min(Math.max(width, 0), 100) : 0;
  
  return (
    <Tooltip title={type === 'ask' ? 'Ask' : 'Bids'} color={type === 'ask' ? 'red' : 'green'}>
      <div
        style={{
          backgroundColor: color,
          width: `${safeWidth}%`,
          height: 32,
          minWidth: '1px', // 确保即使宽度很小也能看到
        }}
      />
    </Tooltip>
  )
}

  // Function to fetch order book data for a specific outcome
  const fetchOrderBookData = async (outcomeId: number, conditionId: string, orderType: string = 'yes') => {
    if (!conditionId) {
      console.error("Cannot fetch order book: conditionId is missing");
      return;
    }
    
    // Check if the environment variable is defined
    const baseUrl = import.meta.env.VITE_SECOND_BASE_URL;
    if (!baseUrl) {
      console.error("VITE_SECOND_BASE_URL environment variable is not defined");
      
      // Update only this outcome's error state
      setOrderBookDataMap(prev => ({
        ...prev,
        [outcomeId]: {
          ...prev[outcomeId] || { asks: [], bids: [], isLoading: false, error: "API configuration error" },
          outcomeId,
          isLoading: false,
          error: "API configuration error"
        }
      }));
      
      return;
    }
    
    // Update loading state for this specific outcome
    setOrderBookDataMap(prev => ({
      ...prev,
      [outcomeId]: {
        ...prev[outcomeId] || { asks: [], bids: [], isLoading: true, error: null },
        outcomeId,
        isLoading: true,
        error: null
      }
    }));
    
    try {
      const orderTypeValue = orderType === 'yes' ? 0 : 1;
      const url = `${baseUrl}/depth/${conditionId}/${orderTypeValue}`;
      
      console.log(`Fetching order book data for outcome ${outcomeId} from: ${url}`);
      const response = await fetch(url);
      
      // if (!response.ok) {
      //   throw new Error(`API responded with status: ${response.status}`);
      // }
      
      const data: OrderBookApiResponse = await response.json();
      console.log("API response for outcome", outcomeId, ":", data);
      
      if (data && data.code === 0 && data.data) {
        // Add safety checks for asks and bids arrays
        const asks = Array.isArray(data.data.asks) ? data.data.asks : [];
        const bids = Array.isArray(data.data.bids) ? data.data.bids : [];
        
        // Safe accessor function for object properties
        const safeGet = (obj: any, key: string, defaultValue: any = '') => {
          return obj && typeof obj === 'object' && key in obj ? obj[key] : defaultValue;
        };
        
        // Convert API data format to UI format with additional safety checks
        const convertedData = {
          outcomeId,
          asks: asks.map(ask => ({
            price: `${safeGet(ask, 'price', '0')}¢`,
            shares: safeGet(ask, 'quantity', '0'),
            total: `$${(Number(safeGet(ask, 'tvl', 0)) / 100)}`
          })),
          bids: bids.map(bid => ({
            price: `${safeGet(bid, 'price', '0')}¢`,
            shares: safeGet(bid, 'quantity', '0'),
            total: `$${(Number(safeGet(bid, 'tvl', 0)) / 100)}`
          })),
          isLoading: false,
          error: null
        };
        
        // Update only this outcome's data in the map
        setOrderBookDataMap(prev => ({
          ...prev,
          [outcomeId]: convertedData
        }));
        
        console.log("Order book data loaded successfully for outcome", outcomeId, ":", convertedData);
      } else {
        console.error("Invalid API response format for outcome", outcomeId, ":", data);
        
        // Update only this outcome's error state
        setOrderBookDataMap(prev => ({
          ...prev,
          [outcomeId]: {
            ...prev[outcomeId] || { asks: [], bids: [] },
            outcomeId,
            isLoading: false,
            error: `Invalid API response: ${data?.message || 'Unknown error'}`
          }
        }));
      }
    } catch (error) {
      console.error("Error fetching order book data for outcome", outcomeId, ":", error);
      
      // Update only this outcome's error state
      setOrderBookDataMap(prev => ({
        ...prev,
        [outcomeId]: {
          ...prev[outcomeId] || { asks: [], bids: [] },
          outcomeId,
          isLoading: false,
          error: error instanceof Error ? error.message : "Failed to load order book data"
        }
      }));
    }
  };
  
  // Update order book when selected outcome or buy/sell option changes
  useEffect(() => {
    // Safety check to make sure we have selected outcome and market data
    if (!selectedOutcome || !passedMarketData) return;
    
    // 使用已经定义的 ref
    if (orderBookRequestSentRef.current) return;
    
    orderBookRequestSentRef.current = true;
    
    try {
      // Find the corresponding condition for the selected outcome
      if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
        // First try to find condition by matching description to outcome name
        let condition = passedMarketData.conditions.find(c => 
          c && c.description === selectedOutcome.name
        );
        
        // If not found, try matching by ID
        if (!condition && typeof selectedOutcome.id === 'number') {
          condition = passedMarketData.conditions.find(c => 
            c && c.id === selectedOutcome.id.toString()
          );
        }
        
        // If still not found, use the first condition as fallback
        if (!condition && passedMarketData.conditions.length > 0) {
          condition = passedMarketData.conditions[0];
        }
        
        // Only fetch if we have a valid condition with a cid
        if (condition && condition.cid) {
          console.log(`Fetching order book for condition: ${condition.description} (${condition.cid})`);
          // 加入防抖处理，避免短时间内重复请求
          const timer = setTimeout(() => {
            console.log("fetchOrderBookData6");
            fetchOrderBookData(selectedOutcome.id, condition.cid, selectedYesNo.toLowerCase());
          }, 300);
          
          return () => {
            clearTimeout(timer);
          };
        } else {
          console.warn("No valid condition found for selected outcome:", selectedOutcome);
        }
      }
    } catch (error) {
      console.error("Error in useEffect for order book data:", error);
    }
    
    // 清理函数
    return () => {
      orderBookRequestSentRef.current = false;
    };
  }, [selectedOutcome?.id, selectedYesNo]); // 减少依赖项，只在真正需要时更新

  // Function to fetch price history data for the chart
  const fetchPriceHistory = async (conditionId: string) => {
    if (!conditionId) {
      console.error("Cannot fetch price history: conditionId is missing");
      return;
    }
    
    // Check if the environment variable is defined
    const baseUrl = import.meta.env.VITE_THIRD_BASE_URL;
    if (!baseUrl) {
      console.error("VITE_THIRD_BASE_URL environment variable is not defined");
      setChartError("API configuration error");
      return;
    }
    
    setChartLoading(true);
    setChartError(null);
    
    try {
      const url = `${baseUrl}/v1/market/history/${conditionId}`;
      
      console.log(`Fetching price history data from: ${url}`);
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }
      
      const responseData: PriceHistoryResponse = await response.json();
      console.log("Price history API response:", responseData);
      
      // Check if we have valid data
      if (responseData && responseData.status === "success" && Array.isArray(responseData.data) && responseData.data.length > 0) {
        // Sort data by timestamp if available
        const sortedData = [...responseData.data];
        if (sortedData[0].start_time) {
          sortedData.sort((a, b) => {
            return (a.start_time || 0) - (b.start_time || 0);
          });
        }
        
        // Extract close prices for the chart data
        const priceData = sortedData.map(item => {
          // Use close_price or fallback to other price values
          return item.close_price || item.open_price || item.high_price || 0;
        });
        
        // Generate date labels
        const dateLabels = sortedData.map((item, index) => {
          // If we have a timestamp, use it
          if (item.start_time) {
            // Convert timestamp to date string (MM-DD format)
            const date = new Date(item.start_time);
            return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
          }
          
          // Fallback: use index-based dates
          const today = new Date();
          const date = new Date(today);
          date.setDate(today.getDate() - (sortedData.length - index - 1));
          return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
        });
        
        // Handle empty or invalid data
        if (priceData.length === 0 || priceData.every(price => price === 0)) {
          console.warn("No valid price data found, using fallback data");
          setChartError("No price history available");
          return;
        }
        
        // Update state with the new data
        setChartData(priceData);
        setChartDates(dateLabels);
        console.log("Chart data updated:", { priceData, dateLabels });
      } else {
        // Use mock data as fallback
        console.warn("Invalid price history data, using fallback data");
        setChartError(t('OrderDetail.noData'));
      }
    } catch (error) {
      console.error("Error fetching price history data:", error);
      setChartError(error instanceof Error ? error.message : "Failed to load price history");
    } finally {
      setChartLoading(false);
    }
  };
  
  // Update price history when selected outcome changes
  useEffect(() => {
    if (!selectedOutcome || !passedMarketData) return;
    
    // 使用已经定义的 ref
    if (priceHistoryRequestSentRef.current) return;
    
    priceHistoryRequestSentRef.current = true;
    
    try {
      // Find the corresponding condition for the selected outcome
      if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
        // First try to find condition by matching description to outcome name
        let condition = passedMarketData.conditions.find(c => 
          c && c.description === selectedOutcome.name
        );
        
        // If not found, try matching by ID
        if (!condition && typeof selectedOutcome.id === 'number') {
          condition = passedMarketData.conditions.find(c => 
            c && c.id === selectedOutcome.id.toString()
          );
        }
        
        // If still not found, use the first condition as fallback
        if (!condition && passedMarketData.conditions.length > 0) {
          condition = passedMarketData.conditions[0];
        }
        
        // Only fetch if we have a valid condition with a cid
        if (condition && condition.cid) {
          console.log(`Fetching price history for condition: ${condition.description} (${condition.cid})`);
          fetchPriceHistory(condition.cid);
        } else {
          console.warn("No valid condition found for selected outcome:", selectedOutcome);
        }
      }
    } catch (error) {
      console.error("Error in useEffect for price history:", error);
    }
    
    // 清理函数
    return () => {
      priceHistoryRequestSentRef.current = false;
    };
  }, [selectedOutcome?.id]); // 只依赖 ID 减少不必要的请求

  // Handle tab change to fetch data when switching to graph tab
  const handleTabChange = (activeKey: string, outcome?: MarketOutcome) => {
    setActiveTab(activeKey);
    
    // If switching to graph tab, fetch price history if not already loaded
    if (activeKey === 'graph' && !chartLoading) {
      const targetOutcome = outcome || selectedOutcome;
      if (!targetOutcome || !passedMarketData) return;
      
      try {
        // Find the corresponding condition for the selected outcome
        if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
          let condition = passedMarketData.conditions.find(c => 
            c && c.description === targetOutcome.name
          );
          
          if (!condition && typeof targetOutcome.id === 'number') {
            condition = passedMarketData.conditions.find(c => 
              c && c.id === targetOutcome.id.toString()
            );
          }
          
          if (!condition && passedMarketData.conditions.length > 0) {
            condition = passedMarketData.conditions[0];
          }
          
          if (condition && condition.cid) {
            console.log(`Tab changed to graph, fetching price history for: ${condition.cid}`);
            fetchPriceHistory(condition.cid);
          }
        }
      } catch (error) {
        console.error("Error fetching price history on tab change:", error);
      }
    }
  };
  
  // Render the regular order book for the selected outcome (used in trading interface)
  // const renderOrderBook = () => { 
  //   const type = 'ask';
  //   const barColor = type === 'ask' ? '#********' : '#25AE6020';
  //   const barColor1 = type !== 'ask' ? '#********' : '#25AE6020';
    
  //   // Get the order book data for the selected outcome
  //   const outcomeData = selectedOutcome ? orderBookDataMap[selectedOutcome.id] : null;
  //   const isLoading = outcomeData?.isLoading || orderBookLoading;
  //   const error = outcomeData?.error || orderBookError;
    
  //   return(
  //     <div className="order-book-container" style={{marginLeft: '16px'}}>
  //       <Tabs defaultActiveKey="order-book" onChange={(key) => handleTabChange(key)} className={`market-tabs ${themeClass}`}>
  //         <TabPane tab={t('OrderDetail.orderBook')} key="order-book">
  //           <div className="order-book-header">
  //             <div className="trade-header">{t('default.tradeYes')}</div>
  //             <div className="price-header">{t('OrderDetail.price')}</div>
  //             <div className="shares-header">{t('OrderDetail.shares')}</div>
  //             <div className="total-header">{t('myOrders.total')}</div>
  //           </div>
            
  //           {isLoading ? (
  //             <div style={{ textAlign: 'center', padding: '20px 0' }}>
  //               <Spin size="small" />
  //               <div style={{ marginTop: 10 }}>Loading order book...</div>
  //             </div>
  //           ) : error ? (
  //             <div style={{ textAlign: 'center', padding: '20px 0', color: '#ff4d4f' }}>
  //               {error}
  //               <div style={{ marginTop: 10 }}>
  //                 <Button size="small" onClick={() => {
  //                   if (selectedOutcome && passedMarketData?.conditions?.length > 0) {
  //                     const condition = passedMarketData.conditions.find(c => 
  //                       c && (c.description === selectedOutcome.name || c.id === selectedOutcome.id.toString())
  //                     ) || passedMarketData.conditions[0];
                      
  //                     if (condition?.cid) {
  //                       fetchOrderBookData(selectedOutcome.id, condition.cid, selectedYesNo.toLowerCase());
  //                     }
  //                   }
  //                 }}>
  //                   Retry
  //                 </Button>
  //               </div>
  //             </div>
  //           ) : (
  //             <>
  //               <div className="asks-section">
  //                 {outcomeData && outcomeData.asks && outcomeData.asks.length > 0 ? 
  //                   outcomeData.asks.map((ask, index) => (
  //                     <div key={`ask-${index}`} className="order-row ask-row">
  //                       <div className="trade-col">
  //                         <DepthBar type={"ask"} width={Number(100)} color={barColor} />
  //                       </div>
  //                       <div className="price-col">{ask.price}</div>
  //                       <div className="shares-col">{ask.shares}</div>
  //                       <div className="total-col">{ask.total}</div>
  //                     </div>
  //                   )) : 
  //                   defaultOrderBookData.asks.map((ask, index) => (
  //                     <div key={`ask-${index}`} className="order-row ask-row">
  //                       <div className="trade-col">
  //                         <DepthBar type={"ask"} width={Number(100)} color={barColor} />
  //                       </div>
  //                       <div className="price-col">{ask.price}</div>
  //                       <div className="shares-col">{ask.shares}</div>
  //                       <div className="total-col">{ask.total}</div>
  //                     </div>
  //                   ))
  //                 }
  //               </div>
                
  //               <div className="bids-section">
  //                 {outcomeData && outcomeData.bids && outcomeData.bids.length > 0 ? 
  //                   outcomeData.bids.map((bid, index) => (
  //                     <div key={`bid-${index}`} className="order-row bid-row">
  //                       <div className="trade-col">
  //                         <DepthBar type={"Bids"} width={Number(100)} color={barColor1} />
  //                       </div>
  //                       <div className="price-col">{bid.price}</div>
  //                       <div className="shares-col">{bid.shares}</div>
  //                       <div className="total-col">{bid.total}</div>
  //                     </div>
  //                   )) :
  //                   defaultOrderBookData.bids.map((bid, index) => (
  //                     <div key={`bid-${index}`} className="order-row bid-row">
  //                       <div className="trade-col">
  //                         <DepthBar type={"Bids"} width={Number(100)} color={barColor1} />
  //                       </div>
  //                       <div className="price-col">{bid.price}</div>
  //                       <div className="shares-col">{bid.shares}</div>
  //                       <div className="total-col">{bid.total}</div>
  //                     </div>
  //                   ))
  //                 }
  //               </div>
  //             </>
  //           )}
  //         </TabPane>
  //         <TabPane tab={t('myOrders.graph')} key="graph">
  //           <div className="chart-container" style={{ 
  //             height: '300px', 
  //             margin: '20px 0',
  //             backgroundColor: 'transparent' 
  //           }}>
  //             {chartLoading ? (
  //               <div style={{ textAlign: 'center', padding: '20px 0', height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
  //                 <Spin size="large" />
  //                 <div style={{ marginTop: 10 }}>Loading price history...</div>
  //               </div>
  //             ) : chartError ? (
  //               <div style={{ textAlign: 'center', padding: '20px 0', color: '#ff4d4f', height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
  //                 {chartError}
  //                 <div style={{ marginTop: 10 }}>
  //                   <Button size="small" onClick={() => {
  //                     if (passedMarketData?.conditions?.length > 0) {
  //                       const condition = passedMarketData.conditions.find(c => 
  //                         c && (c.description === selectedOutcome.name || c.id === selectedOutcome.id.toString())
  //                       ) || passedMarketData.conditions[0];
                        
  //                       if (condition?.cid) {
  //                         fetchPriceHistory(condition.cid);
  //                       }
  //                     }
  //                   }}>
  //                     Retry
  //                   </Button>
  //                 </div>
  //               </div>
  //             ) : (
  //               <LineChart 
  //                 data={chartData} 
  //                 xAxisData={chartDates} 
  //                 theme={theme.name === 'dark' ? 'dark' : 'light'} 
  //                 showSymbol={chartData.length < 30} // Only show symbols if we have fewer data points
  //               />
  //             )}
  //           </div>
  //         </TabPane>
  //       </Tabs>
  //     </div>
  //   );
  // };

  // 格式化价格的辅助函数
  // Format price helper
  const formatPrice = (price: number | string | undefined): string => {
    if (price === undefined || price === null) return "0¢";
    if (typeof price === 'number') return `${price}¢`;
    return price.toString().includes('¢') ? price.toString() : `${price}¢`;
  };

  // 处理价格减少的函数
  // Handle price decrease
  const handlePriceDecrease = () => {
    setMarketPriceActive(false);
    const numericPrice = parseFloat(price.replace('¢', ''));
    const newPrice = Math.max(1, numericPrice - 1) + '¢';
    setPrice(newPrice);
  };

  // 处理价格增加的函数
  // Handle price increase
  const handlePriceIncrease = () => {
    setMarketPriceActive(false);
    const numericPrice = parseFloat(price.replace('¢', ''));
    const newPrice = (numericPrice + 1) + '¢';
    setPrice(newPrice);
  };

  // 处理价格变化的函数
  // Handle price change
  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMarketPriceActive(false);
    let value = e.target.value;
    // Make sure the value always ends with ¢
    if (!value.endsWith('¢') && value !== '') {
      value = value.replace(/¢/g, '') + '¢';
    }
    setPrice(value);
  };

  // 处理份额减少的函数
  // Handle shares decrease
  const handleSharesDecrease = () => {
    const numericShares = parseFloat(shares) || 0;
    const newShares = Math.max(0, numericShares - 1).toString();
    setShares(newShares);
  };

  // 处理份额增加的函数
  // Handle shares increase
  const handleSharesIncrease = () => {
    const numericShares = parseFloat(shares) || 0;
    const newShares = (numericShares + 1).toString();
    setShares(newShares);
  };

  // 处理份额变化的函数
  // Handle shares change
  const handleSharesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow numeric input
    const value = e.target.value.replace(/[^0-9]/g, '');
    setShares(value);
  };

  // Add useEffect to refresh market prices when options change
  useEffect(() => {
    // 使用组件顶层的ref，而不是在useEffect内创建
    
    if (selectedOutcome && passedMarketData && !priceRefreshRef.current) {
      priceRefreshRef.current = true;
      console.log("触发价格刷新，原因：", {selectedOutcome: selectedOutcome.id, buyOrSell, selectedYesNo});
      refreshMarketPrices(false); // 传入false表示不强制更新selectedOutcome
      
      // 设置延时，防止短时间内重复刷新
      const timer = setTimeout(() => {
        priceRefreshRef.current = false;
      }, 3000); // 3秒内不重复刷新
      
      return () => {
        clearTimeout(timer);
      };
    }
  }, [buyOrSell, selectedYesNo]); // 移除selectedOutcome避免循环依赖，只在买卖状态切换时刷新

  // 刷新市场价格的函数
  // Refresh market prices
  const refreshMarketPrices = async (forceUpdateOutcome: boolean = true) => {
      let condition = passedMarketData?.conditions.find(c => 
        c && c.description === selectedOutcome.name
      );
      if (!condition) {
        errorMsg('❌ condition is not found')
        return
      }
      if (condition?.cid) {
        try {
          //  url: `${import.meta.env.VITE_SECOND_BASE_URL}/marketPrices/${id}`,
          const marketPricesHelper = await getOrderBookMarketPrices(condition.cid);
          const result = await marketPricesHelper.unwrap();
          if (result) {

            const yesBuyPrice = Math.floor(parseFloat(ethers.formatUnits((result.yesBuyPrice).toString(), 16))) + "¢"
            const yesSellPrice = Math.floor(parseFloat(ethers.formatUnits((result.yesSellPrice).toString(), 16))) + "¢"
            const noBuyPrice = Math.floor(parseFloat(ethers.formatUnits((result.noBuyPrice).toString(), 16))) + "¢"
            const noSellPrice = Math.floor(parseFloat(ethers.formatUnits((result.noSellPrice).toString(), 16))) + "¢"

            // 更新市场价格状态
            setMarketPrice({
              yes: buyOrSell === 'buy' ? Number(yesBuyPrice) || 0 : Number(yesSellPrice) || 0,
              no: buyOrSell === 'buy' ? Number(noBuyPrice) || 0 : Number(noSellPrice) || 0,
            })
            
            // 更新当前显示的价格
            if (selectedYesNo === 'Yes') {
              setPrice(yesBuyPrice);
            } else {
              setPrice(noBuyPrice);
            }
            
            // 只有在需要时才更新selectedOutcome，避免不必要的重渲染
            if (forceUpdateOutcome) {
              // 更新selectedOutcome中的价格
              const updatedOutcome = {
                ...selectedOutcome,
                yesPrice: yesBuyPrice,
                noPrice: noBuyPrice
              };
              setSelectedOutcome(updatedOutcome);
              
              // 更新marketData中的outcomes
              setMarketData(prevData => {
                const updatedOutcomes = prevData.outcomes.map(outcome => {
                  if (outcome.id === selectedOutcome.id) {
                    return updatedOutcome;
                  }
                  return outcome;
                });
                return {
                  ...prevData,
                  outcomes: updatedOutcomes
                };
              });
            }
          }
        } catch (error) {
          console.error('Failed to fetch market prices:', error)
        }
      }
    }
  // 生成唯一订单ID的函数
  // Generate unique order ID
  const generateUniqueOrderId = (baseId: bigint, isYes: boolean) => {
      // 获取当前时间戳（毫秒）
      const timestamp = BigInt(Date.now());
      // 生成6位随机数
      const random = BigInt(Math.floor(Math.random() * 1000000));
      
      // 组合ID: baseId + timestamp + random + flag(yes=1/no=0)
      const uniqueId = (baseId * 1000000000000000n) + // 基础ID
                      (timestamp * 1000000n) +         // 时间戳
                      random +                         // 随机数
                      (isYes ? 1n : 0n);              // yes/no标志位
      
      return uniqueId.toString(16).padStart(16, '0');
    };

  // 获取限价的函数
  // Get limit price
  const getLimitPrice = () => {
      let limitPrice = 0
      if (buyOrSell === 'buy') {
        if (selectedYesNo === 'yes') {
          limitPrice = marketPrice.yes || 0
        } else  limitPrice = marketPrice.no || 0
      } else {
        if (selectedYesNo === 'yes') {
          limitPrice = marketPrice.yes || 0
        } else limitPrice = marketPrice.no || 0
      }
  
      return limitPrice
    }
 

  const checkWallet = async () => {
    if(address==null){
      const particleConnector = connectors[1];
      // 提供一个空对象作为参数，使其显示所有连接选项
      await connect({connector:particleConnector});
      return;
    }
  }

  const createTradeOrder = async () => {
    // console.log('address',address);
    if (address == null) {
      console.log("address", address);
      checkWallet();
    }

    if ((Number(price) * Number(shares)) / 100 <= 0) {
      return errorMsg("Price greater than zero");
    }

    setTradeLoading(true);
    //  获取 singer provider
    console.warn("provider", provider, socialAddress);
    
    // const txhash = await sendTransaction({
    //   to: "******************************************",
    //   value: 1,
    // });
    // console.warn("txhash", txhash);
    try {
      if (!betMessageContract) {
        errorMsg("❌ betManager is not initialized");
        return;
      }
      let condition = passedMarketData?.conditions.find(
        (c) => c && c.description === selectedOutcome.name
      );
      if (!condition) {
        errorMsg("❌ condition is not found");
        return;
      }

      const baseOrderId = BigInt(condition.cid) * 10n + 1n;
      const yesOrderId = generateUniqueOrderId(baseOrderId, true);
      const noOrderId = generateUniqueOrderId(baseOrderId, false);

      const gameId = condition.cid.substring(0, 10);
      const conditionId = condition.cid;
      const marketSide = selectedYesNo === "Yes" ? 0 : 1;
      const amount = shares;
      const numericPrice = parseFloat(price.replace("¢", ""));
      const priceLimit = ethers.parseUnits(numericPrice.toString(), 16); //0.7  70
      const side = buyOrSell === "buy" ? 1 : 0;
      const orderId = selectedYesNo === "Yes" ? yesOrderId : noOrderId;

      // 先进行授权
      // const requiredAllowance = ethers.parseUnits((parseInt(amount.toString())*2).toString(), 18);
      const tokenAddress = import.meta.env.VITE_BET_MANAGER;
      // const gasConfig = {
      //   maxFeePerGas: ethers.parseUnits("150", "gwei"),
      //   maxPriorityFeePerGas: ethers.parseUnits("100", "gwei"),
      //   gasLimit: 3000000,
      // };
      if (betTokenContract) {
        try {
          // 检查当前授权额度
          const currentAllowance = await betTokenContract.allowance(address, tokenAddress);
          if(currentAllowance<ethers.parseUnits("10000000000", 18)){
            console.log('当前授权额度:', currentAllowance);
            // console.log('需要授权的额度:', requiredAllowance);
            // if (currentAllowance > requiredAllowance) {
            const AllowanceNUM = ethers.parseUnits("100000000000", 18);
            const tx1 = await betTokenContract.approve(
              tokenAddress,
              AllowanceNUM,
              // gasConfig
            );
            await tx1.wait();
            console.log("✅ betToken approved!");
            // const AllowanceNUM = ethers.parseUnits("100000000000", 18);
            const tx2 = await vaultContract?.approve(
              tokenAddress,
              AllowanceNUM,
              // gasConfig
            );
            await tx2.wait();
            console.log("✅ vaultContract approved!");
          }
          // } else {
          //   console.log('当前授权额度足够，无需重新授权');
          // }
        } catch (error) {
          console.error("授权失败:", error);
          throw new Error("Token approve failed");
        }
      }

      const num = await vaultContract?.getFrozenAmount(address);
      console.log("❌ num冻结金额:", num);
      // 执行交易
      const tx = await betMessageContract.createTradeOrder(
        gameId,
        conditionId,
        marketSide,
        amount,
        priceLimit,
        side,
        orderId,
        Date.now(),
        // gasConfig
      );
      await tx.wait();
      console.log("订单创建成功");

      const olderNum = await vaultContract?.getFrozenAmount(address);
      console.log("❌ olderNum冻结金额:", olderNum);

      console.log("✅ Trade order created!");
      successMsg("Trade order created!");
      // setValues({
      //   limit: getLimitPrice(),
      //   shares: 0
      // })
      setShares("0");
      setPrice(getLimitPrice().toString());
      refreshMarketPrices(true); // 在交易后强制更新价格和显示
      // fetchOrderBookData(selectedOutcome.id, condition.cid, selectedYesNo.toLowerCase());
      // fetchPriceHistory(condition.cid);
    } catch (error: any) {
      console.error("❌ Failed to create trade order:", error);
      const reason =
        error?.reason || error?.revert?.args?.[0] || "Something went wrong";
      errorMsg(`❌ Trade failed: ${reason}`);
    } finally {
      setTradeLoading(false);
    }
  }
   // 获取钱包余额
   const fetchVaultBalance = async () => {
    try {
      if (vaultContract && address) {
        const balance = await vaultContract.balanceOf(address)
        setVaultBalance(Number(balance.toString()) / 1000000000000000000)
      }
    } catch (error) {
      console.error('获取钱包余额失败:', error)
    }
  }

  // 获取已购买的数量
  const fetchBetBalance = async () => {
    try {
      if(!passedMarketData) return;
      let condition = passedMarketData.conditions.find(c => 
        c && c.description === selectedOutcome.name
      );
      if (!condition) {
        errorMsg('❌ condition is not found')
        return
      }
      // console.log('--------->',selectedYesNo);
      // console.log('--------->',betTokenContract);
      // console.log('--------->',address);
      // console.log('--------->',condition?.cid);
      if (betTokenContract && address && condition?.cid) {
        const gameId = condition.cid.substring(0, 10)
        const betAmount = await betTokenContract.getUserTotalBet(
          address,
          gameId,
          condition.cid,
          selectedYesNo === 'Yes' ? 0 : 1
        )
        // console.log('-------=========-->',betAmount);
        setBetBalance(Number(betAmount.toString()))
      }
    } catch (error) {
      console.error('获取下注数量失败:', error)
    }
  }
    // 在组件加载和相关状态变化时获取余额
    useEffect(() => {
      console.log("----->1111");
      if (buyOrSell === 'buy') {
        fetchVaultBalance()
      } else {
        fetchBetBalance()
      }
    }, [buyOrSell, selectedYesNo, address, passedMarketData])

  //交易模块
  const renderTradingInterface = () => (
    <div className={`trading-interface ${themeClass}`}>
      <div className="selected-outcome">
        <Avatar src={userAvatarImage} size={40} />
        <Title level={5} style={{marginLeft: "10px", marginBottom: "20px"}}>
          {selectedOutcome?.name || "Select an outcome"}
        </Title>
      </div>
      
      <div className="buy-sell-tabs">
        <div 
          className={`tab-item ${buyOrSell === 'buy' ? 'active' : ''}`}
          onClick={() => toggleBuySell('buy')}
          style={{
            cursor: 'pointer',
            color: buyOrSell === 'buy' ? '#2C9CDC' : '#BFBFBF',
            borderBottom: buyOrSell === 'buy' ? '2px solid #2C9CDC' : 'none',
            padding: '0px 20px',
            fontWeight: 500,
            fontSize: '13px',
            transition: 'all 0.3s ease'
          }}
        >
          {t('OrderDetail.buy')}
        </div>
        <div 
          className={`tab-item ${buyOrSell === 'sell' ? 'active' : ''}`}
          onClick={() => toggleBuySell('sell')}
          style={{
            cursor: 'pointer',
            color: buyOrSell === 'sell' ? '#2C9CDC' : '#BFBFBF',
            borderBottom: buyOrSell === 'sell' ? '2px solid #2C9CDC' : 'none',
            padding: '0px 20px',
            fontWeight: 500,
            fontSize: '13px',
            transition: 'all 0.3s ease'
          }}
        >
          {t('OrderDetail.sell')}
        </div>
        <div className={`tab-item ${buyOrSell === 'sell' ? 'active' : ''}`} />
        <div className={`tab-item ${buyOrSell === 'sell' ? 'active' : ''}`} />
        <div className={`tab-item ${buyOrSell === 'sell' ? 'active' : ''}`} />
        <div className={`tab-item ${buyOrSell === 'sell' ? 'active' : ''}`} />
        <div className={`tab-item ${buyOrSell === 'sell' ? 'active' : ''}`} />
      </div>
      
      <div className="outcome-selection">
        <Text>{t('myOrders.outcome')}</Text>
        <div className="outcome-buttons">
          <Button 
            className={`yes-button ${selectedYesNo === 'Yes' ? 'active' : ''}`}
            onClick={() => selectYesNo('Yes')}
          >
            {t('default.yes')} {formatPrice(selectedOutcome?.yesPrice)}
          </Button>
          <Button 
            className={`no-button ${selectedYesNo === 'No' ? 'active' : ''}`}
            onClick={() => selectYesNo('No')}
          >
            {t('default.no')} {formatPrice(selectedOutcome?.noPrice)}
          </Button>
        </div>
      </div>
      
      <div className="limit-price">
        <div className="price-label">
          <Text>{t('myOrders.limitPrice')}</Text>
          <Text className="balance">
          {buyOrSell === 'buy' 
                ? `${t('dashboard.balance')}: ${parseFloat(vaultBalance.toString()).toFixed(2)}` 
                : `${selectedYesNo} ${t('default.amount')}: ${betBalance}`
              }

          </Text>
        </div>
        <div className="price-input">
          <Button className="decrease" onClick={handlePriceDecrease}>-</Button>
          <Input
            className="price-value"
            value={price}
            onChange={handlePriceChange}
            style={{
              height: '36px',
              textAlign: 'center',
              padding: '0 10px',
              margin: '0 8px',
              borderRadius: '4px',
              background: theme.name === 'dark' ? '#192734' : '#F0F0F0',
              border: `1px solid ${theme.name === 'dark' ? '#2C3F4F' : '#D9D9D9'}`,
              color: theme.textColor
            }}
          />
          <Button className="increase" onClick={handlePriceIncrease}>+</Button>
          <Button 
            className={`market-price-btn${marketPriceActive ? ' active' : ''}`}
            style={{
              marginLeft: 8,
              height: '36px',
              borderRadius: '4px',
              background: marketPriceActive ? (theme.name === 'dark' ? '#2C9CDC' : '#2C9CDC') : (theme.name === 'dark' ? '#2C9CDC90' : '#2C9CDC90'),
              color: '#fff',
              border: 'none',
              fontWeight: 'bold',
              fontSize: '14px',
              padding: '0 16px',
              transition: 'background 0.2s',
            }}
            onClick={async () => {
              setMarketPriceActive((prev) => !prev);
              await refreshMarketPrices();
              // createTradeOrder();
            }}
          >{t("myOrders.MarketValue")}</Button>
        </div>
      </div>
      
      <div className="shares-input">
        <div className="shares-label">
          <Text>{t('OrderDetail.shares')}</Text>
        </div>
        <div className="shares-control">
          <Button className="decrease" onClick={handleSharesDecrease}>-</Button>
          <Input
            className="shares-value"
            value={shares}
            onChange={handleSharesChange}
            style={{
              height: '36px',
              textAlign: 'center',
              padding: '0 10px',
              margin: '0 8px',
              borderRadius: '4px',
              background: theme.name === 'dark' ? '#192734' : '#F0F0F0',
              border: `1px solid ${theme.name === 'dark' ? '#2C3F4F' : '#D9D9D9'}`,
              color: theme.textColor
            }}
          />
          <Button className="increase" onClick={handleSharesIncrease}>+</Button>
        </div>
      </div>
      
      <div className="trade-summary">
        <div className="total">
          <Text>{t('myOrders.total')}</Text>
          <Text className="total-value">${calculateTotal()}</Text>
        </div>
        <div className="potential-return">
          <Text>{t('default.potentialReturn')}</Text>
          <Text className="return-value">${calculatePotentialReturn().value}</Text>
        </div>
      </div>
      
      <Button className="buy-button" onClick={createTradeOrder} loading={tradeLoading}>{t('default.submitTrans')}</Button>
      
      <div className="terms-text">
        <Text>{t('default.byTrading')}<a href="javascript:;">{t('default.terms')}</a>.</Text>
      </div>
    </div>
  );

  const renderRules = () => (
    <div className={`rules-section ${themeClass}`}>
      <Title level={4}>{t('OrderDetail.rules')}</Title>
      <div style={{borderBottom: theme.name === "dark" ? "1px solid #2C3F4F" : "1px solid #E7E7E7", marginBottom: "16px"}}/>
      {Array.isArray(marketData.rules) ? (
        marketData.rules.map((rule: string, index: number) => (
          <Paragraph key={index}>{rule}</Paragraph>
        ))
      ) : (
        <Paragraph>{marketData.rules || "No rules specified"}</Paragraph>
      )}
    </div>
  );

  // Memoize the gameId value to prevent unnecessary re-renders of CommentSection
  const commentGameId = useMemo(() => {
    return passedMarketData?.id || marketId || '';
  }, [passedMarketData?.id, marketId]);

  const renderComments = () => (
    <CommentSection gameId={commentGameId} />
  );

  const renderMarketDrawerContent = () => (
    <DrawerContent
      theme={theme}
      isLoggedIn={isLoggedIn}
      themeClass={themeClass}
      handleConnectWallet={handleConnectWallet}
      handleLogout={handleLogout}
      toggleTheme={toggleTheme}
    />
  );

  // 修改Buy Yes和Buy No按钮的点击处理函数
  const handleTradeButtonClick = (outcome: MarketOutcome, yesNo: string) => {
    if (!outcome) return;
    
    // Set the selected outcome and yes/no option
    setSelectedYesNo(yesNo);
    selectOutcome(outcome, yesNo);
    
    // Get condition ID for this specific outcome
    if (passedMarketData) {
      try {
        if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
          let condition = passedMarketData.conditions.find(c => 
            c && c.description === outcome.name
          );
          
          if (!condition && typeof outcome.id === 'number') {
            condition = passedMarketData.conditions.find(c => 
              c && c.id === outcome.id.toString()
            );
          }
          
          if (!condition && passedMarketData.conditions.length > 0) {
            condition = passedMarketData.conditions[0];
          }
          
          // If we found a condition, fetch the order book data for this specific outcome
          if (condition && condition.cid) {
            console.log(`Handling trade button click for outcome ${outcome.id}, fetching order book with condition: ${condition.cid}`);
            console.log("fetchOrderBookData70");
            fetchOrderBookData(outcome.id, condition.cid, yesNo.toLowerCase());
          }
        }
      } catch (error) {
        console.error("Error in handleTradeButtonClick:", error);
      }
    }
    
    // If on mobile, show the trade modal
    if (isMobile) {
      setTradeModalVisible(true);
    }
  };
  
  // 关闭交易弹窗
  const closeTradeModal = () => {
    setTradeModalVisible(false);
  };

  // Render the order book for a specific outcome (used in the outcomes list)
  const renderOrderBookForOutcome = (outcome: MarketOutcome) => {
    const isExpanded = expandedOrderBooks.includes(outcome.id);
    const outcomeData = orderBookDataMap[outcome.id] || { 
      outcomeId: outcome.id,
      asks: defaultOrderBookData.asks,
      bids: defaultOrderBookData.bids,
      isLoading: false,
      error: null
    };
    
    // 使用组件顶层的ref，不要在函数内创建新的ref
    
    // Only fetch once when expanded
    if (isExpanded && !outcomeData.asks.length && !outcomeData.bids.length && 
        !outcomeData.isLoading && !outcomeDataFetchedRef.current[outcome.id]) {
      
      // 标记此outcome已经尝试获取数据
      outcomeDataFetchedRef.current = {
        ...outcomeDataFetchedRef.current,
        [outcome.id]: true
      };
      
      // Get condition ID for this specific outcome
      if (passedMarketData) {
        try {
          if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
            let condition = passedMarketData.conditions.find(c => 
              c && c.description === outcome.name
            );
            
            if (!condition && typeof outcome.id === 'number') {
              condition = passedMarketData.conditions.find(c => 
                c && c.id === outcome.id.toString()
              );
            }
            
            if (!condition && passedMarketData.conditions.length > 0) {
              condition = passedMarketData.conditions[0];
            }
            
            // If we found a condition, fetch the order book data for this specific outcome
            if (condition && condition.cid) {
              console.log(`Order book expanded for outcome ${outcome.id}, fetching data with condition: ${condition.cid}`);
              console.log("fetchOrderBookData1");
              fetchOrderBookData(outcome.id, condition.cid, selectedYesNo.toLowerCase());
            }
          }
        } catch (error) {
          console.error("Error in renderOrderBookForOutcome:", error);
        }
      }
    }
    
    // Reset the ref when collapsed
    if (!isExpanded) {
      // 清除此outcome的获取标记，使其可以在下次展开时重新获取
      outcomeDataFetchedRef.current = {
        ...outcomeDataFetchedRef.current,
        [outcome.id]: false
      };
    }
    
    // Render the order book with outcome-specific data
    const renderOutcomeOrderBook = () => {
      const type = 'ask';
      const barColor = type === 'ask' ? '#********' : '#25AE6020';
      const barColor1 = type !== 'ask' ? '#********' : '#25AE6020';
      
      // Calculate maximum quantities for asks and bids to determine bar widths
      const maxAskQuantity = Math.max(...outcomeData.asks.map(ask => parseFloat(ask.shares) || 0), 1);
      const maxBidQuantity = Math.max(...outcomeData.bids.map(bid => parseFloat(bid.shares) || 0), 1);
      
      return (
        <div className="order-book-container" style={{marginLeft: '16px'}}>
          <Tabs defaultActiveKey="order-book" onChange={(key) => handleTabChange(key, outcome)} className={`market-tabs ${themeClass}`}>
            <TabPane tab={t('OrderDetail.orderBook')} key="order-book">
              <div className="order-book-header">
                <div className="trade-header">{t('default.tradeYes')}</div>
                <div className="price-header">{t('OrderDetail.price')}</div>
                <div className="shares-header">{t('OrderDetail.shares')}</div>
                <div className="total-header">{t('myOrders.total')}</div>
              </div>
              
              {outcomeData.isLoading ? (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <Spin size="small" />
                  <div style={{ marginTop: 10 }}>Loading order book...</div>
                </div>
              ) : outcomeData.error ? (
                <div style={{ textAlign: 'center', padding: '20px 0', color: '#ff4d4f' }}>
                  {outcomeData.error}
                  <div style={{ marginTop: 10 }}>
                    <Button size="small" onClick={() => {
                      if (passedMarketData&&passedMarketData?.conditions?.length > 0) {
                        const condition = passedMarketData?.conditions.find(c => 
                          c && (c.description === outcome.name || c.id === outcome.id.toString())
                        ) || passedMarketData?.conditions[0];
                        
                        if (condition?.cid) {
                          console.log("fetchOrderBookData2");
                          fetchOrderBookData(outcome.id, condition.cid, selectedYesNo.toLowerCase());
                        }
                      }
                    }}>
                      Retry
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="asks-section">
                    {outcomeData.asks.map((ask, index) => {
                      // Calculate proportional width for this ask
                      const askQuantity = parseFloat(ask.shares) || 0;
                      const askWidthPercentage = (askQuantity / maxAskQuantity) * 100;
                      
                      return (
                        <div 
                          key={`ask-${index}`} 
                          className="order-row ask-row"
                          onClick={() => {
                            // Fill price and shares inputs when clicked
                            // Format price properly to ensure it's displayed as a readable value with ¢ symbol
                            const formattedPrice = safeFormatUnits(ask.price.replace('¢', ''), 16)+'¢';
                            setPrice(formattedPrice);
                            setShares(ask.shares);
                            // Select the outcome if not already selected
                            if (selectedOutcome.id !== outcome.id) {
                              selectOutcome(outcome, selectedYesNo);
                            }
                          }}
                          style={{ cursor: 'pointer' }}
                        >
                          <div className="trade-col">
                            <DepthBar type={"ask"} width={askWidthPercentage} color={barColor} />
                          </div>
                          <div className="price-col">{safeFormatUnits(ask.price.replace('¢', ''), 16)}¢</div>
                          <div className="shares-col">{ask.shares}</div>
                          <div className="total-col">${safeFormatUnits(ask.total.replace('$', ''), 16)}</div>
                        </div>
                      );
                    })}
                  </div>
                  
                  <div className="bids-section">
                    {outcomeData.bids.map((bid, index) => {
                      // Calculate proportional width for this bid
                      const bidQuantity = parseFloat(bid.shares) || 0;
                      const bidWidthPercentage = (bidQuantity / maxBidQuantity) * 100;
                      
                      return (
                        <div 
                          key={`bid-${index}`} 
                          className="order-row bid-row"
                          onClick={() => {
                            // Fill price and shares inputs when clicked
                            // Format price properly to ensure it's displayed as a readable value with ¢ symbol
                            const formattedPrice = safeFormatUnits(bid.price.replace('¢', ''), 16) + '¢';
                            setPrice(formattedPrice);
                            setShares(bid.shares);
                            // Select the outcome if not already selected
                            if (selectedOutcome.id !== outcome.id) {
                              selectOutcome(outcome, selectedYesNo);
                            }
                          }}
                          style={{ cursor: 'pointer' }}
                        >
                          <div className="trade-col">
                            <DepthBar type={"Bids"} width={bidWidthPercentage} color={barColor1} />
                          </div>
                          <div className="price-col">{safeFormatUnits(bid.price.replace('¢', ''), 16)}¢</div>
                          <div className="shares-col">{bid.shares}</div>
                          <div className="total-col">${safeFormatUnits(bid.total.replace('$', ''), 16)}</div>
                        </div>
                      );
                    })}
                  </div>
                </>
              )}
            </TabPane>
            <TabPane tab={t('myOrders.graph')} key="graph">
              <div className="chart-container" style={{ 
                height: '300px', 
                margin: '20px 0',
                backgroundColor: 'transparent' 
              }}>
                {chartLoading ? (
                  <div style={{ textAlign: 'center', padding: '20px 0', height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Spin size="large" />
                    <div style={{ marginTop: 10 }}>Loading price history...</div>
                  </div>
                ) : chartError ? (
                  <div style={{ textAlign: 'center', padding: '20px 0', color: '#ff4d4f', height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {chartError}
                    <div style={{ marginTop: 10 }}>
                      <Button size="small" onClick={() => {
                        if (passedMarketData&&passedMarketData?.conditions?.length > 0) {
                          const condition = passedMarketData?.conditions.find(c => 
                            c && (c.description === outcome.name || c.id === outcome.id.toString())
                          ) || passedMarketData?.conditions[0];
                          
                          if (condition?.cid) {
                            fetchPriceHistory(condition.cid);
                          }
                        }
                      }}>
                        Retry
                      </Button>
                    </div>
                  </div>
                ) : (
                  <LineChart 
                    data={chartData} 
                    xAxisData={chartDates} 
                    theme={theme.name === 'dark' ? 'dark' : 'light'} 
                    showSymbol={chartData.length < 30} // Only show symbols if we have fewer data points
                  />
                )}
              </div>
            </TabPane>
          </Tabs>
        </div>
      );
    };
    
    return (
      <div className="order-book-section">
        <div className={`order-book-content ${isExpanded ? 'expanded' : 'collapsed'}`}>
          {isExpanded && renderOutcomeOrderBook()}
        </div>
      </div>
    );
  };

  // 安全格式化，避免小数点字符串报错
  function safeFormatUnits(value: string, decimals: number = 16): string {
    if (typeof value === 'string' && !value.includes('.') && value !== '') {
      try {
        const formattedValue = ethers.formatUnits(value, decimals);
        // 将格式化后的值转换为数字，然后保留2位小数
        const valueAsNumber = parseFloat(formattedValue);
        return valueAsNumber.toFixed(2);
      } catch {
        // 尝试将原始值直接转为数字并保留2位小数
        try {
          const valueAsNumber = parseFloat(value);
          if (!isNaN(valueAsNumber)) {
            return valueAsNumber.toFixed(2);
          }
        } catch {
          // 如果转换失败，返回原始值
        }
        return value;
      }
    }
    // 尝试将非符合条件的值转为数字并保留2位小数
    try {
      const valueAsNumber = parseFloat(value);
      if (!isNaN(valueAsNumber)) {
        return valueAsNumber.toFixed(2);
      }
    } catch {
      // 如果转换失败，返回原始值
    }
    return value;
  }

  // 安全格式化volume数据
  const formatVolume = (vol: number | undefined | null): string => {
    if (vol === undefined || vol === null || isNaN(vol)) {
      return '0';
    }
    try {
      const formattedVol = (vol / 1e18).toFixed(0);
      return isNaN(parseFloat(formattedVol)) ? '0' : formattedVol;
    } catch {
      return '0';
    }
  };

  // 添加语言变化事件监听
  useEffect(() => {
    const handleLanguageChange = async () => {
      if (!passedMarketData?.id) return;
      
      try {
        // 获取最新的市场数据
        const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/game?id=${passedMarketData.id}&lang=${i18n.language}`);
        if (!response.ok) {
          throw new Error(`API responded with status: ${response.status}`);
        }
        
        const data = await response.json();
        if (data && data.code === 200 && data.data) {
          const updatedMarketData = data.data;
          
          const lang = i18n.language;
          
          console.log("lang",lang);
          console.log("description",updatedMarketData.description);
          console.log("rules",updatedMarketData.rules);

  
          // 更新市场数据，保持其他字段不变，只更新description和rules
          setMarketData(prevData => ({
            ...prevData,
            title: updatedMarketData.description || prevData.title,
            rules: updatedMarketData.rules ? [updatedMarketData.rules] : prevData.rules
          }));
        }
      } catch (error) {
        console.error('更新市场数据失败:', error);
      }
    };

    // 订阅语言变化事件
    eventBus.on('languageChanged', handleLanguageChange);

    // 清理订阅
    return () => {
      eventBus.off('languageChanged', handleLanguageChange);
    };
  }, [passedMarketData?.id, i18n.language]);

  // 新增：条件id到vol的映射
  const [outcomeVolMap, setOutcomeVolMap] = useState<Record<string, string>>({});

  // // 获取所有 outcome 的 cid
  // useEffect(() => {
  //   // if (!passedMarketData?.conditions || passedMarketData.conditions.length === 0) return;
  //   // const cids = passedMarketData.conditions.map(c => c.cid).filter(Boolean);
  //   // if (cids.length === 0) return;
  //   // 拼接接口
  //   const url = `https://dapp.yc365.io/orderbook/volume/conditions/${passedMarketData.cid}`;
  //   fetch(url)
  //     .then(res => res.json())
  //     .then(data => {
  //       if (data && data.code === 0 && data.data) {
  //         setOutcomeVolMap(data.data);
  //       }
  //     })
  //     .catch(() => {});
  // }, [passedMarketData?.cid]);

  // Toggle favorite status
  const toggleFavorite = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    try {
      if (!address) {
        // If user is not logged in, prompt them to connect wallet
        checkWallet();
        return;
      }


      if (!isFavorite) {
        // Add bookmark using the API
        const response = await dappApi.post(
          '/v1/favorite', 
          { game_id: marketId },
        );
        
        if (response.status==='success') {
          setIsFavorite(true);
          successMsg(t('favorite.added'));
        } else {
          errorMsg(response.message || t('favorite.addFailed'));
        }
      } else {
        // Remove bookmark using the API
        // Note: You'll need to implement the delete endpoint if it's not provided
        const response = await dappApi.del(
          `/v1/favorite?game_id=${marketId}`
        );
        
        if (response.status==='success') {
          setIsFavorite(false);
          successMsg(t('favorite.removed'));
        } else {
          errorMsg(response.message || t('favorite.removeFailed'));
        }
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      errorMsg(t('favorite.error'));
    }
  };

  // Check if market is in favorites on load
  useEffect(() => {
    const checkFavoriteStatus = async () => {
      if (!marketId || !address) return;
      
      try {
        // Use the specific endpoint to check bookmark status for this activity
        const response = await dappApi.get(`/v1/favorite/status?game_id=${marketId}`);
        
        if (response.status === 'success' && response.data) {
          setIsFavorite(response.data.is_favorite);
        }
      } catch (error) {
        console.error('Error checking bookmark status:', error);
      }
    };
    
    checkFavoriteStatus();
  }, [marketId, address]);

  // 添加分享到X的函数
  const shareToTwitter = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    // 构建分享链接
    const text = `${marketData.title}`;
    // const text = "test"
    const url = window.location.href;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
    
    // 打开新窗口
    window.open(twitterUrl, '_blank');
    
    // 显示成功消息
    successMsg(t('share.success'));
  };

  return (
    <Layout className={`market-detail-layout ${themeClass}`}>
      {contextHolder}
      <Content className="market-detail-content">
        {isMobile && (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '10px',
            paddingRight: '10px',
            paddingLeft: '2px',
            gap: '12px'
          }}>
            <div style={{
              color: '#8C939F',
              fontSize: 12,
              lineHeight: '16px',
              flex: 1,
              minWidth: 0
            }}>
              {isMarketDataLoading ? (
                <Spin size="small" style={{ marginRight: 4 }} />
              ) : (
                `$${formatVolume(marketData.vol)} Vol.`
              )}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', flexShrink: 0, gap: '20px' }}>
              <div 
                onClick={toggleFavorite}
                style={{ 
                  cursor: 'pointer',
                  fontSize: '22px',
                  color: isFavorite ? '#F8CB46' : theme.name === 'dark' ? '#8C939F' : '#BFBFBF'
                }}
              >
                {isFavorite ? <StarFilled /> : <StarOutlined />}
              </div>
              <div 
                onClick={shareToTwitter}
                style={{ 
                  cursor: 'pointer',
                  fontSize: '22px',
                  color: theme.name === 'dark' ? '#8C939F' : '#BFBFBF'
                }}
              >
                <TwitterOutlined />
              </div>
            </div>
          </div>
        )}
        
        <div className="market-title-section">
          <div style={{ display: 'flex', alignItems: 'flex-start', gap: '16px', width: '100%' }}>
            <div className="market-avatar">
              <Avatar src={passedMarketData?.icon_url || userAvatarImage} size={48} />
            </div>
            <div className="market-title-info" style={{ maxWidth: 'calc(100% - 120px)' }}>
              <Title 
                level={4} 
                style={{ 
                  marginBottom: '4px', 
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  display: 'block'
                }}
              >
                {isMarketDataLoading ? (
                  <div style={{ 
                    width: '60%', 
                    height: '24px', 
                    backgroundColor: theme.name === 'dark' ? '#2C3F4F' : '#F0F0F0',
                    borderRadius: '4px',
                    animation: 'pulse 1.5s ease-in-out infinite'
                  }} />
                ) : (
                  marketData.title || 'Loading...'
                )}
              </Title>
              <Text className="market-date">
                {isMarketDataLoading ? (
                  <Spin size="small" style={{ marginRight: 8 }} />
                ) : (
                  marketData.date || 'Loading...'
                )}
              </Text>
            </div>
            {!isMobile && (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center',
                flexShrink: 0,
                gap: '20px'
              }}>
                <div 
                  onClick={toggleFavorite}
                  style={{ 
                    cursor: 'pointer',
                    fontSize: '22px',
                    color: isFavorite ? '#F8CB46' : theme.name === 'dark' ? '#8C939F' : '#BFBFBF'
                  }}
                >
                  {isFavorite ? <StarFilled /> : <StarOutlined />}
                </div>
                <div 
                  onClick={shareToTwitter}
                  style={{ 
                    cursor: 'pointer',
                    fontSize: '22px',
                    color: theme.name === 'dark' ? '#8C939F' : '#BFBFBF'
                  }}
                >
                  <TwitterOutlined />
                </div>
              </div>
            )}
          </div>
        </div>
        
        {!isMobile && (
          <div style={{
            width: '100%',
            textAlign: 'left',
            color: '#8C939F',
            fontSize: 12,
            lineHeight: '16px',
            marginTop: -10,
            marginBottom: 15,
            paddingLeft: 2
          }}>
            {isMarketDataLoading ? (
              <Spin size="small" style={{ marginRight: 4 }} />
            ) : (
              `$${formatVolume(marketData.vol)} Vol.`
            )}
          </div>
        )}

        <Row gutter={24} className="market-details-row">
          <Col xs={24} lg={16} className="market-main-col">
            <div style={{ 
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginLeft:'16px',
              marginBottom: '8px',
              cursor: 'pointer',
              userSelect: 'none'
            }}>
              <div className="trade-header">{t('myOrders.outcome')}</div>
            
              <div style={{width: "90px",marginRight:isMobile?"0px": "190px"}}>% {t('default.chance')}</div>
              
            </div>
            {marketData.outcomes && marketData.outcomes.map((outcome: MarketOutcome) => {
              // Define the toggle function for this specific outcome
              const handleToggleOrderBook = () => {
                const isCurrentlyExpanded = expandedOrderBooks.includes(outcome.id);
                
                if (!isCurrentlyExpanded && passedMarketData) {
                  try {
                    // Find the corresponding condition for this outcome
                    if (passedMarketData.conditions && Array.isArray(passedMarketData.conditions) && passedMarketData.conditions.length > 0) {
                      // First try to find condition by matching description to outcome name
                      let condition = passedMarketData.conditions.find(c => 
                        c && c.description === outcome.name
                      );
                      
                      // If not found, try matching by ID
                      if (!condition && typeof outcome.id === 'number') {
                        condition = passedMarketData.conditions.find(c => 
                          c && c.id === outcome.id.toString()
                        );
                      }
                      
                      // If still not found, use the first condition as fallback
                      if (!condition && passedMarketData.conditions.length > 0) {
                        condition = passedMarketData.conditions[0];
                      }
                      
                      // Only fetch if we have a valid condition with a cid
                      if (condition && condition.cid) {
                        console.log(`Toggling order book for condition: ${condition.description} (${condition.cid})`);
                        console.log("fetchOrderBookData3");
                        fetchOrderBookData(outcome.id, condition.cid, selectedYesNo.toLowerCase());
                      } else {
                        console.warn("No valid condition found for outcome:", outcome);
                      }
                    }
                  } catch (error) {
                    console.error("Error in handleToggleOrderBook:", error);
                  }
                }
                
                toggleOrderBook(outcome.id);
              };
              
              return (
                <React.Fragment key={outcome.id}>
                  <div 
                    className="outcome-row"
                    onClick={() => {selectOutcome(outcome, selectedYesNo); handleToggleOrderBook();}}
                    style={{borderBottom:theme.name === "dark" ? "1px solid #2C3F4F" : "1px solid #E7E7E7"}}
                  >
                    <div className="outcome-info">
                    <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'start',flexDirection: 'column',width: '100%',marginBottom: '10px'}}>
                      <div className="outcome-name">{outcome.name}</div>
                      <div
                        style={{
                          width: '100%',
                          textAlign: 'left',
                          color: '#8C939F',
                          fontSize: 12,
                          lineHeight: '16px',
                          marginTop: 5,
                          marginBottom: 0,
                        }}
                        >
                          {(() => {
                            // 找到当前 outcome 的 cid
                            const condition = passedMarketData?.conditions?.find(c => c.description === outcome.name || c.id === outcome.id.toString());
                            const cid = condition?.cid;
                            const vol = cid && outcomeVolMap[cid];
                            
                            if (isMarketDataLoading) {
                              return <Spin size="small" />;
                            }
                            
                            if (!vol || isNaN(Number(vol))) {
                              return '-- Vol.';
                            }
                            
                            try {
                              const formattedVol = (Number(vol) / 1e18).toFixed(0);
                              return `$ ${formattedVol} Vol.`;
                            } catch {
                              return '-- Vol.';
                            }
                          })()}
                       </div>
                    </div>
                      
                      <div className="outcome-chance">{outcome.yesPrice.replace('¢', '')}%</div>
                    </div>
                    <div className="outcome-actions">
                    {outcome.result ===0 &&  <Button 
                        className="yes-price-btn" 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTradeButtonClick(outcome, 'Yes');
                        }}
                      >
                        {t('default.buyyes')} {formatPrice(outcome.yesPrice)}
                      </Button>}
                      {outcome.result ===0 &&  <Button 
                        className="no-price-btn" 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTradeButtonClick(outcome, 'No');
                        }}
                      >
                        {t('default.buyno')} {formatPrice(outcome.noPrice)}
                      </Button>}

                      {outcome.result ===1 && <Flex gap={'small'} align="center" style={{ flex: 2 }}><Text>Result is YES</Text></Flex>}
                      {outcome.result ===2 && <Flex gap={'small'} align="center" style={{ flex: 2 }}><Text>Result is NO</Text></Flex>}
  
                    </div>
                  </div>
                  {renderOrderBookForOutcome(outcome)}
                </React.Fragment>
              );
            })}
            
            {marketData.rules?renderRules():""}
            {marketData.comments?renderComments():""}
          </Col>
          
          {/* 在非移动设备上显示侧边栏交易界面 */}
          {!isMobile && (
            <Col xs={24} lg={8} className={`market-sidebar-col ${themeClass}`}>
              {selectedOutcome && selectedOutcome.result !== 1 && selectedOutcome.result !== 2 && (
                renderTradingInterface()
              ) }
            </Col>
          )}
        </Row>
      </Content>
      
      {/* 使用Drawer代替Modal，从底部弹出 */}
      <Drawer
        title={null}
        placement="bottom"
        closable={false}
        onClose={closeTradeModal}
        open={tradeModalVisible}
        height="auto"
       
        bodyStyle={{ 
          padding: 0,
          overflow: 'hidden'
        }}
        className={`bottom-drawer ${themeClass}`}
      >
        <div className="modal-trade-interface"> 
          <div style={{ 
            padding: '0 16px 20px',
            background: theme.backgroundColor
          }}>
            {selectedOutcome && selectedOutcome.result !== 1 && selectedOutcome.result !== 2 && (
              renderTradingInterface()
            )}
          </div>
        </div>
      </Drawer>
      
      <Drawer
        title={isLoggedIn ? <Avatar src={userAvatarImage} /> : "Menu"}
        placement="left"
        onClose={onCloseDrawer}
        open={drawerVisible}
        closable={true}
        closeIcon={<CloseOutlined style={{color: theme.textColor}}/>}
        bodyStyle={{ 
          padding: 0, 
          backgroundColor: theme.backgroundColor,
          height: '100%'
        }}
        headerStyle={{ 
          backgroundColor: theme.backgroundColor, 
          borderBottom: `1px solid ${theme.borderColor}`
        }}
        className={`dark-drawer ${themeClass}`}
        style={{ background: theme.backgroundColor }}
      >
        {renderMarketDrawerContent()}
      </Drawer>
    </Layout>
  );
};

export default MarketDetail;
