/* Dashboard styles */
.dashboard-layout {
  width: 100%;
  min-height: calc(100vh  - 124px);
}

.dashboard-layout.dark-theme {
  background-color: #1A223500;
  color: #fff;
}

.dashboard-layout.light-theme {
  background-color: #fff;
  color: #000;
}

.dashboard-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-title {
  margin-bottom: 16px;
}

.dashboard-title h2 {
  font-weight: 500;
  margin: 0;
}

/* Chart cards */
.dashboard-charts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 桌面版视图 - Balance和Earnings并排显示 */
@media (min-width: 769px) {
  .dashboard-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
      "balance earnings"
      "available-assets frozen-assets"
      "activity activity";
    gap: 16px;
  }
  
  .balance-card {
    grid-area: balance;
  }
  
  .earnings-card {
    grid-area: earnings;
  }
  
  .available-assets-card {
    grid-area: available-assets;
  }
  
  .frozen-assets-card {
    grid-area: frozen-assets;
  }
  
  .activity-card {
    grid-area: activity;
  }
}

.balance-card,
.earnings-card,
.activity-card,
.available-assets-card,
.frozen-assets-card {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
}

/* Theme-specific card styles */
.dark-theme.balance-card,
.dark-theme.earnings-card,
.dark-theme.activity-card,
.dark-theme.available-assets-card,
.dark-theme.frozen-assets-card {
  background-color: #1D2B39;
  border: 1px solid #2C3F4F;
}

.light-theme.balance-card,
.light-theme.earnings-card,
.light-theme.activity-card,
.light-theme.available-assets-card,
.light-theme.frozen-assets-card {
  background-color: #fff;
  border: 1px solid #E7E7E7;
}

/* Override Ant Design Card body padding */
.balance-card .ant-card-body,
.earnings-card .ant-card-body,
.activity-card .ant-card-body,
.available-assets-card .ant-card-body,
.frozen-assets-card .ant-card-body {
  padding: 0;
}

/* Card header styles */
.card-header {
  display: flex;
  align-items: center;
  margin-left: 16px;
  margin-top: 20px;
  margin-bottom: 0;
  height: 0px;
}

.card-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 18px;
}

.card-icon {
  color: #fff;
  width: 20px;
  height: 20px;
}

.dark-theme .card-icon {
  color: #fff;
}

.light-theme .card-icon {
  color: #333;
}

.card-header h4 {
  margin: 0;
  font-weight: 400;
  font-size: 16px;
}

/* Chart container */
.chart-container {
  position: relative;
  padding: 0 10px 5px;
  height: 200px;
  width: 100%;
  background-color: #00ff0000 !important;
}

/* Activity chart container */
.activity-card .chart-container {
  height: 260px;
  background-color: #00ff0000 !important;
}

/* Activity chart legends */
.activity-legends {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin: 0 20px 0 0;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #97A0A4;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 6px;
}

.quantity-color {
  background-color: #E74801;
}

.amount-color {
  background-color: #25AE60;
}

/* Activity chart */
.activity-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Percentage labels on charts */
.percentage-labels {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.percentage-label {
  position: absolute;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
}

/* Balance chart uses green labels */
.balance-card .percentage-label {
  color: #25AE60;
}

/* Earnings chart uses blue labels */
.earnings-card .percentage-label {
  color: #2C9CDC;
}

/* Activity chart has two sets of labels */
.quantity-labels .percentage-label {
  color: #E74801;
}

.amount-labels .percentage-label {
  color: #25AE60;
}

/* Responsive styles */
@media (max-width: 768px) {
  .dashboard-content {
    padding: 16px;
  }
  
  .chart-container {
    padding: 0 5px 5px;
    height: 200px;
  }
  
  .activity-card .chart-container {
    height: 240px;
  }
  
  .activity-legends {
    justify-content: flex-end;
    margin: 0 10px 0 0;
    gap: 12px;
  }
  
  .legend-item {
    font-size: 10px;
  }
} 