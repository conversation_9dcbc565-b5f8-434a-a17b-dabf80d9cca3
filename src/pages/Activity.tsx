import React, { useState, useEffect } from 'react';
import { Typography, List, Avatar, message, Tooltip, Card } from 'antd';
import { useTheme } from '../theme/ThemeProvider';
import './Activity.css';
import { dappApi } from '../services/request';
import { t } from 'i18next';

const { Title, Text } = Typography;

// 接口数据类型定义
interface ActivityData {
  id: number;
  game_id: string;
  description: string;
  icon_url: string;
  user_address: string;
  user_name: string;
  profile_image: string;
  side: string;
  token_type: string;
  price: number;
  amount: number;
  created_at: string;
}

// 本地使用的活动数据类型
interface FormattedActivity {
  id: number;
  title: string;
  user: string;
  action: string;
  outcome: string;
  price: string;
  amount: string;
  time: string;
  image: string | null;
  bgColor: string;
  profile_image: string;
}

const Activity: React.FC = () => {
  const { theme } = useTheme();
  const [activities, setActivities] = useState<FormattedActivity[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [hoveredUser, setHoveredUser] = useState<number | null>(null);
  const [newActivityIds, setNewActivityIds] = useState<number[]>([]);
  
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';

  // 格式化接口返回的时间
  const formatTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}秒前`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}天前`;
    }
  };

  // 生成随机背景色
  const getRandomColor = (): string => {
    const colors = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // 将接口数据转换为本地使用的格式
  const formatActivities = (data: ActivityData[]): FormattedActivity[] => {
    return data.map(item => ({
      id: item.id,
      title: item.description,
      user: item.user_name || item.user_address.substring(0, 16) + '...',
      action: item.side,
      outcome: item.token_type.toUpperCase(),
      price: `${item.price.toFixed(2)}¢`,
      amount: `$${item.amount.toFixed(2)}`,
      time: formatTime(item.created_at),
      image: item.icon_url || null,
      bgColor: getRandomColor(),
      profile_image: item.profile_image
    }));
  };
  
  // 从本地缓存获取活动ID列表
  const getLocalActivityIds = (): number[] => {
    try {
      const cachedIds = localStorage.getItem('activityIds');
      if (cachedIds) {
        return JSON.parse(cachedIds);
      }
    } catch (error) {
      console.error('解析本地缓存ID失败:', error);
    }
    return [];
  };

  // 将活动ID列表保存到本地缓存
  const saveLocalActivityIds = (ids: number[]): void => {
    try {
      localStorage.setItem('activityIds', JSON.stringify(ids));
    } catch (error) {
      console.error('保存ID到本地缓存失败:', error);
    }
  };

  // 获取活动数据
  const fetchActivities = async () => {
    try {
      const response = await dappApi.get('/v1/trade-activities/latest');
      
      if (response.status === 'success' && Array.isArray(response.data)) {
        const formattedData = formatActivities(response.data);
        
        // 获取本地缓存ID
        const cachedIds = getLocalActivityIds();
        const cachedIdSet = new Set(cachedIds);
        
        // 检测新数据（与本地缓存比较）
        const newIds = formattedData
          .filter(item => !cachedIdSet.has(item.id))
          .map(item => item.id);
        
        // 如果有新数据，更新newActivityIds状态
        if (newIds.length > 0) {
          setNewActivityIds(newIds);
          
          // 4秒后清除高亮效果
          setTimeout(() => {
            setNewActivityIds([]);
          }, 4000);
          
          // 更新本地缓存的ID列表
          const currentIds = formattedData.map(item => item.id);
          saveLocalActivityIds(currentIds);
        }
        
        setActivities(formattedData);
        setLoading(false);
      } else {
        console.error('获取活动数据失败:');
      }
    } catch (error) {
      console.error('获取活动数据出错:', error);
      message.error(t("error"));
      setLoading(false);
    }
  };

  // 组件挂载时获取数据并设置轮询
  useEffect(() => {
    // 首次获取数据
    fetchActivities();
    
    // 设置10秒轮询
    const intervalId = setInterval(fetchActivities, 10000);
    
    // 组件卸载时清除定时器
    return () => clearInterval(intervalId);
  }, []);

  // Get outcome color based on outcome value
  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'YES':
        return '#4CAF50';
      case 'NO':
        return '#F44336';
      case 'UP':
        return '#4CAF50';
      case 'DOWN':
        return '#F44336';
      default:
        return theme.textColor;
    }
  };

  // 用户卡片内容组件
  const UserCard = ({ user, profile_image }: { user: string; profile_image: string }) => {
    const { theme } = useTheme();
    
    // 模拟财务数据（实际项目中应从API获取）
    const userStats = {
      profit: '$2k',
      volume: '$300k',
      isProfit: true // 利润为正
    };
    
    return (
      <Card 
        bordered={false}
        bodyStyle={{ 
          padding: '16px', 
          width: '240px',
          borderRadius: '12px'
        }}
      >
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '16px',
          paddingBottom: '12px',
          borderBottom: `1px solid ${theme.name === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`
        }}>
          <Avatar 
            size={48} 
            src={profile_image} 
            style={{ marginRight: '12px' }} 
          />
          <div>
            <div style={{ 
              fontWeight: 'bold',
              color: theme.textColor
            }}>
              {user}
            </div>
          </div>
        </div>
        
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-around',
          marginTop: '12px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              fontWeight: 'bold',
              color: userStats.isProfit ? '#4CAF50' : '#F44336'
            }}>
              {userStats.profit}
            </div>
            <div style={{ 
              color: theme.textColor,
              opacity: 0.7
            }}>
              {t('activity.profit_loss')}
            </div>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              fontWeight: 'bold',
              color: theme.textColor
            }}>
              {userStats.volume}
            </div>
            <div style={{ 
              color: theme.textColor,
              opacity: 0.7
            }}>
              {t('activity.volume')}
            </div>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className={`activity-container ${themeClass}`}>
      <div className="activity-header">
        <Title level={1} style={{ color: theme.textColor, margin: 0 }}>
          {t('activity.activity')}
        </Title>
      </div>

      <List
        className="activity-list"
        itemLayout="horizontal"
        loading={loading}
        dataSource={activities}
        renderItem={(item, index) => {
          const isNewActivity = newActivityIds.includes(item.id);
          return (
            <List.Item 
              className={`activity-item ${isNewActivity ? 'new-activity-highlight' : ''}`} 
              style={{
                transition: 'all 0.5s ease',
                backgroundColor: isNewActivity 
                  ? theme.name === 'dark' 
                    ? 'rgba(24, 144, 255, 0.15)' 
                    : 'rgba(230, 247, 255, 0.9)'
                  : 'transparent',
                transform: isNewActivity ? 'scale(1.02)' : 'scale(1)',
                boxShadow: isNewActivity 
                  ? theme.name === 'dark'
                    ? '0 0 10px rgba(24, 144, 255, 0.5)'
                    : '0 0 10px rgba(24, 144, 255, 0.3)'
                  : 'none',
                borderRadius: '8px',
              }}
            >
              <div className="activity-content">
                <div className="activity-left">
                  <Avatar 
                    src={item.image} 
                    size={48} 
                    className="activity-image" 
                    style={{ backgroundColor: item.bgColor }}
                  />
                  <div className="activity-details">
                    <div className="activity-title">{item.title}</div>
                    <div className="activity-user">
                      <Avatar size={24} src={item.profile_image} style={{ backgroundColor: '#1890ff' }} />
                      <div
                        style={{ position: 'relative' }}
                        onMouseEnter={() => setHoveredUser(index)}
                        onMouseLeave={() => setHoveredUser(null)}
                      >
                        <Text className="username" style={{ cursor: 'pointer' }}>{item.user}</Text>
                        {hoveredUser === index && (
                          <div style={{ 
                            position: 'absolute', 
                            zIndex: 1000, 
                            bottom: '100%', 
                            left: 0,
                            marginBottom: '8px'
                          }}>
                            <UserCard user={item.user} profile_image={item.profile_image} />
                          </div>
                        )}
                      </div>
                      <Text className="action">{item.action}</Text>
                      <Text className="outcome" style={{ color: getOutcomeColor(item.outcome) }}>
                        {item.outcome}
                      </Text>
                      <Text className="price">{t('activity.price')} {item.price}</Text>
                      <Text className="amount">({item.amount})</Text>
                    </div>
                  </div>
                </div>
                <div className="activity-time">{item.time}</div>
              </div>
            </List.Item>
          );
        }}
      />
    </div>
  );
};

export default Activity; 