/* Market Detail Page Styles */
.market-detail-layout {
  width: 100%;
  min-height: calc(100vh  - 124px);
}

.market-detail-layout.dark-theme {
  background-color: #111A2400;
  color: #fff;
}

.market-detail-layout.light-theme {
  background-color: #fff;
  color: #000;
}

.market-detail-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Market Title Section */
.market-title-section {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.market-avatar {
  margin-right: 16px;
}

.market-title-info h4 {
  /* margin-bottom: 4px; */
  margin-top: 0%;
}

.market-date {
  color: #777E8C;
  font-size: 14px;
}

/* Market Details Row */
.market-details-row {
  display: flex;
  flex-wrap: wrap;
}

.market-main-col {
  flex: 1;
  min-width: 0;
}

.market-sidebar-col {
  width: 320px;
  margin-left: 24px;
  border-radius: 10px 10px 10px 10px;
  /* border: 1px solid #E7E7E7;  */
  height: 554px; 
  margin-top: -100px;
}

.market-sidebar-col.light-theme {
  border: 1px solid #E7E7E7; 
}

.market-sidebar-col.dark-theme {
  border: 1px solid #2C3F4F;
}

@media (max-width: 992px) {
  .market-sidebar-col {
    width: 100%;
    margin-left: 0;
    margin-top: 24px;
  }
}

/* Outcome Rows */
.outcome-row {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 0px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.outcome-info {
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: row;
}

.outcome-name {
  flex: 1;
  font-weight: 500;
}

.dark-theme .outcome-row {
  background-color: #1D2B39;
}

.light-theme .outcome-row {
  background-color: #F5F5F500;
  border: 1px solid #E7E7E700;
}

/* Hover effects for outcome rows */
.dark-theme .outcome-row:hover {
  background-color: #2C3F4F;
  border-color: #4A90E200;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
  transform: translateY(-1px);
}

.light-theme .outcome-row:hover {
  background-color: #F8F9FA;
  border-color: #4A90E200;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);
  transform: translateY(-1px);
}

.outcome-row.selected {
  border: 1px solid #0852F0;
}

/* Active state for outcome rows */
.dark-theme .outcome-row:active {
  background-color: #1A2530;
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(74, 144, 226, 0.3);
}

.light-theme .outcome-row:active {
  background-color: #E9ECEF;
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(74, 144, 226, 0.2);
}

.outcome-chance {
  font-weight: bold;
  font-size: 18px;
  width: 60px;
  text-align: center;
}

.dark-theme .outcome-chance {
  color: #fff;
}

.light-theme .outcome-chance {
  color: #000;
}

.outcome-actions {
  display: flex;
  gap: 8px;
}

.yes-price-btn {
  background-color: #25AE6020;
  color: #25AE60;
  border: none;
  border-radius: 4px;
}

.yes-price-btn:hover {
  background-color: #25AE60 !important;
  color: #FFFFFF !important;
}

.no-price-btn {
  background-color: #E7480120;
  color: #E74801;
  border: none;
  border-radius: 4px;
}

.no-price-btn:hover {
  background-color: #E74801 !important;
  color: #FFFFFF !important;
}

/* Order Book */
.order-book-container {
  margin-top: 24px;
  border-radius: 8px;
  overflow: hidden;
}

.dark-theme .order-book-container {
  background-color: #1D2B39;
}

.light-theme .order-book-container {
  background-color: #F5F5F500;
  border: 1px solid #E7E7E700;
}

.market-tabs {
  width: 100%;
}

.order-book-header {
  display: flex;
  padding: 12px 16px;
  font-weight: bold;
  border-bottom: 1px solid;
}

.dark-theme .order-book-header {
  border-color: #2C3F4F;
}

.light-theme .order-book-header {
  background-color: #F5F5F500;
  border-color: #E7E7E700;
}

.trade-header, .trade-col {
  width: 100px;
}

.price-header, .price-col {
  width: 80px;
  text-align: right;
}

.shares-header, .shares-col {
  width: 300px;
  text-align: right;
}

.total-header, .total-col {
  flex: 1;
  text-align: right;
}

.order-row {
  display: flex;
  padding: 8px 16px;
  align-items: center;
}

.ask-row .trade-col {
  color: #E74801;
}

.bid-row .trade-col {
  color: #25AE60;
}

.ask-row .price-col {
  color: #E74801;
}

.bid-row .price-col {
  color: #25AE60;
}

.spread-indicator {
  display: flex;
  justify-content: center;
  padding: 8px 0;
}

.spread-text {
  font-size: 12px;
  color: #777E8C;
}

/* Trading Interface */
.trading-interface {
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.dark-theme .trading-interface {
  background-color: #1D2B39;
}

.light-theme .trading-interface {
  background-color: #F5F5F500;
  border: 1px solid #E7E7E700;
}

.selected-outcome {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  height: 40px;
}

.buy-sell-tabs {
  display: flex;
  background-color: #1D2B39;
  border-radius: 4px;
  margin-bottom: 20px;
  width: 100%;
  overflow: hidden;
}

.dark-theme .buy-sell-tabs {
  background-color: #1D2B39;
}

.light-theme .buy-sell-tabs {
  background-color: #f5f5f500;
}

.tab-item {
  text-align: center;
  flex: 1;
  position: relative;
  letter-spacing: 0.5px;
}

.tab-item.active {
  font-weight: 600;
}

.dark-theme .tab-item:not(.active) {
  color: #777E8C;
}

.light-theme .tab-item:not(.active) {
  color: #9e9e9e;
}

.buy-tab, .sell-tab {
  flex: 1;
  text-align: center;
  height: 40px;
  border: none;
  border-radius: 0;
}

.dark-theme .buy-tab, .dark-theme .sell-tab {
  background-color: #192734;
  color: #777E8C;
}

.light-theme .buy-tab, .light-theme .sell-tab {
  background-color: #E0E0E0;
  color: #666;
}

.buy-tab.active {
  background-color: #0852F0;
  color: white;
}

.sell-tab.active {
  background-color: #E74801;
  color: white;
}

.outcome-selection {
  margin-bottom: 16px;
}

.outcome-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.yes-button, .no-button {
  flex: 1;
  height: 36px;
  border-radius: 4px;
}

.dark-theme .yes-button{
  border-color: #25AE6020;
  background-color: #25AE6020;
  color: #25AE60;
}

.dark-theme .no-button {
  border-color: #E7480120;
  background-color: #E7480120;
  color: #E74801;
}

.light-theme .yes-button, .light-theme .no-button {
  border-color: #D9D9D9;
  background-color: white;
  color: #666;
}

.yes-button.active {
  background-color: #25AE60;
  color: white;
  border-color: #25AE60;
}
.yes-button:hover {
  background-color: #25AE60 !important;
  color: white !important;
  border-color: #25AE60 !important;
}

.no-button.active {
  background-color: #E74801;
  color: white;
  border-color: #E74801;
}
.no-button:hover {
  background-color: #E74801 !important;
  color: white !important;
  border-color: #E74801 !important;
}

.limit-price, .shares-input {
  margin-bottom: 16px;
}

.price-label, .shares-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.balance {
  color: #777E8C;
}

.price-input, .shares-control {
  display: flex;
  align-items: center;
  height: 36px;
}

.decrease, .increase {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  padding: 0;
}

.dark-theme .decrease, .dark-theme .increase {
  background-color: #192734;
  border-color: #2C3F4F;
  color: #fff;
}

.light-theme .decrease, .light-theme .increase {
  background-color: #F0F0F0;
  border-color: #D9D9D9;
  color: #666;
}

.price-value, .shares-value {
  flex: 1;
  text-align: center;
  padding: 0 10px;
  height: 36px;
  line-height: 36px;
  border-radius: 4px;
  margin: 0 8px;
}

.dark-theme .price-value, .dark-theme .shares-value {
  background-color: #192734;
  border: 1px solid #2C3F4F;
}

.light-theme .price-value, .light-theme .shares-value {
  background-color: #F0F0F0;
  border: 1px solid #D9D9D9;
}

.trade-summary {
  margin-bottom: 20px;
}

.total, .potential-return {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.total-value {
  color: #2C9CDC;
  font-weight: bold;
}

.return-value {
  color: #25AE60;
}

.buy-button {
  width: 100%;
  height: 40px;
  background-color: #2C9CDC90;
  border: none;
  color: white;
  border-radius: 4px;
  font-weight: bold;
  margin-bottom: 16px;
}

.buy-button:hover {
  background-color: #2C9CDC !important;
  color: white !important;
 
}


.terms-text {
  font-size: 12px;
  text-align: center;
  color: #777E8C;
}

.terms-text a {
  color: #777E8C;
}

/* Rules Section */
.rules-section {
  padding: 0px 20px;
  border-radius: 8px;
}

.dark-theme .rules-section {
  background-color: #1D2B39;
}

.light-theme .rules-section {
  background-color: #F5F5F500;
  border: 1px solid #E7E7E700;
}

.rules-section h4 {
  margin-bottom: 16px;
}

.rules-section p {
  margin-bottom: 12px;
  line-height: 1.5;
}

/* Comments Section */
.comments-section {
  margin-top: 30px;
  padding: 0 20px;
}

.dark-theme .comments-section {
  background-color: #1D2B39;
}

.light-theme .comments-section {
  background-color: #F5F5F500;
  border: 1px solid #E7E7E700;
}

.comments-section h4 {
  margin-bottom: 16px;
}

.new-comment {
  margin-bottom: 30px;
}

.comment-input-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
}

.comment-input-container {
  position: relative;
  flex: 1;
  background-color: rgba(31, 41, 55, 0.8);
  border-radius: 8px;
  /* padding: 15px; */
  border: 1px solid #2C3F4F;
}

/* 移除所有输入框的焦点效果 */
.ant-input:focus, 
.ant-input-focused,
.ant-input:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused,
.ant-input-affix-wrapper:hover,
.ant-input-textarea-show-count::after,
input.price-value:focus, 
input.price-value:hover,
input.shares-value:focus, 
input.shares-value:hover {
  border-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

input.price-value,
input.shares-value,
.ant-input {
  border-color: transparent !important;
}

/* 确保Input在价格和份额输入框中不会改变原有样式 */
.price-value.ant-input,
.shares-value.ant-input {
  flex: 1;
  text-align: center;
  line-height: 36px;
  margin: 0 8px;
}

/* 深色主题覆盖 */
.dark-theme .comment-input-container {
  background-color: rgba(31, 41, 55, 0.8);
  border-color: #2C3F4F;
}

/* 浅色主题覆盖 */
.light-theme .comment-input-container {
  background-color: rgba(245, 245, 245, 0.8);
  border-color: #e0e0e0;
}

.comment-item {
  display: flex;
  margin-bottom: 25px;
}

.comment-avatar {
  margin-right: 16px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.comment-time {
  margin-left: 8px;
  color: #777E8C;
  font-size: 12px;
}

.comment-text {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #FFFFFF;
}

.comment-footer, .reply-footer {
  margin-top: 8px;
}

.reply-btn, .show-replies-btn, .hide-replies-btn {
  color: #97A0A4;
  padding: 0;
  height: auto;
}

.replies-container {
  margin-top: 16px;
  margin-left: 16px;
}

.reply-item {
  display: flex;
  margin-bottom: 16px;
}

.reply-avatar {
  margin-right: 12px;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.reply-time {
  margin-left: 8px;
  color: #777E8C;
  font-size: 12px;
}

.reply-input {
  margin: 15px 0;
  margin-left: 0px; /* 给回复保持缩进 */
  width: calc(100% );
}

.ant-input-textarea {
  width: 100%;
}

.ant-input-textarea textarea.ant-input {
  background-color: transparent;
}

.ant-btn.post-btn:hover {
  background-color: transparent !important;
  color: #4CB3FF !important;
}

.ant-btn.cancel-btn:hover {
  background-color: transparent !important;
  color: #999 !important;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  gap: 8px;
}

.page-btn {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn.active {
  background-color: #0852F0;
  color: white;
  border-color: #0852F0;
}

.prev-btn, .next-btn {
  min-width: 80px;
}

/* Graph Tab */
.chart-container {
  background-color: transparent !important;
  height: 100%;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  padding: 16px;
  margin-top: 16px;
}

.light-theme .chart-container {
  background-color: transparent !important;
}

.dark-theme .chart-container {
  background-color: transparent !important;
}

/* Media Queries */
@media (max-width: 768px) {
  .market-detail-content {
    padding: 16px;
  }
  
  .outcome-row {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
  }
  
  /* Mobile hover effects */
  .dark-theme .outcome-row:hover {
    background-color: #2C3F4F;
    border-color: #4A90E2;
    box-shadow: 0 2px 6px rgba(74, 144, 226, 0.2);
    transform: translateY(-1px);
  }
  
  .light-theme .outcome-row:hover {
    background-color: #F8F9FA;
    border-color: #4A90E2;
    box-shadow: 0 2px 6px rgba(74, 144, 226, 0.15);
    transform: translateY(-1px);
  }
  
  /* Mobile active state */
  .dark-theme .outcome-row:active {
    background-color: #1A2530;
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(74, 144, 226, 0.3);
  }
  
  .light-theme .outcome-row:active {
    background-color: #E9ECEF;
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(74, 144, 226, 0.2);
  }
  
  .outcome-info {
    flex-direction: row;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .outcome-name {
    margin-bottom: 0;
    width: auto;
    font-size: 14px;
    margin-right: 10px;
    flex: 1;
  }
  
  .outcome-chance {
    margin-bottom: 0;
    width: auto;
    text-align: right;
    font-size: 16px;
    font-weight: bold;
  }
  
  .outcome-actions {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }
  
  .yes-price-btn, .no-price-btn {
    flex: 1;
    font-size: 12px;
    padding: 0 8px;
    height: 32px;
    min-width: auto;
  }
  
  .order-book-header, .order-row {
    padding: 8px;
  }
  
  .trade-header, .trade-col {
    width: 60px;
  }
  
  .price-header, .price-col {
    width: 60px;
  }
  
  .shares-header, .shares-col {
    width: 190px;
  }
}

/* OrderBook 折叠/展开样式 */
.order-book-section {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.order-book-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--component-background);
  cursor: pointer;
  user-select: none;
}

.order-book-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.order-book-content {
  transition: max-height 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
}

.order-book-content.expanded {
  max-height: 2000px; /* 足够大的值以容纳内容 */
  opacity: 1;
}

.order-book-content.collapsed {
  max-height: 0;
  opacity: 0;
}

.light-theme .order-book-header {
  background-color: #f5f5f5;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
}

.dark-theme .order-book-header {
  background-color: #1f2937;
  color: #fff;
  border-bottom: 1px solid #374151;
}

.light-theme .order-book-section {
  background-color: #fff;
  border-color: #e8e8e8;
}

.dark-theme .order-book-section {
  background-color: #11182700;
  border-color: #374151;
}

/* 移动端交易弹窗样式 */
.modal-trade-interface {
  padding: 0;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-trade-interface .trading-interface {
  border-radius: 0;
  margin-bottom: 0;
  padding-bottom: 30px;
}

.dark-theme .ant-modal-content {
  background-color: #1D2B39;
}

.dark-theme .ant-modal-header {
  background-color: #1D2B39;
  border-bottom: 1px solid #2C3F4F;
}

.dark-theme .ant-modal-title {
  color: #ffffff;
}

.dark-theme .ant-modal-close {
  color: #ffffff;
}

.light-theme .ant-modal-content {
  background-color: #ffffff;
}

.light-theme .ant-modal-header {
  background-color: #ffffff;
  border-bottom: 1px solid #E7E7E7;
}

.light-theme .ant-modal-title {
  color: #000000;
}

.light-theme .ant-modal-close {
  color: #000000;
}

/* 底部抽屉样式 */
.bottom-drawer .ant-drawer-wrapper-body {
  overflow: hidden;
}

.bottom-drawer .ant-drawer-content-wrapper {
  box-shadow: none !important;
}

.bottom-drawer .ant-drawer-content {
  background-color: #1D2B39;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}

.light-theme.bottom-drawer .ant-drawer-content {
  background-color: #ffffff;
}

.bottom-drawer .modal-trade-interface {
  padding: 0;
  /* border-radius: 16px 16px 0 0; */
  overflow: hidden;
}

.bottom-drawer .trading-interface {
  border-radius: 0;
  border: none;
  margin-bottom: 0;
}

/* 拖动指示器样式 */
.drag-indicator {
  width: 36px;
  height: 4px;
  border-radius: 2px;
  background-color: rgba(150, 150, 150, 0.5);
  margin: 10px auto;
}

.dark-theme .drag-indicator {
  background-color: rgba(180, 180, 180, 0.5);
}

.light-theme .drag-indicator {
  background-color: rgba(100, 100, 100, 0.5);
}

/* Pagination 样式 */
.ant-pagination {
  background: transparent;
}

.ant-pagination-item {
  background: #0852F000 !important;
}

.ant-pagination-item-active {
  background: #0852F000 !important;
}

.ant-pagination-item-active a {
  color: #0852F0 !important;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  background: transparent;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  color: #666;
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* For touch devices, reduce hover effects and enhance touch feedback */
  .outcome-row {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
  
  .dark-theme .outcome-row:hover {
    background-color: #1D2B39;
    border-color: transparent;
    box-shadow: none;
    transform: none;
  }
  
  .light-theme .outcome-row:hover {
    background-color: #F5F5F500;
    border-color: transparent;
    box-shadow: none;
    transform: none;
  }
  
  /* Enhanced touch feedback */
  .dark-theme .outcome-row:active {
    background-color: #2C3F4F;
    border-color: #4A90E2;
    box-shadow: 0 1px 3px rgba(74, 144, 226, 0.3);
    transform: scale(0.98);
  }
  
  .light-theme .outcome-row:active {
    background-color: #F8F9FA;
    border-color: #4A90E2;
    box-shadow: 0 1px 3px rgba(74, 144, 226, 0.2);
    transform: scale(0.98);
  }
}

/* Focus states for accessibility */
.outcome-row:focus {
  outline: 2px solid #4A90E2;
  outline-offset: 2px;
}

.outcome-row:focus:not(:focus-visible) {
  outline: none;
}

/* Smooth transitions for all interactive elements */
.outcome-row * {
  transition: inherit;
} 