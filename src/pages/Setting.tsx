import { useState, useEffect } from 'react';
import type { ChangeEvent } from 'react';
import { 
  Layout, 
  Typography, 
  Button, 
  Input, 
  Select,
  Upload,
  Avatar,
  message,
  Progress,Grid
} from 'antd';
import { useMediaQuery } from 'react-responsive';
import eventTitleImg from '../assets/figma_images/eventTitleImg.png';
import { useUser } from '../context/UserContext';
import { 
  DownOutlined,
  UploadOutlined
} from '@ant-design/icons';

import { useTheme } from '../theme/ThemeProvider';
// import { useTranslation } from 'react-i18next';
import './Setting.css';
// import Web3 from 'web3';
import { dappApi,fileApi } from '../services/request';
import { DAPP_API } from '../services/apiPaths';

const { Content} = Layout;
const { Title, Text } = Typography;
const { Option } = Select;
const { useBreakpoint } = Grid;

const Setting = () => {
  // const { t } = useTranslation();
  const { theme } = useTheme();

  // 状态
  const [username, setUsername] = useState('Anonymous');
  const [rpcUrl, setRpcUrl] = useState('');
  const [gasPrice, setGasPrice] = useState('Low gas');
  const [avatarUrl, setAvatarUrl] = useState(eventTitleImg); // 默认头像
  const [walletAddress, setWalletAddress] = useState('');
  // const [profileData, setProfileData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { profileData, refreshProfile,address } = useUser();
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';
  const [messageApi, contextHolder] = message.useMessage();
  const screens = useBreakpoint();
  const isMobileMediaQuery = useMediaQuery({ maxWidth: 768 });
  const isMobile = !screens.md || isMobileMediaQuery;
  // 通用消息提示方法，可以传入消息类型
  const showMessage = (msg: String, type: 'success' | 'error' | 'warning' ) => {
    messageApi.open({
      type: type,
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  
  // 保留原有方法，但使用通用方法实现
  const errorMsg = (msg: String) => showMessage(msg, 'error');
  // const warningMsg = (msg: String) => showMessage(msg, 'warning');
  const successMsg = (msg: String) => showMessage(msg, 'success');
  // 获取用户数据
  const fetchUserData = async () => {
    try {
      setIsLoading(true);
      // const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`);
      // setProfileData(profile.data);
      
      // 设置表单初始值
      if (profileData) {
        setUsername(profileData?.username || 'Anonymous');
        setAvatarUrl(profileData?.profile_image || eventTitleImg);
        setRpcUrl(profileData?.settings?.rpc || '');
        setGasPrice(profileData?.settings?.low_gas || 'Low gas');
      }
    } catch (error) {
      console.error('获取用户数据失败:', error);
      // errorMsg('获取用户数据失败');
    } finally {
      setIsLoading(false);
    }
  };


  // 添加useEffect监听profileData变化
  useEffect(() => {
    if (profileData) {
      setUsername(profileData?.username || 'Anonymous');
      setAvatarUrl(profileData?.profile_image || eventTitleImg);
      setRpcUrl(profileData?.settings?.rpc || '');
      setGasPrice(profileData?.settings?.low_gas || 'Low gas');
    }
  }, [profileData]);

  // 获取钱包地址
  useEffect(() => {
    const getWalletAddress = async () => {
      try {
        if (window.ethereum) {
          // const web3 = new Web3(window.ethereum);
          // await (window.ethereum as any).request({ method: 'eth_requestAccounts' });
          // const accounts = await web3.eth.getAccounts();
          // const address = accounts[0];
          setWalletAddress(address);
          
          if (address) {
            await fetchUserData();
          }
        } else {
          // warningMsg('请安装MetaMask或其他以太坊钱包');
        }
      } catch (error) {
        console.error('获取钱包地址失败:', error);
        errorMsg('获取钱包地址失败');
      }
    };

    // 添加钱包账号变化的监听器
    const handleAccountsChanged = (accounts: string[]) => {
      if (accounts.length === 0) {
        setWalletAddress('');
        // message.info('钱包已断开连接');
      } else {
        const newAddress = accounts[0];
        setWalletAddress(newAddress);
        fetchUserData();
      }
    };
    
    // 添加钱包链变化的监听器
    const handleChainChanged = () => {
      window.location.reload();
    };
    
    if (window.ethereum) {
      (window.ethereum as any).on('accountsChanged', handleAccountsChanged);
      (window.ethereum as any).on('chainChanged', handleChainChanged);
    }

    getWalletAddress();
    
    return () => {
      if (window.ethereum) {
        (window.ethereum as any).removeListener('accountsChanged', handleAccountsChanged);
        (window.ethereum as any).removeListener('chainChanged', handleChainChanged);
      }
    };
  }, []);
  
  // 处理保存
  const handleSave = async () => {
    if (!walletAddress) {
      errorMsg('请先连接钱包');
      return;
    }

    try {
      setIsLoading(true);
      await dappApi.put(DAPP_API.UPDATE_PROFILE, {
        address: walletAddress,
        username: username,
        settings: { 
          rpc: rpcUrl || '',
          theme: theme.name,
          low_gas: gasPrice,
          language: 'en'
        },
        profile_image:  avatarUrl==eventTitleImg?"":avatarUrl
      });

      successMsg('设置已保存');
      
      // 刷新用户资料，确保菜单显示最新的头像和用户名
      await refreshProfile();
      
      if (rpcUrl !== profileData?.settings?.rpc) {
        successMsg('RPC地址已更新，请刷新页面以应用新设置');
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      errorMsg('保存设置失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理头像上传
  const handleUpload = async () => {
    const file = fileList[0];
    if (!file) {
      errorMsg('请先选择一个文件');
      return;
    }
    if(file.size > 0.5 * 1024 * 1024) {
      errorMsg('文件大小超过500KB，请压缩后再上传');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const result = await fileApi.upload(
        '/upload', 
        file,
        (percent) => {
          setUploadProgress(percent);
        },
        { 
          type: 'avatar', 
          description: '用户头像',
          sid: localStorage.getItem('sid') // 添加 sid
        },
        { maxSize: 5 * 1024 * 1024 }
      );
      
      successMsg('上传成功');
      console.log('上传结果:', result);
      
      if (result && result.data.url) {
        setAvatarUrl(result.data.url);
        
        // 立即更新头像，无需等待保存按钮
        try {
          await dappApi.put(DAPP_API.UPDATE_PROFILE, {
            address: walletAddress,
            profile_image: result.data.url
          });
          
          // 刷新用户资料，确保菜单显示最新的头像和用户名
          await refreshProfile();
        } catch (err) {
          console.error('更新头像失败:', err);
        }
      }
      
      setFileList([]);
    } catch (error: any) {
      console.error('上传失败:', error);
      
      if (error.code === 'FILE_TOO_LARGE') {
        errorMsg(error.message);
      } else if (error.message.includes('Network Error') || error.message.includes('网络错误')) {
        errorMsg('网络错误，请检查您的网络连接或联系管理员配置CORS');
      } else if (error.message.includes('超过服务器限制')) {
        errorMsg('文件大小超过服务器限制，请压缩后再上传');
      } else {
        errorMsg('上传失败: ' + (error.message || '未知错误'));
      }
    } finally {
      setUploading(false);
    }
  };

  // 文件选择改变时的处理
  // const handleChange = (info: any) => {
  //   setFileList(info.fileList.slice(-1)); // 只保留最后选择的文件
  // };

  const uploadProps = {
    onRemove: () => {
      setFileList([]);
    },
    beforeUpload: (file: any) => {
      // Set the file and then call handleUpload after a short delay
      // to ensure the file is set in state
      setFileList([file]);
      return false; // Prevent default upload behavior
    },
    onChange: (info:any) => {
      console.log(info.fileList);
      setTimeout(() => {
        handleUpload();
      }, 100);
    },
    fileList,
  };

  // 渲染表单输入框
  const renderFormField = (
    label: string, 
    value: string, 
    onChange: ((e: ChangeEvent<HTMLInputElement>) => void) | ((value: string) => void), 
    placeholder: string, 
    type: "input" | "select" = "input"
  ) => (
    <div className="form-field">
      <Text className="field-label" style={{marginLeft: isMobile ? '10px' : ''}}>{label}</Text>
      {type === "input" ? (
        <Input
          className={`form-input ${themeClass}`}
          value={value}
          onChange={onChange as (e: ChangeEvent<HTMLInputElement>) => void}
          placeholder={placeholder}
        />
      ) : (
        <Select
          className={`form-select ${themeClass}`}
          value={value}
          onChange={onChange as (value: string) => void}
          dropdownStyle={{ backgroundColor: theme.backgroundColor }}
          suffixIcon={<DownOutlined style={{ color: theme.name === 'dark' ? '#97A0A4' : '#777E8C' }} />}
        >
          <Option value="Low gas" style={{color:theme.textColor,backgroundColor: value === "Low gas" ? "#87BEDE" : "transparent"}}>Low gas</Option>
          <Option value="Medium gas" style={{color:theme.textColor,backgroundColor: value === "Medium gas" ? "#87BEDE" : "transparent"}}>Medium gas</Option>
          <Option value="High gas" style={{color:theme.textColor,backgroundColor: value === "High gas" ? "#87BEDE" : "transparent"}}>High gas</Option>
        </Select>
      )}
    </div>
  );

  return (
    <Layout className={`setting-layout ${themeClass}`} style={{marginTop: isMobile? '-70px':'' }}>
  {contextHolder}
      <Content className="setting-content">
        <Title level={2} className="setting-title">Setting</Title>
        
        {/* Profile Settings Section */}
        <div className="settings-section">
          <Title level={4} className="section-title">Profile Settings</Title>
          
          {/* Avatar Upload Section */}
          <div className="avatar-section">
           <Avatar size={80} src={avatarUrl} className="profile-avatar" />

            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Upload 
                {...uploadProps}
                className="white-upload-text"
                showUploadList={true}
              >
                <Button 
                  icon={<UploadOutlined />} 
                  className={`upload-button ${themeClass}`}
                >
                  选择文件
                </Button>
              </Upload>
              
              <Button
                type="primary"
                onClick={handleUpload}
                disabled={fileList.length === 0}
                loading={uploading}
                className={`upload-button ${themeClass}`}
              >
                {uploading ? '上传中' : '开始上传'}
              </Button>
              
              {uploadProgress > 0 && uploading && (
                <Progress percent={uploadProgress} style={{ width: '120px' }} />
              )}
            </div>
          </div>
          
          {renderFormField(
            "Username",
            username,
            (e: ChangeEvent<HTMLInputElement>) => setUsername(e.target.value),
            "Enter your username"
          )}
        </div>
        
        {/* Wallet Settings Section */}
        <div className="settings-section" style={{marginTop: '-40px'}}>
          <Title level={4} className="section-title">Wallet Settings</Title>
          
          {renderFormField(
            "RPC",
            rpcUrl,
            (e: ChangeEvent<HTMLInputElement>) => setRpcUrl(e.target.value),
            "Please Enter"
          )}
          
          {renderFormField(
            "Gas Price",
            gasPrice,
            setGasPrice,
            "",
            "select"
          )}
        </div>
        
        {/* Save Button */}
        <Button 
          className="save-button" 
          type="primary" 
          onClick={handleSave}
          loading={isLoading}
        >
          Save Changes
        </Button>
      </Content>
    </Layout>
  );
};

export default Setting; 