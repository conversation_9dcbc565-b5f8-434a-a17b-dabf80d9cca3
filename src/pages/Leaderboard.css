.leaderboard-container {
  padding: 0 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.dark-theme .leaderboard-container {
  background: transparent;
}

.light-theme .leaderboard-container {
  background: transparent;
}

.leaderboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.time-filter-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  gap: 10px;
}

.time-filter-btn {
  min-width: 80px;
}

.time-filter-btn.active {
  font-weight: bold;
}

.leaderboard-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  border: none;
  overflow: hidden;
}

.light-theme .leaderboard-card {
  background-color: #fff;
  color: #333;
}

.dark-theme .leaderboard-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  color: #f8fafc;
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.leaderboard-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
  padding: 4px 0;
}

.leaderboard-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: none;
  margin-bottom: 8px;
}

.leaderboard-item:hover {
  background: rgba(71, 85, 105, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.dark-theme .leaderboard-item:hover {
  background: rgba(71, 85, 105, 0.2);
}

.leaderboard-rank-avatar {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.rank-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-weight: 700;
  font-size: 14px;
  color: #000;
  margin-right: 12px;
  position: relative;
  z-index: 1;
}

/* Rank-specific colors */
.rank-badge.rank-1 {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.rank-badge.rank-2 {
  background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
  box-shadow: 0 2px 8px rgba(192, 192, 192, 0.3);
}

.rank-badge.rank-3 {
  background: linear-gradient(135deg, #CD7F32, #B87333);
  box-shadow: 0 2px 8px rgba(205, 127, 50, 0.3);
}

.rank-badge.rank-other {
  background: linear-gradient(135deg, #4A5568, #2D3748);
  color: #fff;
  box-shadow: 0 2px 8px rgba(74, 85, 104, 0.3);
}

.username {
  margin-left: 12px;
  font-weight: 500;
  font-size: 16px;
  flex: 1;
  color: #f8fafc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.light-theme .username {
  color: #1e293b;
}

.amount {
  font-weight: 600;
  font-size: 16px;
  color: #94a3b8;
  text-align: right;
  min-width: 100px;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.amount:hover {
  background: rgba(71, 85, 105, 0.1);
  transform: scale(1.02);
}

/* Amount tooltip styling */
.ant-tooltip.amount-tooltip .ant-tooltip-inner {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
  color: #f8fafc !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  backdrop-filter: blur(10px) !important;
}

.ant-tooltip.amount-tooltip .ant-tooltip-arrow::before {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

/* Add padding inside cards */
.ant-card-body {
  padding: 24px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .leaderboard-container {
    padding: 15px;
  }
  
  .time-filter-container {
    flex-wrap: wrap;
  }
  
  .leaderboard-item {
    padding: 12px 8px;
  }
  
  .username {
    max-width: 140px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .rank-badge {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
  
  .ant-card-body {
    padding: 16px !important;
  }
}

.countdown-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  padding: 10px 20px;
  border-radius: 20px;
  width: fit-content;
  margin: 0 auto 30px;
}

.light-theme .countdown-container {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.dark-theme .countdown-container {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.countdown-icon {
  font-size: 18px;
  margin-right: 8px;
}

.light-theme .countdown-icon {
  color: #1890ff;
}

.dark-theme .countdown-icon {
  color: #1890ff;
}

.countdown-text {
  font-size: 14px;
  font-weight: 500;
}

/* User Info Tooltip Styles */
.user-info-hover {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
  min-width: 0;
  transition: all 0.2s ease;
}

.user-info-hover:hover {
  transform: translateX(2px);
}

.user-tooltip {
  background: rgba(15, 23, 42, 0.95);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(71, 85, 105, 0.3);
  min-width: 240px;
  max-width: 280px;
}

.tooltip-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.tooltip-user-info {
  flex: 1;
}

.tooltip-username {
  color: #f8fafc;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.tooltip-joined {
  color: #94a3b8;
  font-size: 14px;
}

.tooltip-stats {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  word-break: break-all;
  overflow-wrap: break-word;
}

.stat-value.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

.stat-label {
  color: #94a3b8;
  font-size: 12px;
  text-transform: capitalize;
}

/* Override Antd tooltip styles */
.user-info-tooltip .ant-tooltip-inner {
  background: transparent !important;
  padding: 0 !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
}

.user-info-tooltip .ant-tooltip-arrow::before {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
} 