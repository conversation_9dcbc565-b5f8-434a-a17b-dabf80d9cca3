import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Layout, Card, Row, Col, Typography, Grid, Carousel, Spin, Empty, Button, message, Tabs, Popover, Checkbox, Select, Space } from 'antd';
import { useMediaQuery } from 'react-responsive';
import './Home.css';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useAppContext } from '../context/AppContext';
import { mergeGameWithConditions } from '../utils';
import { eventBus } from '../utils/eventBus';
import { DoubleRightOutlined, FilterOutlined } from '@ant-design/icons';

// Import downloaded assets (update paths as needed)
import eventTitleImg from '../assets/figma_images/eventTitleImg.png';
import welcomeBannerImage1 from '../assets/figma_images/welcomeBannerImage1.jpg';
// import welcomeBannerImage2 from '../assets/figma_images/welcomeBannerImage2.jpg';/

const { Content } = Layout;
const { Text, Paragraph  } = Typography;
const { useBreakpoint } = Grid;

// News categories for the filter bar
// const newsCategories = [
//   { key: 'all', label: 'All' },
//   { key: 'breaking', label: 'Breaking News' },
//   { key: 'trump', label: 'Trump Presidency' },
//   { key: 'bill', label: 'Big Beautiful Bill' },
//   { key: 'trade', label: 'Trade War' },
//   { key: 'elon', label: 'Trump vs Elon' },
//   { key: 'mayor', label: 'NYC Mayor' },
//   { key: 'diddy', label: 'Diddy' },
//   { key: 'iran', label: 'Iran' },
//   { key: 'geopolitics', label: 'Geopolitics' }
// ];

// 使用状态保存从API获取的新闻分类


// 游戏类型接口
interface GameType {
  id: number;
  name: string;
}

// 默认游戏类型
// const defaultGameTypes: GameType[] = [
//   { id: 0, name: 'All' },
//   { id: 1006, name: 'Sports' },
//   { id: 1007, name: 'Cryptos' },
//   { id: 1008, name: 'Politics' }
// ];

// 移除静态的bannerItems定义
// const bannerItems = [
//   {
//     key: '1',
//     image: welcomeBannerImage1,
//     url: 'https://www.baidu.com',
//   },
//   {
//     key: '2',
//     image: welcomeBannerImage2,
//     url: 'https://www.baidu.com',
//   }
// ];

// 升级版环形进度条组件，仿照图片样式
const ChanceArc = ({ percent }: { percent: number }) => {
  const radius = 28;
  const stroke = 5;
  const center = radius + stroke;
  const totalDegree = 210; // 总弧度角度
  const totalAngle = totalDegree * Math.PI / 180; // 230度的弧度
  // const gapAngle = 12; // 主断开角度
  const smallGapAngle = 13; // 彩色和灰色之间的小断开角度

  // 以12点钟方向为0，逆时针为正
  const startAngle = (360 - totalDegree) / 2 * Math.PI / 180; // 起点角度（弧度）
  const endAngle = (360 - (360 - totalDegree) / 2) * Math.PI / 180; // 终点角度（弧度）

  const percentAngle = totalAngle * (percent / 100);

  // 颜色区分
  let arcColor = '#E74801';
  if (percent >= 66) arcColor = '#25AE60';
  else if (percent >= 33) arcColor = '#F5A623';

  const percentText = `${percent}%`;

  // 极坐标转笛卡尔
  const polarToCartesian = (angle: number) => {
    const x = center + radius * Math.cos(angle - Math.PI / 2 + Math.PI);
    const y = center + radius * Math.sin(angle - Math.PI / 2 + Math.PI);
    return { x, y };
  };

  // 有色弧线
  const arcStart = polarToCartesian(startAngle);
  const arcEnd = polarToCartesian(startAngle + percentAngle);
  const arcLargeArcFlag = percentAngle > Math.PI ? 1 : 0;
  const arcPath = `M ${arcStart.x},${arcStart.y} A ${radius},${radius} 0 ${arcLargeArcFlag} 1 ${arcEnd.x},${arcEnd.y}`;

  // 灰色弧线
  const bgStartAngle = startAngle + percentAngle + (smallGapAngle * Math.PI / 180);
  const bgEndAngle = endAngle;
  const bgStart = polarToCartesian(bgStartAngle);
  const bgEnd = polarToCartesian(bgEndAngle);
  const bgLargeArcFlag = (totalAngle - percentAngle - (smallGapAngle * Math.PI / 180)) > Math.PI ? 1 : 0;
  const bgPath = `M ${bgStart.x},${bgStart.y} A ${radius},${radius} 0 ${bgLargeArcFlag} 1 ${bgEnd.x},${bgEnd.y}`;

  return (
    <div style={{ position: 'relative', width: 2 * (radius + stroke), height: 2 * (radius + stroke) }}>
      <svg width={2 * (radius + stroke)} height={2 * (radius + stroke)}>
        {/* 灰色弧线（剩余部分） */}
        {percent < 100 && (
          <path
            d={bgPath}
            fill="none"
            stroke="#888C96"
            strokeWidth={stroke}
            strokeLinecap="round"
          />
        )}
        {/* 有色弧线 */}
        {percent > 0 && (
          <path
            d={arcPath}
            fill="none"
            stroke={arcColor}
            strokeWidth={stroke}
            strokeLinecap="round"
          />
        )}
      </svg>
      {/* 百分比和chance文字，绝对定位在弧形正中间 */}
      <div style={{
        position: 'absolute',
        left: 0,
        top: '0%',
        width: 2 * (radius + stroke),
        height: 2 * (radius + stroke),
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        pointerEvents: 'none'
      }}>
        <span style={{ color: '#fff', fontWeight: 600, fontSize: 13, lineHeight: 1 }}>{percentText}</span>
        <span style={{ color: '#8C939F', fontSize: 10, lineHeight: 1, marginTop: 2 }}>chance</span>
      </div>
    </div>
  );
};
const HomePage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme, themeClass } = useAppContext();
  const screens = useBreakpoint();
  const isMobileMediaQuery = useMediaQuery({ maxWidth: 768 });
  const isMobile = !screens.md || isMobileMediaQuery;
  const carouselRef = useRef<any>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  
  // News filter state
  const [activeNewsCategory, setActiveNewsCategory] = useState<string>('all');
  
  // Filter popover state
  const [filterVisible, setFilterVisible] = useState(false);
  const [sortBy, setSortBy] = useState<string>('volume_24h');
  const [frequency, setFrequency] = useState<string>('All');
  const [hideSportsMarkets, setHideSportsMarkets] = useState(false);
  const [hideCryptoMarkets, setHideCryptoMarkets] = useState(false);
  const [newsCategories, setNewsCategories] = useState<{key: string, label: string}[]>([
    { key: 'all', label: 'All' } // 默认总是有"全部"选项
  ]);
  const [newsCategoriesLoading, setNewsCategoriesLoading] = useState(false);
  // 添加活动数据状态
  const [activities, setActivities] = useState<any[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(false);
  
  // 用于无限滚动的状态
  const [displayedData, setDisplayedData] = useState<any[]>([]);
  const [page, setPage] = useState(0);
  const [pageSize] = useState(10);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  // 添加初始化加载标志，防止重复加载
  const [isInitialized, setIsInitialized] = useState(false);
  // 添加搜索状态
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  
  // 游戏类型状态
  // const [gameTypes, setGameTypes] = useState<GameType[]>(defaultGameTypes);
  
  // 添加防抖计时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [messageApi, contextHolder] = message.useMessage();
  
  // 从URL中获取当前选中的分类和搜索关键词
  const getInitialCategory = () => {
    const categoryId = searchParams.get('categoryId');
    const categoryName = searchParams.get('category');
    const search = searchParams.get('search');
    
    // 如果有搜索关键词，设置搜索状态
    if (search) {
      setSearchKeyword(search);
    }
    
    // 优先使用categoryId
    if (categoryId) {
      const id = parseInt(categoryId);
      return { id, name: categoryName || 'Unknown' };
    }
    
    // 如果没有categoryId但有category名称
    // if (categoryName) {
    //   const foundType = gameTypes.find(type => type.name === categoryName);
    //   if (foundType) {
    //     return foundType;
    //   }
    // }
    
    // 默认返回All
    return { id: 0, name: 'All' };
  };
  
  // 添加分类选择状态，从URL初始化
  const [selectedCategory, setSelectedCategory] = useState<GameType>(() => getInitialCategory());
  // 在组件初始化时设置 message 的全局配置
  const errorMsg = (msg :String) => {
    messageApi.open({
      type: 'error',
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  
  // 当URL参数变化时更新选中的分类
  useEffect(() => {
    const category = getInitialCategory();
    const search = searchParams.get('search');
    
    // 如果搜索关键词变化或分类变化，重置状态并重新加载数据
    if ((search !== searchKeyword) || 
        (category.id !== selectedCategory.id || category.name !== selectedCategory.name)) {
      
      if (search !== searchKeyword) {
        setSearchKeyword(search || '');
      }
      
      if (category.id !== selectedCategory.id || category.name !== selectedCategory.name) {
        setSelectedCategory(category);
      }
      
      // 重置分页并重新加载数据
      setPage(0);
      setDisplayedData([]);
      setHasMore(true);
      setError(null);
      // 重置初始化标志，以便重新加载数据
      setIsInitialized(false);
    }
  }, [searchParams, location.search]);
  
  // 获取游戏类型数据
  // useEffect(() => {
  //   const fetchGameTypes = async () => {
  //     try {
  //       const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/gametype/all`);
  //       const data = await response.json();
        
  //       if (data && data.code === 200 && Array.isArray(data.data)) {
  //         const types = data.data.map((type: any) => ({
  //           id: type.id,
  //           name: type.name || ''
  //         }));
          
  //         // 确保始终有"All"分类
  //         const allTypes = [{ id: 0, name: 'All' }, ...types];
  //         setGameTypes(allTypes);
  //       }
  //     } catch (error) {
  //       console.error('获取游戏类型出错:', error);
  //     }
  //   };
    
  //   fetchGameTypes();
  // }, []);

  // 加载游戏数据
  const loadGameData = async () => {
    if (loading || !hasMore) return;
    
    // 防抖处理，避免短时间内多次调用
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(async () => {
      setLoading(true);
      setError(null);
      
      try {
        // 构建API请求参数
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('count', pageSize.toString());
        params.append('lang', i18n.language);
        
        // 添加新闻分类过滤参数
        if (activeNewsCategory !== 'all') {
          params.append('category', activeNewsCategory);
        }
        
        // 添加排序参数
        params.append('sort_by', sortBy);
        
        // 添加频率过滤
        if (frequency !== 'All') {
          params.append('frequency', frequency);
        }
        
        // 添加隐藏体育市场参数
        if (hideSportsMarkets) {
          params.append('hide_sports', 'true');
        }
        
        // 添加隐藏加密货币市场参数
        if (hideCryptoMarkets) {
          params.append('hide_crypto', 'true');
        }
        
        // 如果有搜索关键词，使用搜索API
        if (searchKeyword) {
          const searchUrl = `${import.meta.env.VITE_API_URL}/v1/game/search?keyword=${encodeURIComponent(searchKeyword)}&lang=${i18n.language}`;
          const searchResponse = await fetch(searchUrl);
          
          if (!searchResponse.ok) {
            throw new Error(`Search API responded with status: ${searchResponse.status}`);
          }
          
          const searchData = await searchResponse.json();
          
          if (!searchData || !searchData.code || searchData.code !== 200 || !Array.isArray(searchData.data)) {
            setHasMore(false);
            return;
          }
          
          const gamesList = searchData.data;
          
          // 如果没有游戏数据，设置hasMore为false并返回
          if (gamesList.length === 0) {
            setHasMore(false);
            return;
          }
          
          // 提取游戏ID并用逗号连接
          const gameIds = gamesList.map((game: any) => game.id).join(',');
          
          // 获取条件数据并合并
          if (gameIds) {
            const conditionsUrl = `${import.meta.env.VITE_API_URL}/v1/game/conditions/released?id=${gameIds}`;
            const conditionsResponse = await fetch(conditionsUrl);
            
            if (!conditionsResponse.ok) {
              throw new Error(`Conditions API responded with status: ${conditionsResponse.status}`);
            }
            
            const conditionsData = await conditionsResponse.json();
            
            if (conditionsData && conditionsData.code === 200 && Array.isArray(conditionsData.data)) {
              // 合并游戏数据和条件数据
              const mergedData = mergeGameWithConditions(gamesList, conditionsData.data);
              
              // 更新显示数据
              setDisplayedData(prev => [...prev, ...mergedData]);
              
              // 搜索结果一次性返回，不需要分页
              setHasMore(false);
            } else {
              // 如果没有条件数据，仍然显示游戏数据
              const mergedData = gamesList.map((game: any) => ({
                ...game,
                conditions: [],
                isGameCondition: false,
                chance: 0,
                yesBuyPrice:0
              }));
              
              setDisplayedData(prev => [...prev, ...mergedData]);
              setHasMore(false);
            }
          } else {
            setHasMore(false);
          }
          
          return;
        }
        
        // 如果选择了特定分类（不是All），则添加game_type参数
        if (selectedCategory.id > 0) {
          params.append('game_type', selectedCategory.id.toString());
        }
        
        // 根据sortBy使用不同的API
        let apiUrl = '';
        if (sortBy && ['create_time', 'expire_time', 'total_volume', 'volume_24h', 'liquidity'].includes(sortBy)) {
          // 使用筛选API
          apiUrl = `${import.meta.env.VITE_API_URL}/v1/game/released/filter?sort=${sortBy}&page=${page}&count=${pageSize}`;
        } else {
          // 使用普通API
          apiUrl = `${import.meta.env.VITE_API_URL}/v1/game/released?${params.toString()}`;
        }
        
        // 第一个API请求：获取游戏列表
        const gamesResponse = await fetch(apiUrl);
        
        if (!gamesResponse.ok) {
          throw new Error(`Games API responded with status: ${gamesResponse.status}`);
        }
        
        const gamesData = await gamesResponse.json();
        
        if (!gamesData || !gamesData.code || gamesData.code !== 200 || !Array.isArray(gamesData.data)) {
          setHasMore(false);
          return;
        }
        
        const gamesList = gamesData.data;
        
        // 如果没有游戏数据，设置hasMore为false并返回
        if (gamesList.length === 0) {
          setHasMore(false);
          return;
        }
        
        // 提取游戏ID并用逗号连接
        const gameIds = gamesList.map((game: any) => game.id).join(',');
        
        // 第二个API请求：获取条件数据
        if (gameIds) {
          const conditionsUrl = `${import.meta.env.VITE_API_URL}/v1/game/conditions/released?id=${gameIds}`;
          const conditionsResponse = await fetch(conditionsUrl);
          
          if (!conditionsResponse.ok) {
            throw new Error(`Conditions API responded with status: ${conditionsResponse.status}`);
          }
          
          const conditionsData = await conditionsResponse.json();
          
          if (conditionsData && conditionsData.code === 200 && Array.isArray(conditionsData.data)) {
            // 合并游戏数据和条件数据
            const mergedData = mergeGameWithConditions(gamesList, conditionsData.data);
            
            // 更新显示数据
            setDisplayedData(prev => [...prev, ...mergedData]);
            
            // 如果返回的数据少于请求的数量，说明没有更多数据了
            setHasMore(gamesList.length >= pageSize);
            
            // 更新页码
            setPage(prev => prev + 1);
          } else {
            // 如果没有条件数据，仍然显示游戏数据
            const mergedData = gamesList.map((game: any) => ({
              ...game,
              conditions: [],
              isGameCondition: false,
              chance: 0,
              yesBuyPrice:0
            }));
            
            setDisplayedData(prev => [...prev, ...mergedData]);
            setHasMore(gamesList.length >= pageSize);
            setPage(prev => prev + 1);
          }
        } else {
          setHasMore(false);
        }
      } catch (err) {
        console.error('加载数据失败:', err);
        setError(t('home.loadingError'));
        errorMsg(t('home.loadingError'));
        setHasMore(false);
        // message.error('加载数据失败，请稍后重试');
      } finally {
        setLoading(false);
        debounceTimerRef.current = null;
      }
    }, 300); // 300ms防抖延迟
  };

  // 首次加载数据
  useEffect(() => {
    // 如果已经初始化过，不需要再次加载
    if (isInitialized) return;
    
    setDisplayedData([]);
    setPage(0);
    setHasMore(true);
    loadGameData();
    // setNewsCategories([{ key: 'all', label: 'All' }]);
    fetchNewsCategories();
    setActiveNewsCategory('all');
    // 标记已初始化
    setIsInitialized(true);
  }, [selectedCategory, isInitialized, searchKeyword, 
      sortBy, frequency, hideSportsMarkets, hideCryptoMarkets]);

  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = () => {
      setDisplayedData([]);
      setPage(0);
      setHasMore(true);
      setIsInitialized(false);
      loadGameData();
      fetchNewsCategories();
      setActiveNewsCategory('all');
    };

    // 订阅语言变化事件
    eventBus.on('languageChanged', handleLanguageChange);

    // 清理订阅
    return () => {
      eventBus.off('languageChanged', handleLanguageChange);
    };
  }, []);

  // 设置无限滚动监听
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && !loading && hasMore && isInitialized) {
          // 根据当前活跃的筛选类型调用对应的加载函数
          if (activeNewsCategory !== 'all') {
            // 如果选择了特定标签，加载更多标签数据
            loadMoreTagData();
          } else {
            // 否则使用常规加载函数
            loadGameData();
          }
        }
      },
      { threshold: 0.1 }
    );
    
    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }
    
    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loading, hasMore, isInitialized, activeNewsCategory]);

  // 获取活动数据
  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setActivitiesLoading(true);
        const response = await fetch(`${import.meta.env.VITE_THIRD_BASE_URL}/v1/activities/active`);
        
        if (!response.ok) {
          throw new Error(`Activities API responded with status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data && data.status === 'success' && Array.isArray(data.data.activities)) {
          setActivities(data.data.activities);
        } else {
          console.error('获取活动数据格式错误:', data);
        }
      } catch (error) {
        console.error('获取活动数据出错:', error);
      } finally {
        setActivitiesLoading(false);
      }
    };
    
    fetchActivities();
  }, []);

  // 获取新闻分类数据
  useEffect(() => {
    
    fetchNewsCategories();
  }, [i18n.language,selectedCategory]); // 当语言变化时重新获取
  const fetchNewsCategories = async () => {
    try {
      setNewsCategoriesLoading(true);
      const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/tag/all?lang=all`);
      
      if (!response.ok) {
        throw new Error(`News Categories API responded with status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.code === 200 && Array.isArray(data.data)) {
        // 确定当前语言或默认使用英文
        const currentLang = i18n.language || 'en';
        
        // 将API返回的数据转换为需要的格式
        const categories = data.data
          .filter((tag: any) => tag.visable) // 只使用可见的标签
          .map((tag: any) => {
            // 获取当前语言的描述，如果没有则尝试使用英文，最后使用id作为回退
            const label = 
              (tag.description && tag.description[currentLang]) || 
              (tag.description && tag.description['en'])
            
            return {
              key: String(tag.id),
              label
            };
          });
        
        // 确保始终有"All"选项在最前面
        setNewsCategories([{ key: 'all', label: 'All' }, ...categories]);
        // setActiveNewsCategory('all');
      }
    } catch (error) {
      console.error('获取新闻分类出错:', error);
      // 确保即使API失败，也至少有"All"选项可用
      setNewsCategories([{ key: 'all', label: 'All' }]);
    } finally {
      setNewsCategoriesLoading(false);
    }
  };
  const renderCarouselItem = (item: any) => (
    <div className="carousel-item" key={item.id} onClick={() => window.open(item.link, '_blank')}>
      <div className="carousel-image-container">
        <img className="carousel-image" src={item.image_url} alt={item.title} />
      </div>
    </div>
  );
  
  const navigateToMarket = (marketId: string, option?: string, marketData?: any) => {
    console.log(marketData);
    navigate(`/market/${marketId}${option ? `?option=${option}` : ''}`, {
      state: { marketData }
    });
  };

  const [hoveredBtn, setHoveredBtn] = useState<{marketId: string, type: 'yes' | 'no'} | null>(null);


  const formatNumber = (value: number) => {
    const absValue = Math.abs(value)
    const sign = value < 0 ? '-' : ''
    
    if (absValue >= 1000000) {
      return `${sign}${(absValue / 1000000).toFixed(1)}M`
    } else if (absValue >= 1000) {
      return `${sign}${(absValue / 1000).toFixed(0)}K`
    }
    return value.toString()
  }

  const renderMarketCard = (market: any) => {
    const isSingle = market.conditions && market.conditions.length === 1;

    return (
      <Card 
        className={`market-card ${themeClass}`}
        style={{ borderColor: 'transparent', cursor: 'pointer' }}
        onClick={() => navigateToMarket(market.id, undefined, market)}
      >
        <div
          style={{
            width: '100%',
            height: 180, // 统一卡片高度
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            boxSizing: 'border-box'
          }}
        >
          <div className="card-header-info">
            <img src={market.icon_url || eventTitleImg} style={{ width: '45px', height: '45px' }} alt="Banner" />
            <Paragraph 
              ellipsis={{ rows: 2 }} 
              strong 
              className="market-description"
              style={{ 
                color: theme.textColor, 
                minHeight: 44, 
                fontSize: 14, 
                marginTop: 8, 
                marginBottom: 8 ,
                width: '100%',
                textAlign:'left'
              }}
            >
              {market.description}
            </Paragraph>
            <ChanceArc percent={market.conditions[0].yesBuyPrice/1e16} />
          </div>
          
          {/* 按钮和描述区域 */}
          {isSingle ? (
            <div style={{ width: '100%',height:'100%' }}>
              
              <div style={{ display: 'flex', gap: 8, width: '100%', marginBottom: 0, marginTop: 8 }}>
                <Button
                  className="yes-btn"
                  style={{
                    height: 36,
                    flex: 1,
                    minWidth: 0,
                    backgroundColor: '#25AE60',
                    borderColor: '#25AE60',
                    color: 'white',
                    maxHeight: 36,
                    minHeight: 36,
                    lineHeight: '36px',
                    marginTop: 20,
                    alignSelf: 'flex-start',
                    fontSize: 18
                  }}
                  onMouseEnter={() => setHoveredBtn({marketId: market.id, type: 'yes'})}
                  onMouseLeave={() => setHoveredBtn(null)}
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateToMarket(market.id, 'yes', market);
                  }}
                >
                  Buy Yes
                  <span
                    className="buy-yes-icon"
                    style={{
                      color: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'yes' ? '#fff' : '#25AE60',
                      marginLeft: 6,
                      transition: 'opacity 0.2s, color 0.2s',
                      opacity: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'yes' ? 0.3 : 1,
                      transform: 'rotate(270deg)',
                      animation: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'yes' ? 'icon-blink 0.7s infinite' : 'none'
                    }}
                  >
                    <DoubleRightOutlined />
                  </span>
                </Button>
                <Button
                  className="no-btn"
                  style={{
                    height: 36,
                    flex: 1,
                    minWidth: 0,
                    backgroundColor: '#E74801',
                    borderColor: '#E74801',
                    color: 'white',
                    maxHeight: 36,
                    minHeight: 36,
                    lineHeight: '36px',
                    marginTop: 20,
                    alignSelf: 'flex-start',
                    fontSize: 18
                  }}
                  onMouseEnter={() => setHoveredBtn({marketId: market.id, type: 'no'})}
                  onMouseLeave={() => setHoveredBtn(null)}
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateToMarket(market.id, 'no', market);
                  }}
                >
                  Buy No
                  <span
                    className="buy-no-icon"
                    style={{
                      color: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'no' ? '#fff' : '#E74801',
                      marginLeft: 6,
                      transition: 'opacity 0.2s, color 0.2s',
                      opacity: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'no' ? 0.3 : 1,
                      transform: 'rotate(90deg)',
                      animation: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'no' ? 'icon-blink 0.7s infinite' : 'none'
                    }}
                  >
                    <DoubleRightOutlined />
                  </span>
                </Button>
              </div>
              <div
                style={{
                  width: '100%',
                  height: 0,
                  background: theme.name === 'dark' ? '#2C3F4F' : '#E7E7E7',
                  margin: '30px 0 2px 0'
                }}
              />
              <div
                style={{
                  width: '100%',
                  textAlign: 'left',
                  color: '#8C939F',
                  fontSize: 12,
                  lineHeight: '16px',
                  marginTop: 2,
                  marginBottom: 0,
                  paddingLeft: 2
                }}
              >
                {'$ '+ formatNumber(market.vol/1e18) + ' Vol.'}
              </div>
            </div>
          ) : (
            <div style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
                <div style={{ display: 'flex', width: '65%', flexDirection: 'column' }}>
                  {market.conditions &&
                    market.conditions.slice(0, 3).map((condition: any, index: number) => (
                      <Text
                        key={`price-${index}`}
                        className="price-text"
                        style={{
                          color: theme.textColor,
                          fontSize: 12,
                          height: 22,
                          lineHeight: '22px',
                          marginTop: index > 0 ? 4 : 0,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: 'block'
                        }}
                      >
                        {condition.description}
                      </Text>
                    ))}
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-end',
                    height: 77,
                    overflow: 'hidden'
                  }}
                >
                  {market.conditions &&
                    market.conditions.slice(0, 3).map((condition: any, index: number) => (
                      <div key={`btn-${index}`} style={{ display: 'flex', gap: 4, marginTop: index > 0 ? 4 : 0 }}>
                        <Button
                          className="yes-btn"
                          style={
                            condition.yes_coin_price > 50
                              ? {
                                  backgroundColor: '#25AE60',
                                  borderColor: '#25AE60',
                                  color: 'white',
                                  height: 36,
                                  minWidth: 0,
                                  flex: 1
                                }
                              : { height: 36, minWidth: 0, flex: 1 }
                          }
                          onClick={(e) => {
                            e.stopPropagation();
                            navigateToMarket(market.id, 'yes', market);
                          }}
                        >
                          Yes
                        </Button>
                        <Button
                          className="no-btn"
                          style={
                            condition.no_coin_price > 50
                              ? {
                                  backgroundColor: '#E74801',
                                  borderColor: '#E74801',
                                  color: 'white',
                                  height: 36,
                                  minWidth: 0,
                                  flex: 1
                                }
                              : { height: 36, minWidth: 0, flex: 1 }
                          }
                          onClick={(e) => {
                            e.stopPropagation();
                            navigateToMarket(market.id, 'no', market);
                          }}
                        >
                          No
                        </Button>
                      </div>
                    ))}
                </div>
              </div>
              <div
                style={{
                  width: '100%',
                  height: 1,
                  background: theme.name === 'dark' ? '#2C3F4F' : '#E7E7E7',
                  margin: '8px 0 2px 0'
                }}
              />
              <div
                style={{
                  width: '100%',
                  textAlign: 'left',
                  color: '#8C939F',
                  fontSize: 12,
                  fontWeight: 100,
                  lineHeight: '16px',
                  marginTop: 2,
                  marginBottom: 0,
                  paddingLeft: 2
                }}
              >
                {'$ '+formatNumber(market.vol/1e18) + ' Vol.'}
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  };

  // 处理新闻分类过滤
  const handleNewsCategoryChange = (category: string) => {
    setActiveNewsCategory(category);
    // 重置分页并重新加载数据
    setPage(0);
    setDisplayedData([]);
    setHasMore(true);
    setError(null);
    // setIsInitialized(false);
    
    if (category !== 'all') {
      // 从标签API获取数据
      const fetchTagData = async () => {
        setLoading(true);
        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/game/released/tag?tag=${category}&page=0&count=${pageSize}`);
          
          if (!response.ok) {
            throw new Error(`Tag API responded with status: ${response.status}`);
          }
          
          const data = await response.json();
          
          if (!data || !data.code || data.code !== 200 || !Array.isArray(data.data)) {
            setHasMore(false);
            setLoading(false);
            return;
          }
          
          const gamesList = data.data;
          
          // 如果没有游戏数据，设置hasMore为false并返回
          if (gamesList.length === 0) {
            setHasMore(false);
            setLoading(false);
            return;
          }
          
          // 提取游戏ID并用逗号连接
          const gameIds = gamesList.map((game: any) => game.id).join(',');
          
          // 获取条件数据并合并
          if (gameIds) {
            const conditionsUrl = `${import.meta.env.VITE_API_URL}/v1/game/conditions/released?id=${gameIds}`;
            const conditionsResponse = await fetch(conditionsUrl);
            
            if (!conditionsResponse.ok) {
              throw new Error(`Conditions API responded with status: ${conditionsResponse.status}`);
            }
            
            const conditionsData = await conditionsResponse.json();
            
            if (conditionsData && conditionsData.code === 200 && Array.isArray(conditionsData.data)) {
              // 合并游戏数据和条件数据
              const mergedData = mergeGameWithConditions(gamesList, conditionsData.data);
              
              // 更新显示数据
              setDisplayedData(mergedData);
              
              // 检查是否有更多数据
              setHasMore(gamesList.length >= pageSize);
              
              // 更新页码
              setPage(1);
            } else {
              // 如果没有条件数据，仍然显示游戏数据
              const mergedData = gamesList.map((game: any) => ({
                ...game,
                conditions: [],
                isGameCondition: false,
                chance: 0,
                yesBuyPrice: 0
              }));
              
              setDisplayedData(mergedData);
              setHasMore(gamesList.length >= pageSize);
              setPage(1);
            }
          } else {
            setHasMore(false);
          }
        } catch (err) {
          console.error('加载标签数据失败:', err);
          setError(t('home.loadingError'));
          errorMsg(t('home.loadingError'));
        } finally {
          setLoading(false);
          setIsInitialized(true);
        }
      };
      
      fetchTagData();
    } else {
      // 如果选择了"全部"分类，调用普通加载函数
      loadGameData();
    }
  };
  
  // 加载更多标签数据
  const loadMoreTagData = async () => {
    if (loading || !hasMore || activeNewsCategory === 'all') return;
    
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/game/released/tag?tag=${activeNewsCategory}&page=${page}&count=${pageSize}`);
      
      if (!response.ok) {
        throw new Error(`Tag API responded with status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (!data || !data.code || data.code !== 200 || !Array.isArray(data.data)) {
        setHasMore(false);
        setLoading(false);
        return;
      }
      
      const gamesList = data.data;
      
      // 如果没有更多游戏数据，设置hasMore为false并返回
      if (gamesList.length === 0) {
        setHasMore(false);
        setLoading(false);
        return;
      }
      
      // 提取游戏ID并用逗号连接
      const gameIds = gamesList.map((game: any) => game.id).join(',');
      
      // 获取条件数据并合并
      if (gameIds) {
        const conditionsUrl = `${import.meta.env.VITE_API_URL}/v1/game/conditions/released?id=${gameIds}`;
        const conditionsResponse = await fetch(conditionsUrl);
        
        if (!conditionsResponse.ok) {
          throw new Error(`Conditions API responded with status: ${conditionsResponse.status}`);
        }
        
        const conditionsData = await conditionsResponse.json();
        
        if (conditionsData && conditionsData.code === 200 && Array.isArray(conditionsData.data)) {
          // 合并游戏数据和条件数据
          const mergedData = mergeGameWithConditions(gamesList, conditionsData.data);
          
          // 追加显示数据
          setDisplayedData(prev => [...prev, ...mergedData]);
          
          // 检查是否有更多数据
          setHasMore(gamesList.length >= pageSize);
          
          // 更新页码
          setPage(prev => prev + 1);
        } else {
          // 如果没有条件数据，仍然显示游戏数据
          const mergedData = gamesList.map((game: any) => ({
            ...game,
            conditions: [],
            isGameCondition: false,
            chance: 0,
            yesBuyPrice: 0
          }));
          
          setDisplayedData(prev => [...prev, ...mergedData]);
          setHasMore(gamesList.length >= pageSize);
          setPage(prev => prev + 1);
        }
      } else {
        setHasMore(false);
      }
    } catch (err) {
      console.error('加载更多标签数据失败:', err);
      setError(t('home.loadingError'));
      errorMsg(t('home.loadingError'));
    } finally {
      setLoading(false);
    }
  };

  // 修改点击事件处理函数
  const handleCategoryClick = (category: string) => {
    handleNewsCategoryChange(category);
  };

  // 处理筛选条件变化
  const handleFilterChange = () => {
    // 重置分页并重新加载数据
    setPage(0);
    setDisplayedData([]);
    setHasMore(true);
    setError(null);
    // setIsInitialized(false);
    
    // 使用筛选API获取数据
    const fetchFilteredData = async () => {
      setLoading(true);
      try {
        // 构建API请求参数
        const params = new URLSearchParams();
        if (sortBy) {
          params.append('sort', sortBy);
        }
        params.append('page', '0'); // 首次加载从第0页开始
        params.append('count', pageSize.toString()); // 使用当前的pageSize
        
        const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/game/released/filter?${params.toString()}`);
        
        if (!response.ok) {
          throw new Error(`Filter API responded with status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data || !data.code || data.code !== 200 || !Array.isArray(data.data)) {
          setHasMore(false);
          setLoading(false);
          return;
        }
        
        const gamesList = data.data;
        
        // 如果没有游戏数据，设置hasMore为false并返回
        if (gamesList.length === 0) {
          setHasMore(false);
          setLoading(false);
          return;
        }
        
        // 提取游戏ID并用逗号连接
        const gameIds = gamesList.map((game: any) => game.id).join(',');
        
        // 获取条件数据并合并
        if (gameIds) {
          const conditionsUrl = `${import.meta.env.VITE_API_URL}/v1/game/conditions/released?id=${gameIds}`;
          const conditionsResponse = await fetch(conditionsUrl);
          
          if (!conditionsResponse.ok) {
            throw new Error(`Conditions API responded with status: ${conditionsResponse.status}`);
          }
          
          const conditionsData = await conditionsResponse.json();
          
          if (conditionsData && conditionsData.code === 200 && Array.isArray(conditionsData.data)) {
            // 合并游戏数据和条件数据
            const mergedData = mergeGameWithConditions(gamesList, conditionsData.data);
            
            // 更新显示数据
            setDisplayedData(mergedData);
            
            // 检查是否有更多数据可加载
            setHasMore(gamesList.length >= pageSize);
            
            // 更新页码，为后续加载做准备
            setPage(1); // 下一页从1开始
          } else {
            // 如果没有条件数据，仍然显示游戏数据
            const mergedData = gamesList.map((game: any) => ({
              ...game,
              conditions: [],
              isGameCondition: false,
              chance: 0,
              yesBuyPrice: 0
            }));
            
            setDisplayedData(mergedData);
            setHasMore(gamesList.length >= pageSize);
            setPage(1);
          }
        } else {
          setHasMore(false);
        }
      } catch (err) {
        console.error('加载筛选数据失败:', err);
        setError(t('home.loadingError'));
        errorMsg(t('home.loadingError'));
      } finally {
        setLoading(false);
        setIsInitialized(true);
      }
    };
    
    fetchFilteredData();
  };

  return (
    <Content>
      {/* News Filter Bar */}
      <div className="news-filter-bar" style={{
        display: 'flex',
        overflowX: 'auto',
        padding: '10px 0',
        backgroundColor: theme.name === 'dark' ? '#1e2a38' : '#f0f2f5',
        borderRadius: '8px',
        marginBottom: '16px',
        WebkitOverflowScrolling: 'touch',
        msOverflowStyle: 'none',
        scrollbarWidth: 'none',
        position: 'relative',
      }}>
        {/* Filter Icon */}
        <div 
          className="filter-icon-container" 
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '0 16px',
            cursor: 'pointer',
            color: filterVisible ? '#1890ff' : (theme.name === 'dark' ? '#fff' : '#000'),
          }}
          onClick={() => setFilterVisible(!filterVisible)}
        >
          <FilterOutlined style={{ fontSize: 18 }} />
        </div>

        {newsCategoriesLoading ? (
          <div style={{ padding: '8px 16px' }}>
            <Spin size="small" />
          </div>
        ) : (
          newsCategories.map(category => (
            <div
              key={category.key}
              className={`news-category-item ${activeNewsCategory === category.key ? 'active' : ''}`}
              onClick={() => handleCategoryClick(category.key)}
              style={{
                padding: '8px 16px',
                margin: '0 4px',
                whiteSpace: 'nowrap',
                cursor: 'pointer',
                borderRadius: '16px',
                backgroundColor: activeNewsCategory === category.key 
                  ? (theme.name === 'dark' ? '#3a4a5a' : '#e6f7ff') 
                  : 'transparent',
                color: activeNewsCategory === category.key 
                  ? (theme.name === 'dark' ? '#fff' : '#1890ff') 
                  : (theme.name === 'dark' ? '#8c939f' : '#595959'),
                fontWeight: activeNewsCategory === category.key ? 500 : 400,
                transition: 'all 0.3s ease'
              }}
            >
              {category.label}
            </div>
          ))
        )}
      </div>

      {/* Filter Options */}
      {filterVisible && (
        <div 
          className="filter-options-container"
          style={{
            padding: '12px 16px',
            backgroundColor: theme.name === 'dark' ? '#1e2a38' : '#f0f2f5',
            borderRadius: '8px',
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            flexWrap: 'wrap'
          }}
        >
          <div>
            <Typography>sort by:
            <Select
              value={sortBy}
              onChange={(value) => {
                setSortBy(value);
                handleFilterChange();
              }}
              style={{ width: '150px', background:'transparent',border:'none' }}
              options={[
                { value: 'create_time', label: t('home.sortBy.createTime') },
                { value: 'expire_time', label: t('home.sortBy.expireTime') },
                { value: 'total_volume', label: t('home.sortBy.totalVolume') },
                { value: 'volume_24h', label: t('home.sortBy.volume24h') },
                { value: 'liquidity', label: t('home.sortBy.liquidity') }
              ]}
            />
            </Typography>
          </div>
          
          
        </div>
      )}

      {/* 如果有搜索关键词，显示搜索结果标题 */}
      {searchKeyword && (
        <div
          style={{
            margin: isMobile ? "16px 16px 8px" : "24px 0 16px",
            fontSize: "18px",
            fontWeight: "bold",
            color: theme.textColor,
          }}
        >
          {t("home.searchResults")}: "{searchKeyword}"
        </div>
      )}

      {contextHolder}

      {/* Welcome Carousel - 只在没有搜索时显示 */}
      {!searchKeyword && (
        <div
          className="carousel-container"
          style={{ margin: isMobile ? "0 16px 16px" : "0 0 24px" }}
        >
          <Carousel
            autoplay
            className="welcome-carousel"
            ref={carouselRef}
            dotPosition="bottom"
          >
            {activities.length > 0 ? (
              activities.map(renderCarouselItem)
            ) : activitiesLoading ? (
              <div className="carousel-loading">
                <Spin />
              </div>
            ) : (
              // 如果没有活动数据，显示默认图片
              <div className="carousel-item">
                <div className="carousel-image-container">
                  <img
                    className="carousel-image"
                    src={welcomeBannerImage1}
                    alt="Banner"
                  />
                </div>
              </div>
            )}
          </Carousel>
        </div>
      )}

      {/* Market Cards */}
      <Row gutter={[16, 16]} style={{ padding: isMobile ? "0 16px" : "0 0px" }}>
        {displayedData.map((market) => (
          <Col xs={24} sm={12} md={8} lg={6} key={market.id}>
            {renderMarketCard(market)}
          </Col>
        ))}
      </Row>
      {/* Error State */}
      {error && !loading && (
        <div style={{ textAlign: "center", margin: "20px 0", padding: "10px" }}>
          <Empty description={error} />
          <Button
            type="primary"
            onClick={() => {
              setError(null);
              loadGameData();
            }}
            style={{ marginTop: 16 }}
          >
{t('home.retry')}
          </Button>
        </div>
      )}

      {/* Load More Indicator */}
      {hasMore && (
        <div
          ref={loadMoreRef}
          style={{ textAlign: "center", margin: "20px 0", padding: "10px" }}
        >
          {loading && <Spin />}
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && displayedData.length === 0 && (
        <Empty
          description={t("home.noMarketsFound")}
          style={{ margin: "40px 0" }}
        />
      )}
    </Content>
  );
};

export default HomePage;