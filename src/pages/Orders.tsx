import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Layout, 
  Typography, 
  Table,  
  Button, 
  Drawer, 
  Row, 
  Col,
  Modal,
  Avatar,
  Grid,
  Menu,
  Spin,
  Empty,
  message,
  Select,
  Checkbox,
  Space
} from 'antd';
import { useMediaQuery } from 'react-responsive';
import moment from 'moment'
import { 
  CloseOutlined, 
  HomeOutlined,
  UnorderedListOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useTheme } from '../theme/ThemeProvider';
// import AppHeader from '../layout/Header';
import './Orders.css';
import { DrawerContent } from '../components/shared/MenuComponents';
import userAvatarImage from '../assets/figma_images/user_avatar_image.png';
import Rectangle from '../assets/figma_images/Rectangle.png';
import { useTranslation } from 'react-i18next';
import { ethers } from 'ethers';
// import Web3 from 'web3';
import { useUser } from '../context/UserContext';
// import { dappApi, orderApi,get} from '../services/request';
import { ORDER_API,API } from '../services/apiPaths';
// import toast, { Toaster } from 'react-hot-toast';

const { Content, Footer } = Layout;
const { Title, Text } = Typography;
const { useBreakpoint } = Grid;
const { Option } = Select;

// 模拟订单数据
// const ordersData = [
//   {
//     id: '1',
//     event: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//     conditions: 'Yes',
//     currency: 'India',
//     price: '31Cent/500/155USDT',
//     type: 'Sell',
//     time: 'May 2, 2025, 10:26 PM',
//     status: 'Waiting For Transaction',
//     statusCode: 'waiting'
//   },
//   {
//     id: '2',
//     event: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//     conditions: 'Yes',
//     currency: 'India',
//     price: '31Cent/500/155USDT',
//     type: 'Sell',
//     time: 'May 2, 2025, 10:26 PM',
//     status: 'Waiting For Transaction',
//     statusCode: 'waiting'
//   },
//   {
//     id: '3',
//     event: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//     conditions: 'Yes',
//     currency: 'India',
//     price: '31Cent/500/155USDT',
//     type: 'Sell',
//     time: 'May 2, 2025, 10:26 PM',
//     status: 'Waiting For Transaction',
//     statusCode: 'waiting'
//   },
//   {
//     id: '4',
//     event: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//     conditions: 'Yes',
//     currency: 'India',
//     price: '31Cent/500/155USDT',
//     type: 'Sell',
//     time: 'May 2, 2025, 10:26 PM',
//     status: 'Waiting For Transaction',
//     statusCode: 'waiting'
//   },
//   {
//     id: '5',
//     event: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//     conditions: 'No',
//     currency: 'India',
//     price: '31Cent/500/155USDT',
//     type: 'Sell',
//     time: 'May 2, 2025, 10:26 PM',
//     status: 'Terminated',
//     statusCode: 'terminated'
//   },
//   {
//     id: '6',
//     event: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//     conditions: 'No',
//     currency: 'India',
//     price: '31Cent/500/155USDT',
//     type: 'Sell',
//     time: 'May 2, 2025, 10:26 PM',
//     status: 'Terminated',
//     statusCode: 'terminated'
//   },
//   {
//     id: '7',
//     event: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//     conditions: 'No',
//     currency: 'India',
//     price: '31Cent/500/155USDT',
//     type: 'Sell',
//     time: 'May 2, 2025, 10:26 PM',
//     status: 'Partially Executed',
//     statusCode: 'partial'
//   }
// ];

// 基础订单详情模拟数据（不使用t函数）
// const baseOrderDetail = {
//   title: 'Which Countries Will The U.S. Agree To Trade Deals With Before July?',
//   conditions: 'RWD WIN',
//   currency: 'NO',
//   price: '30¢',
//   buyOrSell: 'Buy',
//   shares: '100',
//   fee: '$0.2',
//   totalAmount: '$30',
//   executedVolume: '0',
//   executedAmount: '0',
//   expectedEarnings: '$100',
//   status: 'Waiting for Transaction',
//   orderType: 'Limit Order',
//   orderId: '550e8400-e29b-41d4-a716-44665544000',
//   time: '2025-05-02 22:26:29'
// };

const Orders = () => {
  const { theme, toggleTheme } = useTheme();
  const navigate = useNavigate();
  const screens = useBreakpoint();
  const isMobileMediaQuery = useMediaQuery({ maxWidth: 768 });
  const isMobile = !screens.md || isMobileMediaQuery;
  const { i18n,t } = useTranslation();
  const currentLang = i18n.language || 'en';
  const { signer,betMessageContract,networkName,address } = useUser();
  
  // 使用t函数翻译订单详情
  // const orderDetail = {
  //   ...baseOrderDetail,
  //   title: "Which Countries Will The U.S. Agree To Trade Deals With Before July?",
  //   currency: t('default.no'),
  //   buyOrSell: t('default.buy'),
  //   status: t('myOrders.WaitingForTransaction'),
  //   orderType: "limitOrder"
  // };
  
  const [isLoggedIn, setIsLoggedIn] = useState(true);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [walletAddress, setWalletAddress] = useState('');
  const [ordersData, setOrdersData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  // const [isError, setIsError] = useState(false);
  // const [total, setTotal] = useState(0);
  const [isCancelModalVisible, setIsCancelModalVisible] = useState(false);
  const [orderToCancel, setOrderToCancel] = useState<any>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  // const [signer, setSigner] = useState<ethers.Signer | null>(null);
  // const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const pageSize = 10;

  // 用于无限滚动的状态
  // const [displayedData, setDisplayedData] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';
  
  // Deposit/Withdraw Modal状态
  const [depositModalVisible, setDepositModalVisible] = useState(false);
  const [currentTab, setCurrentTab] = useState("deposit"); // deposit or withdraw
  const [amount, setAmount] = useState<string>("");
  const [receivingAddress] = useState("0xB910d... ... 2de1b3270FED");
  const [contractAddress] = useState("0xB910d... ... 2de1b3270FED");
  
  // 添加一个标志来防止重复触发
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  const showMessage = (msg: String, type: 'success' | 'error' | 'warning' ) => {
    messageApi.open({
      type: type,
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  
  // 保留原有方法，但使用通用方法实现
  const errorMsg = (msg: String) => showMessage(msg, 'error');
  // const warningMsg = (msg: String) => showMessage(msg, 'warning');
  const successMsg = (msg: String) => showMessage(msg, 'success');

  // 初始化 signer
  // const initializeSigner = async () => {
  //   if (window.ethereum) {
  //     try {
  //       const provider = new BrowserProvider(window.ethereum);
  //       // setProvider(provider);
        
  //       await provider.send('eth_requestAccounts', []);

  //       const signer = await provider.getSigner();
  //       setSigner(signer);

  //       const address = await signer.getAddress();
  //       console.log('address:', address);
  //     } catch (error) {
  //       console.error('初始化 signer 失败:', error);
  //       message.error('初始化钱包失败');
  //     }
  //   } else {
  //     message.warning('请安装 MetaMask 或其他以太坊钱包');
  //   }
  // };

  // 监听钱包变化
  // useEffect(() => {
  //   if (window.ethereum) {
  //     console.log('selectedOrder:', selectedOrder);
  //     console.log('isError:', isError);
  //     console.log('total:', total);
  //     const handleAccountsChanged = async (accounts: string[]) => {
  //       if (accounts.length === 0) {
  //         setSigner(null);
  //         message.info('钱包已断开连接');
  //       } else {
  //         await initializeSigner();
  //       }
  //     };

  //     const handleChainChanged = () => {
  //       window.location.reload();
  //     };

  //     window.ethereum.on('accountsChanged', handleAccountsChanged);
  //     window.ethereum.on('chainChanged', handleChainChanged);

  //     initializeSigner();

  //     return () => {
  //       window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
  //       window.ethereum.removeListener('chainChanged', handleChainChanged);
  //     };
  //   }
  // }, []);


  // 获取钱包地址
  useEffect(() => {
    const getWalletAddress = async () => {
      try {
        if (window.ethereum) {
          // const web3 = new Web3(window.ethereum);
          // await (window.ethereum as any).request({ method: 'eth_requestAccounts' });
          // const accounts = await web3.eth.getAccounts();
          // const address = accounts[0];
          // console.log('Setting wallet address:', address);
          setWalletAddress(address);
        } else {
          // warningMsg('请安装MetaMask或其他以太坊钱包');
        }
      } catch (error) {
        console.error('获取钱包地址失败:', error);
       errorMsg('获取钱包地址失败');
      }
    };

    getWalletAddress();
  }, []);

  // 获取订单数据
  const fetchOrders = async (page: number = 1) => {
    if (!walletAddress) return;

    console.log('Fetching orders with page:', page);
    setIsLoading(true);
    try {
      // 判断是否有筛选条件被激活
      const hasActiveFilters = orderTypeFilter !== '-1' || 
                             orderStatusFilter !== '-1' || 
                             largeOrdersOnly;
      
      let response;
      
      // 只有当选择了筛选条件时才使用filter接口
      if (hasActiveFilters) {
        

        // 大额订单阈值，如果选中则设置为100，否则为-1表示全部
        const min_expected_revenue = largeOrdersOnly ? 100 : -1;

        // 构建API URL
        const apiUrl = `${import.meta.env.VITE_SECOND_BASE_URL}/orders/${walletAddress}/filter?side=${orderTypeFilter}&order_status=${orderStatusFilter}&min_expected_revenue=${min_expected_revenue}&page=${page}&size=${pageSize}`;
        
        response = await fetch(apiUrl);
      } else {
        // 使用原来的接口获取全部数据
        response = await fetch(`${import.meta.env.VITE_SECOND_BASE_URL}${ORDER_API.LIST}/${walletAddress}/${page}/${pageSize}`);
      }

      const data = await response.json();
      
      if (response && data.code === 0) {
        // 如果返回的数据列表为空，设置 hasMore 为 false 并返回
        if (!data.data.list || data.data.list.length === 0) {
          console.log('No data received, setting hasMore to false');
          setHasMore(false);
          if (page === 1) {
            setOrdersData([]);
          }
          return;
        }

        const conditionIds = data.data.list.map((order: any) => order.conditionId);
        try {
          const conditionResponse = await fetch(`${import.meta.env.VITE_API_URL}${API.BYCID}?cid=${conditionIds.join(',')}`);
          const data1 = await conditionResponse.json();
          
          const enhancedOrders = data.data.list.map((order: any) => {
            const condition = data1.data.find((c: any) => c.condition_cid === order.conditionId);
            const lang = i18n.language;
            // const description = condition?.game_description[lang]
            const descObj = JSON.parse(condition?.game_description || '{}');
            return {
              ...order,
              eventName: descObj[lang] || order.eventId,
              conditionName: condition?.condition_description || order.conditionId,
            };
          });

          if (page === 1) {
            setOrdersData(enhancedOrders);
          } else {
            setOrdersData(prev => [...prev, ...enhancedOrders]);
          }
          
          // setTotal(data.data.total || 0);
          setHasMore(enhancedOrders.length === pageSize);
        } catch (error) {
          console.error('获取条件数据失败:', error);
          if (page === 1) {
            setOrdersData(data.data.list || []);
          } else {
            setOrdersData(prev => [...prev, ...(data.data.list || [])]);
          }
          // setTotal(data.data.total || 0);
          setHasMore(data.data.list.length === pageSize);
        }
      } else {
        throw new Error(data.message || '请求失败');
      }
    } catch (error) {
      console.error('获取订单数据失败:', error);
      // setIsError(true);
      errorMsg('获取订单数据失败，请检查网络连接');
      setHasMore(false);
    } finally {
      setIsLoading(false);
      setLoading(false);
      setIsLoadingMore(false);
    }
  };

  // 监听钱包地址变化，重置分页并重新获取数据
  useEffect(() => {
    if (walletAddress) {
      console.log('Wallet address changed, resetting page to 1');
      setCurrentPage(1);
      setOrdersData([]);
      setHasMore(true);
      fetchOrders(1);
    }
  }, [walletAddress,currentLang]);

  // 监听页码变化，获取新数据
  useEffect(() => {
    console.log('Page changed to:', currentPage);
    if (currentPage > 1 && hasMore) {
      fetchOrders(currentPage);
    }
  }, [currentPage]);

  // 格式化订单数据
  const formattedOrders = ordersData.map((order: any) => {
    let statusText = '';
    let statusColor = '';

    if (order.orderStatus === 0) {
      statusText = t('myOrders.Completed');
      statusColor = 'blue';
    } else if (order.orderStatus === 1) {
      statusText = t('myOrders.Cancelled');
      statusColor = 'gray';
    } else if (order.orderStatus === 2) {
      statusText = t('myOrders.WaitingForTransaction');
      statusColor = 'green';
    } else if (order.orderStatus === 3) {
      statusText = t('myOrders.PartiallyExecuted');
      statusColor = 'orange';
    } else if (order.orderStatus === 4) {
      statusText = t('myOrders.Terminated');
      statusColor = 'black';
    }

    const orderType = Number(order.side) === 0 ? t("myOrders.sell") : t("myOrders.buy");
    const answerType = order.marketSide === 0 ? 'Yes' : 'No';

    const orderTime = new Date(order.createTime);
    
    const formattedTime = orderTime.toLocaleString(currentLang, {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    });

    return {
      key: String(order.id),
      id: order.id,
      orderId: order.orderId,
      event: `${order.eventId}`,
      condition: `${order.conditionId}`,
      eventName: order.eventName || `${order.eventId}`,
      conditionName: order.conditionName || `${order.conditionId}`,
      currency: `$${order.price/1000000000000000000}/${order.quantity}/$${(order.price * order.quantity) /1000000000000000000}`,
      time: formattedTime,
      status: statusText,
      statusColor: statusColor,
      type: orderType,
      answer: answerType,
      price: order.price /1000000000000000000,
      quantity: order.quantity,
      fee: order.fee / 1000000000000000000,
      expectedRevenue: order.expectedRevenue / 1000000000000000000,
      completedAmount: order.completedAmount,
      realRevenue: order.realRevenue / 1000000000000000000,
      orderTimestamp: order.orderTimestamp,
      failAmount: order.failAmount,
      orderStatus: order.orderStatus,
      side: order.side,
      marketSide: order.marketSide,
    };
  });

  // 桌面端表格列配置
  const columns = [
    {
      title: t('myOrders.events'),
      dataIndex: 'eventName',
      key: 'eventName',
      ellipsis: true,
      width: '27%',
      render: (text: string) => (
        <div style={{ width: '95%' }}>
          <Text strong style={{ 
            wordWrap: 'break-word',
            overflowWrap: 'break-word',
            wordBreak: 'break-all',
            whiteSpace: 'normal'
          }}>{text}</Text>
        </div>
      ),
    },
    {
      title: t('myOrders.conditions'),
      dataIndex: 'conditionName',
      key: 'conditionName',
      width: '10%',
      render: (text: string) => (
        <div className={`condition-tag ${text.toLowerCase()}`}>{text}</div>
      ),
    },
    {
      title: t('myOrders.currency'),
      dataIndex: 'answer',
      key: 'answer',
      width: '9%',
      render: (text: string) => (
        <div
          style={{
            display: 'inline-block',
            padding: '4px 12px',
            borderRadius: '16px',
            backgroundColor: text === 'Yes' ? '#C578FF40' : '#6A7EFF60',
            color: text === 'Yes' ? '#C578FF' : '#6A7EFF',
            fontWeight: 'bold',
            textAlign: 'center',
            minWidth: '60px',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', width: '200px' }}>{t('myOrders.PSLA')}</div>,
      dataIndex: 'currency',
      width: '20%',
      key: 'currency',
    },
    {
      title: t('myOrders.type'),
      dataIndex: 'type',
      width: '6%',
      key: 'type',
    },
    {
      title: t('myOrders.time'),
      dataIndex: 'time',
      key: 'time',
    },
    {
      title: t('myOrders.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: any) => (
        <div className={`status-indicator ${record.statusColor}`}>
          <span className="status-dot"></span>
          <Text className="status-text">{status}</Text>
        </div>
      ),
    },
    {
      title: '',
      key: 'action',
      width: 50,
      render: (_: any, record: any) =>
        (record.status === t('myOrders.WaitingForTransaction') || record.status === t('myOrders.PartiallyExecuted')) && (
          <Button 
            type="text" 
            icon={<img src={Rectangle} alt="view-detail" />} 
            className="view-detail-btn"
            onClick={(e) => showCancelConfirm(e, record)}
          />
        ),
    },
  ];
  
  // Header相关功能
  const handleConnectWallet = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  const showDrawer = () => {
    navigate('/profile')
  };

  const onCloseDrawer = () => {
    setDrawerVisible(false);
  };

  
  const handleDepositModalClose = () => {
    setDepositModalVisible(false);
  };
  
  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };
  
  const handleAmountChange = (value: string) => {
    setAmount(value);
  };
  
  const handleSetMaxAmount = () => {
    setAmount("1000");
  };
  
  const handleSubmitTransaction = () => {
    console.log("Transaction submitted", {
      type: currentTab,
      amount,
      receivingAddress,
      contractAddress
    });
    // 处理交易提交逻辑
    setDepositModalVisible(false);
  };
  
  
  // 查看订单详情
  const handleViewDetail = (record: any) => {
    setSelectedOrder(record);
    console.log('record', record);
    setDetailDrawerVisible(true);
  };
  
  // 关闭订单详情抽屉
  const handleCloseDetail = () => {
    setDetailDrawerVisible(false);
  };

  // 显示取消订单确认框
  const showCancelConfirm = (e: React.MouseEvent, record: any) => {
    e.stopPropagation();
    setOrderToCancel(record);
    setIsCancelModalVisible(true);
  };

  // 执行取消订单操作
  const handleCancelOrder = async () => {
    if (!orderToCancel?.event || !signer) {
      errorMsg('订单ID不存在或钱包未连接');
      return;
    }

    setConfirmLoading(true);

    try {
      // const gasConfig = {
      //   maxFeePerGas: ethers.parseUnits('150', 'gwei'),
      //   maxPriorityFeePerGas: ethers.parseUnits('100', 'gwei'),
      //   gasLimit: 3000000,
      // };
      if (betMessageContract) {
        const tx = await betMessageContract.cancelTradeOrder(
          ethers.parseUnits(orderToCancel.event.toString(), 0),
          ethers.parseUnits(orderToCancel.condition.toString(), 0),
          parseInt(orderToCancel.marketSide),
          orderToCancel.orderId.toString(),
          ethers.parseUnits(orderToCancel.quantity.toString(), 0),
          // gasConfig
        )

      await tx.wait();
      successMsg('订单已成功取消');
      fetchOrders();
    }
    } catch (error: any) {
      console.error('取消订单错误:', error);
      errorMsg(error.message || '取消订单失败，请稍后重试');
    } finally {
      setConfirmLoading(false);
      setIsCancelModalVisible(false);
      setOrderToCancel(null);
    }
  };

  const handleCancelModalCancel = () => {
    setIsCancelModalVisible(false);
    setOrderToCancel(null);
  };

  // 修改无限滚动逻辑
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && !loading && hasMore && !isLoading && !isLoadingMore) {
          console.log('Loading more data, current page:', currentPage);
          setIsLoadingMore(true);
          setLoading(true);
          // 直接设置下一页，而不是使用函数式更新
          setCurrentPage(currentPage + 1);
        }
      },
      {
        root: null,
        rootMargin: '20px',
        threshold: 0.1
      }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loading, hasMore, isLoading, currentPage, isLoadingMore]);

  // 修改加载更多区域的渲染逻辑
  const renderLoadingMore = () => (
    <div
      ref={loadMoreRef}
      style={{
        textAlign: 'center',
        margin: '20px 0',
        height: '50px',
        lineHeight: '50px',
        color: theme.textColor
      }}
    >
      {isLoading ? (
        <div className="loading-container">
          <Spin />
          <span style={{ marginLeft: 10 }}>loading...</span>
        </div>
      ) : !hasMore && ordersData.length === 0 ? (
        <Empty
          description={t('default.noData')}
          image={null}
          style={{ color: theme.textColor }}
        />
      ) : !hasMore && ordersData.length > 0 ? (
        <div>{t("default.noMoreData")}</div>
      ) : null}
    </div>
  );

  // 在桌面端和移动端的渲染函数中使用新的加载更多组件
  const renderDesktopOrderList = () => (
    <div className="order-table-container">
      <Table 
        dataSource={filteredOrders.length > 0 ? filteredOrders : formattedOrders} 
        columns={columns} 
        rowKey="id"
        pagination={false}
        className={`orders-table ${themeClass}`}  
        onRow={(record) => ({
          onClick: () => handleViewDetail(record),
          style: { cursor: 'pointer' }
        })}
      />
      {renderLoadingMore()}
    </div>
  );

  const renderMobileOrderCard = (order: any) => (
    <div 
      key={order.id} 
      className={`mobile-order-card ${themeClass}`}
      onClick={() => handleViewDetail(order)}
    >
      <div className="mobile-order-title">
        <Text ellipsis strong>{order.event}</Text>
        <Button 
          type="text" 
          icon={<img src={Rectangle} alt="view-detail" />} 
          className="view-detail-btn"
          onClick={(e) => showCancelConfirm(e, order)}
        />
      </div>
      <div className="mobile-order-info">
        {/* <div className={`order-condition ${order.conditions.toLowerCase()}`}>
          {order.conditions === 'Yes' ? t('default.buyyes') : t('default.buyno')}
        </div> */}
        <div
          style={{
            display: 'inline-block',
            padding: '4px 12px',
            borderRadius: '16px',
            backgroundColor: order.answer === 'Yes' ? '#C578FF40' : '#6A7EFF60',
            color: order.answer === 'Yes' ? '#C578FF' : '#6A7EFF',
            fontWeight: 'bold',
            textAlign: 'center',
            minWidth: '60px',
          }}
        >
          {order.answer}
        </div>
        
        <div className="order-price">$ 4.16</div>
      </div>
      <div className="mobile-order-details">
        <Text className="order-currency">{order.price}</Text>
        <Text className="order-time">{order.time}</Text>
      </div>
    </div>
  );
  
  // 渲染移动端订单列表
  const renderMobileOrderList = () => {
    if (!address || !signer) {
      return (
        <div className="mobile-orders-container">
          <Empty
            description={
              <div>
                <p style={{ color: theme.textColor }}>{t('default.noData')}</p>
                {/* <Button type="primary" onClick={handleConnectWallet}> */}
                  {t('comments.loginRequired')}
                {/* </Button> */}
              </div>
            }
            image={null}
            style={{ color: theme.textColor }}
          />
        </div>
      );
    }

    if (formattedOrders.length === 0) {
      return (
        <div className="mobile-orders-container">
          <Empty
            description={t('default.noData')}
            image={null}
            style={{ color: theme.textColor }}
          />
        </div>
      );
    }

    const displayOrders = filteredOrders.length > 0 ? filteredOrders : formattedOrders;

    return (
      <div className="mobile-orders-container">
        {displayOrders.map(renderMobileOrderCard)}
        {renderLoadingMore()}
      </div>
    );
  };
  
  // 渲染订单详情
  const renderOrderDetail = () => {
    // 确保selectedOrder存在
    if (!selectedOrder) {
      return <Empty description={t('OrderDetail.noOrderSelected')} />;
    }
    
    return (
      <div className={`order-detail-container ${themeClass}`}>
        <Title level={5} className="detail-title">{selectedOrder.eventName}</Title>
      
        <Row className="detail-section">
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.conditions')}</Text>
            <Text strong className="detail-value">{selectedOrder.conditionName}</Text>
          </Col>
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.currency')}</Text>
            <Text strong className="detail-value">{selectedOrder.answer}</Text>
          </Col>
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.price')}</Text>
            <Text strong className="detail-value">${Number(selectedOrder.price).toFixed(2)}</Text>
          </Col>
        </Row>
      
        <Row className="detail-section">
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.buySellOrders')}</Text>
            <Text strong className="detail-value">{Number(selectedOrder.side) === 0 ? t("myOrders.sell") : t("myOrders.buy")}</Text>
          </Col>
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.shares')}</Text>
            <Text strong className="detail-value">{selectedOrder.quantity}</Text>
          </Col>
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.fee')}</Text>
            <Text strong className="detail-value">${selectedOrder.fee}</Text>
          </Col>
        </Row>
      
        <div className="total-amount-section">
          <Text className="total-label">{t('myOrders.TotalAmount')}: </Text>
          <Text strong className="total-value">${Number(selectedOrder.expectedRevenue).toFixed(2)}</Text>
        </div>
      
        <Row className="detail-section">
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.executedVolume')}</Text>
            <Text strong className="detail-value">{selectedOrder.completedAmount}</Text>
          </Col>
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.executedAmount')}</Text>
            <Text strong className="detail-value">${selectedOrder.realRevenue}</Text>
          </Col>
          <Col span={8}>
            <Text className="detail-label">{t('OrderDetail.expectedEarnings')}</Text>
            <Text strong className="detail-value">{selectedOrder.failAmount}</Text>
          </Col>
        </Row>
      
        <Row className="detail-section">
          <Col span={12}>
            <Text className="detail-label">{t('OrderDetail.status')}</Text>
            <div className="status-indicator waiting">
              <span className="status-dot"></span>
              <Text className="status-text">{selectedOrder.status}</Text>
            </div>
          </Col>
          <Col span={12}>
            <Text className="detail-label">{t('OrderDetail.orderType')}</Text>
            <Text strong className="detail-value">{t('OrderDetail.limitOrder')}</Text>
          </Col>
        </Row>
      
        <Row className="detail-section">
          <Col span={24}>
            <Text className="detail-label">{t('OrderDetail.orderId')}</Text>
            <Text strong className="detail-value id-value">{selectedOrder.orderId}</Text>
          </Col>
        </Row>
      
        <Row className="detail-section">
          <Col span={24}>
            <Text className="detail-label">{t('OrderDetail.time')}</Text>
            <Text strong className="detail-value">{moment(Number(selectedOrder.orderTimestamp)).format('YYYY-MM-DD HH:mm:ss')}</Text>
          </Col>
        </Row>
      </div>
    );
  };
  
  // 渲染移动端订单详情抽屉
  const renderOrderDetailDrawer = () => (
    <Drawer
      title={null}
      placement={isMobile ? "right" : "right"}
      closable={true}
      onClose={handleCloseDetail}
      open={detailDrawerVisible}
      closeIcon={<CloseOutlined style={{color: theme.textColor}}/>}
      width={isMobile ? "100%" : 450}
      className={`order-detail-drawer ${themeClass}`}
      headerStyle={{ 
        backgroundColor: theme.backgroundColor, 
        borderBottom: `1px solid ${theme.borderColor}`
      }}
      bodyStyle={{ 
        padding: 0, 
        backgroundColor: theme.backgroundColor,
        height: '100%'
      }}
    >
      {renderOrderDetail()}
    </Drawer>
  );

  // 在Layout的尾部添加Footer组件
  const renderMobileFooter = () => (
    <Footer 
      className="mobile-footer" 
      style={{ 
        backgroundColor: theme.name === 'dark' ? '#1D2B39' : '#FFFFFF', 
        borderTop: `1px solid ${theme.name === 'dark' ? '#435363' : '#E8E8E8'}`,
        position: 'fixed',
        bottom: 0,
        left: 0,
        width: '100%',
        zIndex: 10,
        padding: '0',
        boxShadow: '0 -2px 8px rgba(0,0,0,0.15)'
      }}
    >
      <Menu mode="horizontal" defaultSelectedKeys={['orders']} className={`mobile-tab-bar ${themeClass}`} theme={theme.name as any}>
        <Menu.Item key="home" icon={<HomeOutlined style={{ fontSize: '20px' }} />} onClick={() => navigate('/')}>
          <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.home')}</span>
        </Menu.Item>
        <Menu.Item key="orders" icon={<UnorderedListOutlined style={{ fontSize: '20px' }} />}>
          <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.orders')}</span>
        </Menu.Item>
        {isLoggedIn ? (
          <Menu.Item key="person" icon={<Avatar src={userAvatarImage} size={20} />} onClick={showDrawer}>
            <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.person')}</span>
          </Menu.Item>
        ) : (
          <Menu.Item key="person" icon={<UserOutlined style={{ fontSize: '20px' }} />} onClick={showDrawer}>
            <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.person')}</span>
          </Menu.Item>
        )}
      </Menu>
    </Footer>
  );

  const [orderTypeFilter, setOrderTypeFilter] = useState<string>('-1');
  const [orderStatusFilter, setOrderStatusFilter] = useState<string>('-1');
  const [largeOrdersOnly, setLargeOrdersOnly] = useState<boolean>(false);
  const [filteredOrders, setFilteredOrders] = useState<any[]>([]);

  // 前端筛选逻辑
  useEffect(() => {
    if (!ordersData.length) {
      setFilteredOrders([]);
      return;
    }

    let filtered = [...formattedOrders];

    // Filter by order type
    if (orderTypeFilter !== 'all') {
      filtered = filtered.filter(order => order.type === orderTypeFilter);
    }

    // Filter by order status
    if (orderStatusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === orderStatusFilter);
    }

    // Filter large orders (orders with quantity > 100)
    if (largeOrdersOnly) {
      filtered = filtered.filter(order => order.quantity > 100);
    }

    setFilteredOrders(filtered);
  }, [ordersData, orderTypeFilter, orderStatusFilter, largeOrdersOnly]);
  
  // 监听筛选条件变化，使用后端API筛选
  const [prevFilters, setPrevFilters] = useState({
    type: '-1',
    status: '-1',
    large: false
  });
  
  useEffect(() => {
    // 只有当筛选条件真正发生变化时才触发
    if (prevFilters.type !== orderTypeFilter || 
        prevFilters.status !== orderStatusFilter || 
        prevFilters.large !== largeOrdersOnly) {
      
      // 更新前一次的筛选状态
      setPrevFilters({
        type: orderTypeFilter,
        status: orderStatusFilter,
        large: largeOrdersOnly
      });

      
      // 有激活的筛选条件，并且钱包地址存在，才触发API筛选
      if (walletAddress) {
        console.log('Filter conditions changed, resetting page to 1');
        setCurrentPage(1);
        setOrdersData([]);
        setHasMore(true);
        fetchOrders(1);
      }
    }
  }, [orderTypeFilter, orderStatusFilter, largeOrdersOnly]);

  // Add filter components to render
  const renderFilters = () => {
    // 处理筛选条件变化
    const handleFilterChange = (type: string, value: any) => {
      switch(type) {
        case 'type':
          setOrderTypeFilter(value);
          break;
        case 'status':
          setOrderStatusFilter(value);
          break;
        case 'large':
          setLargeOrdersOnly(value);
          break;
      }
      // 筛选条件变化由useEffect监听并处理API调用
    };
    
    return (
      <div className="order-filters" style={{ 
        display: 'flex', 
        flexWrap: 'wrap', 
        justifyContent: 'flex-end',
        gap: '10px'
      }}>
        <Space wrap>
          <Select 
            value={orderTypeFilter}
            onChange={value => handleFilterChange('type', value)}
            style={{ width: 120, backgroundColor: 'transparent' }}
            placeholder={t('myOrders.orderType')}
            size={isMobile ? "small" : "middle"}
            className="custom-select"
          >
            <Option value="-1">{t('myOrders.allTypes')}</Option>
            <Option value="1">{t('myOrders.buy')}</Option>
            <Option value="0">{t('myOrders.sell')}</Option>
          </Select>

          <Select 
            value={orderStatusFilter}
            onChange={value => handleFilterChange('status', value)}
            style={{ width: 150, backgroundColor: 'transparent' }}
            placeholder={t('myOrders.status')}
            size={isMobile ? "small" : "middle"}
            className="custom-select"
          >
            <Option value="-1">{t('myOrders.allStatus')}</Option>
            <Option value="0">{t('myOrders.Completed')}</Option>
            <Option value="1">{t('myOrders.Cancelled')}</Option>
            <Option value="2">{t('myOrders.WaitingForTransaction')}</Option>
            <Option value="3">{t('myOrders.PartiallyExecuted')}</Option>
            <Option value="4">{t('myOrders.Terminated')}</Option>
          </Select>

          <Checkbox 
            checked={largeOrdersOnly} 
            style={{
              backgroundColor: 'transparent'
            }}
            className="custom-checkbox"
            onChange={e => handleFilterChange('large', e.target.checked)}
          >
            <span style={{ color: theme.textColor }}>{t('myOrders.largeOrdersOnly')}</span>
          </Checkbox>
        </Space>
      </div>
    );
  };
  
  // Add custom styles directly to the components
  useEffect(() => {
    // Create style element
    const styleElement = document.createElement('style');
    
    // Define the styles as a string
    const stylesContent = `
      .custom-checkbox .ant-checkbox-inner {
        background-color: ${theme.backgroundColor} !important;
        border-color: ${theme.borderColor} !important;
      }
      .custom-checkbox .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #1677FF !important;
      }
      .custom-checkbox {
        background-color: transparent !important;
      }
      
      .custom-select .ant-select-selector {
        background-color: transparent !important;
      }
      .custom-select .ant-select-selection-item {
        color: ${theme.textColor} !important;
      }
      .custom-select .ant-select-arrow {
        color: ${theme.textColor} !important;
      }
    `;
    
    // Set the innerHTML of the style element
    styleElement.innerHTML = stylesContent;
    
    // Append to document head
    document.head.appendChild(styleElement);
    
    // Cleanup function
    return () => {
      document.head.removeChild(styleElement);
    };
  }, [theme]);

  return (
    <Layout className={`orders-layout ${themeClass}`} style={{marginTop: isMobile? '-50px':'' }}>
      {/* <AppHeader
        isLoggedIn={isLoggedIn}
        handleConnectWallet={handleConnectWallet}
        handleLogout={handleLogout}
        showDrawer={showDrawer}
        showDepositModal={showDepositModal}
        renderDropdownMenu={renderDropdownMenu}
      /> */}
      {contextHolder}
      <Content className="orders-content">
        <div className="orders-title-section" style={{ 
          display: 'flex', 
          flexDirection: isMobile ? 'column' : 'row',
          justifyContent: 'space-between', 
          alignItems: isMobile ? 'flex-start' : 'center', 
          marginBottom: '20px',
          gap: isMobile ? '10px' : '0'
        }}>
          <Title level={4} className="orders-title">{t('myOrders.title')}</Title>
          {renderFilters()}
        </div>
        
        {isMobile ? renderMobileOrderList() : renderDesktopOrderList()}
        
        {renderOrderDetailDrawer()}
      </Content>
      
      <Drawer
        title={isLoggedIn ? <Avatar src={userAvatarImage} /> : t('drawer.menuTitle')}
        placement="left"
        onClose={onCloseDrawer}
        open={drawerVisible}
        closable={true}
        closeIcon={<CloseOutlined style={{color: theme.textColor}}/>}
        bodyStyle={{ 
          padding: 0, 
          backgroundColor: theme.backgroundColor,
          height: '100%'
        }}
        headerStyle={{ 
          backgroundColor: theme.backgroundColor, 
          borderBottom: `1px solid ${theme.borderColor}`
        }}
        className={`dark-drawer ${themeClass}`}
        style={{ background: theme.backgroundColor }}
      >
        <DrawerContent
          theme={theme}
          isLoggedIn={isLoggedIn}
          themeClass={themeClass}
          handleConnectWallet={handleConnectWallet}
          handleLogout={handleLogout}
          toggleTheme={toggleTheme}
        />
      </Drawer>

      {/* 只在移动设备上渲染底部导航 */}
      {isMobile && renderMobileFooter()}

      {/* Deposit/Withdraw Modal */}
      <Modal
        open={depositModalVisible}
        onCancel={handleDepositModalClose}
        footer={null}
        width={520}
        closable={false}
        destroyOnClose
        centered
        className={`deposit-withdraw-modal ${themeClass}`}
        maskClosable={true}
      >
        <div className={`deposit-withdraw-container ${themeClass}`}>

          <div style={{width: '100%', height: '44px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}> <CloseOutlined 
            className="ant-modal-close" 
            onClick={handleDepositModalClose}
            style={{ 
              color: theme.name === 'dark' ? '#fff' : '#777E8C' 
            }}
          /> </div>

          {/* Tab切换按钮 */}
          <div className="modal-tabs">
            <div 
              className={`tab-button ${currentTab === 'deposit' ? 'active' : ''}`} 
              onClick={() => handleTabChange('deposit')}
              style={{
                backgroundColor: currentTab === 'deposit' 
                  ? (theme.name === 'dark' ? '#243546' : '#1677FF') 
                  : (theme.name === 'dark' ? '#1A2531' : '#F5F5F5'),
                color: currentTab === 'deposit'
                  ? '#FFFFFF'
                  : (theme.name === 'dark' ? '#97A0A4' : '#777E8C')
              }}
            >
              {t('default.deposit')}
            </div>
            <div 
              className={`tab-buttonleft ${currentTab === 'withdraw' ? 'active' : ''}`}
              onClick={() => handleTabChange('withdraw')}
              style={{
                backgroundColor: currentTab === 'withdraw' 
                  ? (theme.name === 'dark' ? '#243546' : '#1677FF') 
                  : (theme.name === 'dark' ? '#1A2531' : '#F5F5F5'),
                color: currentTab === 'withdraw'
                  ? '#FFFFFF'
                  : (theme.name === 'dark' ? '#97A0A4' : '#777E8C')
              }}
            >
              {t('default.withdraw')}
            </div>
          </div>
          
          {/* 表单内容 */}
          <div className="modal-form">
            {/* Network & Currency */}
            <div className="form-field">
              <div className="field-label" style={{ color: theme.name === 'dark' ? '#97A0A4' : '#777E8C' }}>
                {t('default.network')}
              </div>
              <div className="field-value" style={{ color: theme.textColor }}>
                {networkName}/USDT
              </div>
            </div>
            
            {/* Amount */}
            <div className="form-field">
              <div className="field-label" style={{ color: theme.name === 'dark' ? '#97A0A4' : '#777E8C' }}>
                {currentTab === 'deposit' ? t('default.depositAmount') : t('default.withdrawAmount')}
              </div>
              <div className="field-value available-balance" style={{ color: '#1677FF' }}>
                1000 USDT {t('default.available')}
              </div>
            </div>
            
            {/* Amount Input */}
            <div className="amount-input-container">
              <input
                type="text"
                placeholder={t('default.enterAmount')}
                value={amount}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="amount-input"
                style={{
                  backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                  color: theme.textColor,
                  border: 'none'
                }}
              />
              <button
                className="max-button"
                onClick={handleSetMaxAmount}
                style={{
                  backgroundColor: theme.name === 'dark' ? '#3B4754' : '#FFFFFF',
                  color: theme.name === 'dark' ? '#FFFFFF' : '#000000'
                }}
              >
                {t('default.max')}
              </button>
            </div>
            
            {/* Address Fields */}
            <div className="form-field">
              <div className="field-label" style={{ color: theme.name === 'dark' ? '#97A0A4' : '#777E8C' }}>
                {currentTab === 'deposit' ? t('default.paymentAddress') : t('default.receivingAddress')}
              </div>
            </div>
            <input
              type="text"
              value={receivingAddress}
              readOnly
              className="address-input"
              style={{
                backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                color: theme.textColor,
                border: 'none'
              }}
            />
            
            <div className="form-field">
              <div className="field-label" style={{ color: theme.name === 'dark' ? '#97A0A4' : '#777E8C' }}>
                {t('default.contractAddress')}
              </div>
            </div>
            <input
              type="text"
              value={contractAddress}
              readOnly
              className="address-input"
              style={{
                backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                color: theme.textColor,
                border: 'none'
              }}
            />
            
            {/* Submit Button */}
            <button
              className="submit-button"
              onClick={handleSubmitTransaction}
              style={{
                backgroundColor: '#1677FF'
              }}
            >
              {t('default.submitTrans')}
            </button>
          </div>
        </div>
      </Modal>

      {/* 取消订单确认弹窗 */}
      <Modal
        title={t('myOrders.CancelOrder')}
        open={isCancelModalVisible}
        onOk={handleCancelOrder}
        onCancel={handleCancelModalCancel}
        okText={t('myOrders.verify')}
        cancelText={t('myOrders.Cancel')}
        confirmLoading={confirmLoading}
        className={`cancel-order-modal ${themeClass}`}
        bodyStyle={{
          backgroundColor: theme.backgroundColor,
          color: theme.textColor,
          borderBottom: 'none'
        }}
        maskStyle={{
          backgroundColor: 'rgba(0, 0, 0, 0.45)'
        }}
        rootClassName="custom-modal-without-borders"
        okButtonProps={{
          style: {
            boxShadow: 'none'
          }
        }}
      >
        <p style={{ color: theme.textColor }}>{t('myOrders.order')}</p>
      </Modal>
    </Layout>
  );
};

export default Orders; 