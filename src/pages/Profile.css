/* Profile Page Styles */
.profile-layout {
  min-height: calc(100vh  - 624px);
}

.profile-layout.light-theme {
  background-color: #ffffff;
}

.profile-layout.dark-theme {
  background-color: #1D2B39;
}

.profile-content {
  padding: 24px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
}

/* Profile Header */
/* .profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
} */

.profile-avatar {
  margin-right: 24px;
}

.user-avatar {
  border: 2px solid #478FB8;
}

.profile-info {
  flex: 1;
}

.profile-name {
  margin-bottom: 4px !important;
}

.light-theme .profile-name {
  color: #000000;
}

.dark-theme .profile-name {
  color: #FFFFFF;
}

.profile-id {
  font-size: 14px;
  margin-bottom: 4px;
}

.light-theme .profile-id {
  color: #777E8C;
}

.dark-theme .profile-id {
  color: #97A0A4;
}

.profile-join-date {
  font-size: 14px;
}

.light-theme .profile-join-date {
  color: #777E8C;
}

.dark-theme .profile-join-date {
  color: #97A0A4;
}

.edit-profile-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 8px;
  font-weight: 500;
}

.light-theme .edit-profile-btn {
  background-color: #F5F5F500;
  border-color: #E7E7E7;
  color: #97A0A4;
}

.dark-theme .edit-profile-btn {
  background-color: #24354600;
  border-color: #435363;
  color: #97A0A4;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 30px;
  margin-top: 16px;
}

.deposit-btn, .withdraw-btn {
  flex: 1;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deposit-btn .anticon, .withdraw-btn .anticon {
  margin-right: 8px;
  font-size: 18px;
}

.light-theme .deposit-btn {
  background-color: #0852F0;
  border-color: #0852F0;
  color: #FFFFFF;
}

.dark-theme .deposit-btn {
  background-color: #2C9CDC;
  border-color: #2C9CDC;
  color: #FFFFFF;
  box-shadow: none !important;
}

.light-theme .withdraw-btn {
  background-color: #F5F5F5;
  border-color: #E7E7E7;
  color: #000000;
}

.dark-theme .withdraw-btn {
  background-color: #243546;
  border-color: #435363;
  color: #FFFFFF;
  box-shadow: none !important;
}

/* Section Container */
.section-container {
  margin-bottom: 30px;
}

.section-title {
  margin-bottom: 16px !important;
}

.light-theme .section-title {
  color: #000000;
}

.dark-theme .section-title {
  color: #FFFFFF;
}

/* Asset Card */
.asset-card, .earning-card {
  display: flex;
  align-items: start;
  flex-direction: column;
  padding: 20px;
  border-radius: 8px;
  height: 100%;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.light-theme .asset-card, .light-theme .earning-card {
  background-color: #FFFFFF;
  border: 1px solid #E7E7E7;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.dark-theme .asset-card, .dark-theme .earning-card {
  background-color: #375066;
  border: none;
  box-shadow: none;
}

.light-theme .asset-card:hover, .light-theme .earning-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border-color: #0852F0;
}

.dark-theme .asset-card:hover, .dark-theme .earning-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  background-color: #436077;
}

.asset-icon-container, .earning-icon-container {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.light-theme .asset-icon-container, .light-theme .earning-icon-container {
  background-color: #F5F5F500;
}

.dark-theme .asset-icon-container, .dark-theme .earning-icon-container {
  background-color: #24354600;
}

.asset-info, .earning-info {
  flex: 1;
}

.asset-title, .earning-title {
  display: block;
  font-size: 14px;
  margin-bottom: 4px;
}

.light-theme .asset-title, .light-theme .earning-title {
  color: #777E8C;
}

.dark-theme .asset-title, .dark-theme .earning-title {
  color: #97A0A4;
}

.asset-amount, .earning-amount {
  display: block;
  font-size: 20px;
  font-weight: 600;
}

.light-theme .asset-amount, .light-theme .earning-amount {
  color: #000000;
}

.dark-theme .asset-amount, .dark-theme .earning-amount {
  color: #FFFFFF;
}

/* Mobile Footer */
.mobile-footer {
  height: 50px;
  line-height: 50px;
}

.mobile-tab-bar {
  display: flex;
  justify-content: space-around;
  border-bottom: none !important;
}

.mobile-tab-bar.light-theme {
  background-color: #FFFFFF !important;
}

.mobile-tab-bar.dark-theme {
  background-color: #1D2B39 !important;
}

.mobile-tab-bar .ant-menu-item {
  margin: 0 !important;
  padding: 0 !important;
  flex: 1;
  text-align: center;
  height: 50px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  line-height: 1.2 !important;
}

.mobile-tab-bar .ant-menu-item .anticon {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}

.mobile-tab-bar .ant-menu-item span {
  display: block;
  color: inherit;
}

.mobile-tab-bar .ant-menu-item-selected {
  background-color: transparent !important;
}

.mobile-tab-bar.dark-theme .ant-menu-item .anticon {
  color: #858D92 !important;
}

.mobile-tab-bar.dark-theme .ant-menu-item-selected .anticon {
  color: #FFFFFF !important;
}

.mobile-tab-bar.light-theme .ant-menu-item {
  color: #858D92 !important;
}

.mobile-tab-bar.light-theme .ant-menu-item-selected {
  color: #1D2B39 !important;
}

/* Responsive */
@media (max-width: 768px) {
  .profile-content {
    padding: 16px;
  }
  
  .profile-header {
    flex-direction: row;
    align-items: center;
    text-align: center;
  }
  
  .profile-avatar {
    margin-right: 0;
    margin-bottom: 16px;
  }
  
  .profile-info {
    margin-bottom: 16px;
  }
  
  .section-container {
    margin-bottom: 20px;
  }
  
  .asset-card, .earning-card {
    padding: 14px;
  }
  
  .asset-icon-container, .earning-icon-container {
    width: 48px;
    height: 48px;
  }
  
  .asset-title, .earning-title {
    font-size: 12px;
  }
  
  .asset-amount, .earning-amount {
    font-size: 16px;
  }
}

/* Deposit/Withdraw Modal Styles */
.deposit-withdraw-modal .ant-modal-content {
  background-color: transparent !important;
  box-shadow: none !important;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.deposit-withdraw-modal .ant-modal-close {
  color: #fff;
  top: 12px;
  right: 12px;
  z-index: 100;
  position: absolute;
}

.deposit-withdraw-container {
  border-radius: 12px;
  overflow: hidden;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
}

.deposit-withdraw-container .ant-modal-close {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 100;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.deposit-withdraw-container.light-theme {
  background-color: #FFFFFF;
  color: #000000;
  border: 1px solid #E7E7E7;
}

.deposit-withdraw-container.dark-theme {
  background-color: #1D2B39;
  color: #FFFFFF;
  border: 1px solid #243546;
}

.deposit-withdraw-container.light-theme .ant-modal-close {
  color: #777E8C;
}

.deposit-withdraw-container.dark-theme .ant-modal-close {
  color: #fff;
}

/* Tab按钮样式 */
.modal-tabs {
  display: flex;
  width: 100%;
  padding: 10px;
}

.tab-button {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  border-radius: 8px 0px 0px 8px;
  margin-left: 20px;
}

.tab-buttonleft {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  border-radius: 0px 8px 8px 0px;
  margin-right: 20px;
}

/* 表单样式 */
.modal-form {
  padding: 20px 30px 30px;
}

.form-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  flex-direction: row;
  margin-left: 32px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  
}

.field-value {
  font-size: 14px;
  font-weight: 600;
}

.available-balance {
  color: #1677FF;
}

/* 金额输入框 */
.amount-input-container {
  position: relative;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
}

.amount-input {
  height: 48px;
  width: 100%;
  padding: 0 16px;
  font-size: 16px;
  outline: none;
  border-radius: 8px;
}

.max-button {
  position: absolute;
  right: 8px;
  height: 32px;
  border: none;
  border-radius: 16px;
  padding: 0 12px;
  font-weight: 500;
  cursor: pointer;
}

/* 地址输入框 */
.address-input {
  height: 48px;
  width: 92%;
  padding: 0 16px;
  font-size: 14px;
  outline: none;
  border-radius: 8px;
  margin-bottom: 24px;
  cursor: default;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 48px;
  border: none;
  border-radius: 8px;
  background-color: #1677FF;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  margin-top: 12px;
}

.submit-button:hover {
  opacity: 0.9;
} 