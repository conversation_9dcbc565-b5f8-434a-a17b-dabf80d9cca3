import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Layout, Card, Row, Col, Typography, Grid, Carousel, Spin, Empty, Button, message, Tooltip } from 'antd';
import { useMediaQuery } from 'react-responsive';
import './Home.css';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useAppContext } from '../context/AppContext';
import { mergeGameWithConditions } from '../utils';
import { eventBus } from '../utils/eventBus';
import { DoubleRightOutlined, StarFilled, StarOutlined } from '@ant-design/icons';

// Import downloaded assets (update paths as needed)
import eventTitleImg from '../assets/figma_images/eventTitleImg.png';
import { dappApi } from '../services/request';
// import welcomeBannerImage1 from '../assets/figma_images/welcomeBannerImage1.jpg';
// import welcomeBannerImage2 from '../assets/figma_images/welcomeBannerImage2.jpg';/

const { Content } = Layout;
const { Text, Paragraph  } = Typography;
const { useBreakpoint } = Grid;


// 游戏类型接口
interface GameType {
  id: number;
  name: string;
}

// 默认游戏类型
// const defaultGameTypes: GameType[] = [
//   { id: 0, name: 'All' },
//   { id: 1006, name: 'Sports' },
//   { id: 1007, name: 'Cryptos' },
//   { id: 1008, name: 'Politics' }
// ];

// 移除静态的bannerItems定义
// const bannerItems = [
//   {
//     key: '1',
//     image: welcomeBannerImage1,
//     url: 'https://www.baidu.com',
//   },
//   {
//     key: '2',
//     image: welcomeBannerImage2,
//     url: 'https://www.baidu.com',
//   }
// ];

// 升级版环形进度条组件，仿照图片样式
const ChanceArc = ({ percent }: { percent: number }) => {
  const radius = 28;
  const stroke = 5;
  const center = radius + stroke;
  const totalDegree = 210; // 总弧度角度
  const totalAngle = totalDegree * Math.PI / 180; // 230度的弧度
  // const gapAngle = 12; // 主断开角度
  const smallGapAngle = 13; // 彩色和灰色之间的小断开角度

  // 以12点钟方向为0，逆时针为正
  const startAngle = (360 - totalDegree) / 2 * Math.PI / 180; // 起点角度（弧度）
  const endAngle = (360 - (360 - totalDegree) / 2) * Math.PI / 180; // 终点角度（弧度）

  const percentAngle = totalAngle * (percent / 100);

  // 颜色区分
  let arcColor = '#E74801';
  if (percent >= 66) arcColor = '#25AE60';
  else if (percent >= 33) arcColor = '#F5A623';

  const percentText = `${percent}%`;

  // 极坐标转笛卡尔
  const polarToCartesian = (angle: number) => {
    const x = center + radius * Math.cos(angle - Math.PI / 2 + Math.PI);
    const y = center + radius * Math.sin(angle - Math.PI / 2 + Math.PI);
    return { x, y };
  };

  // 有色弧线
  const arcStart = polarToCartesian(startAngle);
  const arcEnd = polarToCartesian(startAngle + percentAngle);
  const arcLargeArcFlag = percentAngle > Math.PI ? 1 : 0;
  const arcPath = `M ${arcStart.x},${arcStart.y} A ${radius},${radius} 0 ${arcLargeArcFlag} 1 ${arcEnd.x},${arcEnd.y}`;

  // 灰色弧线
  const bgStartAngle = startAngle + percentAngle + (smallGapAngle * Math.PI / 180);
  const bgEndAngle = endAngle;
  const bgStart = polarToCartesian(bgStartAngle);
  const bgEnd = polarToCartesian(bgEndAngle);
  const bgLargeArcFlag = (totalAngle - percentAngle - (smallGapAngle * Math.PI / 180)) > Math.PI ? 1 : 0;
  const bgPath = `M ${bgStart.x},${bgStart.y} A ${radius},${radius} 0 ${bgLargeArcFlag} 1 ${bgEnd.x},${bgEnd.y}`;

  return (
    <div style={{ position: 'relative', width: 2 * (radius + stroke), height: 2 * (radius + stroke) }}>
      <svg width={2 * (radius + stroke)} height={2 * (radius + stroke)}>
        {/* 灰色弧线（剩余部分） */}
        {percent < 100 && (
          <path
            d={bgPath}
            fill="none"
            stroke="#888C96"
            strokeWidth={stroke}
            strokeLinecap="round"
          />
        )}
        {/* 有色弧线 */}
        {percent > 0 && (
          <path
            d={arcPath}
            fill="none"
            stroke={arcColor}
            strokeWidth={stroke}
            strokeLinecap="round"
          />
        )}
      </svg>
      {/* 百分比和chance文字，绝对定位在弧形正中间 */}
      <div style={{
        position: 'absolute',
        left: 0,
        top: '0%',
        width: 2 * (radius + stroke),
        height: 2 * (radius + stroke),
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        pointerEvents: 'none'
      }}>
        <span style={{ color: '#fff', fontWeight: 600, fontSize: 13, lineHeight: 1 }}>{percentText}</span>
        <span style={{ color: '#8C939F', fontSize: 10, lineHeight: 1, marginTop: 2 }}>chance</span>
      </div>
    </div>
  );
};
const FavoritesPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme, themeClass } = useAppContext();
  const screens = useBreakpoint();
  const isMobileMediaQuery = useMediaQuery({ maxWidth: 768 });
  const isMobile = !screens.md || isMobileMediaQuery;
  const carouselRef = useRef<any>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  
  // 移除活动数据状态，因为收藏页面不需要这些
  
  // 用于无限滚动的状态
  const [displayedData, setDisplayedData] = useState<any[]>([]);
  const [page, setPage] = useState(0);
  const [pageSize] = useState(10);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  // 添加初始化加载标志，防止重复加载
  const [isInitialized, setIsInitialized] = useState(false);
  
  // 游戏类型状态
  // const [gameTypes, setGameTypes] = useState<GameType[]>(defaultGameTypes);
  
  // 添加防抖计时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [messageApi, contextHolder] = message.useMessage();
  
  // 添加收藏状态
  const [favorites, setFavorites] = useState<Record<string, boolean>>({});
  const showMessage = (msg: String, type: 'success' | 'error' | 'warning' ) => {
    messageApi.open({
      type: type,
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  const successMsg = (msg: String) => showMessage(msg, 'success');
  // 从URL中获取当前选中的分类和搜索关键词
  
  // 添加分类选择状态，从URL初始化
  // 在组件初始化时设置 message 的全局配置
  const errorMsg = (msg :String) => {
    messageApi.open({
      type: 'error',
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  
  
  // 加载收藏数据
  const loadGameData = async () => {
    if (loading || !hasMore) return;
    
    // 防抖处理，避免短时间内多次调用
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(async () => {
      setLoading(true);
      setError(null);
      
      try {
        // 获取认证toke
        
        // 构建API请求参数
        const params = new URLSearchParams();
        params.append('page', (page).toString()); // API使用1-based页码
        params.append('page_size', pageSize.toString());
        
        // 调用收藏列表API
        const bookmarksUrl = `/v1/favorite?${params.toString()}`;
        const bookmarksResponse = await dappApi.get(bookmarksUrl);
        
        if (bookmarksResponse.status !== 'success') {
          throw new Error(`Bookmarks API responded with status: ${bookmarksResponse.status}`);
        }
        
        const bookmarksData = bookmarksResponse.data;
        
        if (!Array.isArray(bookmarksData.favorites)) {
          setHasMore(false);
          return;
        }
        
        const bookmarkedActivities = bookmarksData.favorites;
        
        // 如果没有收藏数据，设置hasMore为false并返回
        if (bookmarkedActivities.length === 0) {
          setHasMore(false);
          return;
        }
        
        // 收集所有的game_ids
        const gameIds = bookmarkedActivities.map((activity: any) => activity.game_id).join(',');
        
        // 调用API获取游戏详情数据
        const response = await fetch(`${import.meta.env.VITE_API_URL}/v1/game/released/gameIds?game_ids=${gameIds},`);
        const gameDetailsResponse = await response.json();
        
        if (!response.ok) {
          throw new Error(`Game details fetch failed`);
        }
        
        // 创建游戏数据映射表以便快速查找
        const gameDetailsMap: Record<string, any> = {};
        if (gameDetailsResponse.data && Array.isArray(gameDetailsResponse.data)) {
          gameDetailsResponse.data.forEach((game: any) => {
            gameDetailsMap[game.id] = game;
          });
        }
        
        const transformedData = bookmarkedActivities.map((activity: any) => {
          // 尝试从游戏详情API中获取对应数据
          const gameDetail = gameDetailsMap[activity.game_id] || {};
          
          return {
            id: activity.game_id,
            description: gameDetail.description || activity.title,
            icon_url: gameDetail.icon_url || activity.image_url,
            vol: gameDetail.vol || 0, // 使用API返回的交易量，如果没有则为0
            isGameCondition: true,
            chance: gameDetail.stop_time ? Math.round((new Date().getTime() / gameDetail.stop_time) * 100) : Math.floor(Math.random() * 100),
            link: activity.link,
            start_time: activity.start_time,
            end_time: gameDetail.stop_time || activity.end_time,
            is_active: activity.is_active,
            bookmarked_at: activity.bookmarked_at,
            game_type: gameDetail.game_type || '',
            rules: gameDetail.rules || ''
          };
        });
        const responseConditions = await fetch(`${import.meta.env.VITE_API_URL}/v1/game/conditions/released?id=${gameIds}`);
        if (responseConditions.ok) {
          const conditionsData = await responseConditions.json();
          if (conditionsData.code === 200 && Array.isArray(conditionsData.data)) {
            
              // 合并游戏数据和条件数据
              const mergedData = mergeGameWithConditions(transformedData, conditionsData.data);
              setDisplayedData(prev => [...prev, ...mergedData]);
          }else{
            const mergedData = transformedData.map((game: any) => ({
              ...game,
              conditions: [],
              isGameCondition: false,
              chance: 0,
              yesBuyPrice:0
            }));
            setDisplayedData(prev => [...prev, ...mergedData]);
          }
        }
        
        // 转换API数据格式以适配现有的UI组件
        
        
        // 更新显示数据
        // setDisplayedData(prev => [...prev, ...transformedData]);
        // 检查是否还有更多数据
        const total = bookmarksData.total || 0;
        const currentCount = (page + 1) * pageSize;
        setHasMore(currentCount < total);
        
        // 更新页码
        setPage(prev => prev + 1);
        
      } catch (err: any) {
        console.error('加载收藏数据失败:', err);
        const errorMessage = err.message || '加载收藏数据失败，请稍后重试';
        setError(errorMessage);
        errorMsg(errorMessage);
      } finally {
        setLoading(false);
        debounceTimerRef.current = null;
      }
    }, 300); // 300ms防抖延迟
  };


  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = () => {
      setDisplayedData([]);
      setPage(0);
      setHasMore(true);
      setIsInitialized(false);
      loadGameData();
    };

    // 订阅语言变化事件
    eventBus.on('languageChanged', handleLanguageChange);

    // 清理订阅
    return () => {
      eventBus.off('languageChanged', handleLanguageChange);
    };
  }, []);

  // 设置无限滚动监听
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && !loading && hasMore && isInitialized) {
          loadGameData();
        }
      },
      { threshold: 0.1 }
    );
    
    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }
    
    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loading, hasMore, isInitialized]);

  // 初始化加载收藏数据
  useEffect(() => {
    if (!isInitialized) {
      setIsInitialized(true);
      loadGameData();
    }
  }, [isInitialized]);

  // // 加载收藏数据
  // useEffect(() => {
  //   // 从localStorage加载收藏数据
  //   try {
  //     const savedFavorites = localStorage.getItem('favorites');
  //     if (savedFavorites) {
  //       const favoritesArray = JSON.parse(savedFavorites) as string[];
  //       const favoritesMap: Record<string, boolean> = {};
  //       favoritesArray.forEach(id => {
  //         favoritesMap[id] = true;
  //       });
  //       setFavorites(favoritesMap);
  //     }
  //   } catch (error) {
  //     console.error('加载收藏数据失败:', error);
  //   }
  // }, []);
  
  // 切换收藏状态
  const toggleFavorite = async (e: React.MouseEvent, marketId: string) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击
    
    // 在收藏页面中，所有项目都是已收藏的，所以只需要处理移除操作
    const isFavorite = true;
    
    try {
      // 在收藏页面中，只支持移除收藏操作
      const response = await dappApi.del(`/v1/favorite?game_id=${marketId}`);
      
      if (response.status === 'success') {
        // 从显示列表中移除该项目
        setDisplayedData(prev => prev.filter(item => item.id !== marketId));
        
        // messageApi.success(t('favorite.removed') || '已取消收藏');
        successMsg(t('favorite.removed'));
      } else {
        throw new Error(response.message || '取消收藏失败');
      }
      
      // 收藏页面不需要本地存储，数据直接从 API 获取
      
    } catch (error: any) {
      console.error('切换收藏状态失败:', error);
      const errorMessage = error.message || '操作失败，请稍后重试';
      messageApi.error(errorMessage);
    }
  };
  
  const navigateToMarket = (marketId: string, option?: string, marketData?: any) => {
    console.log(marketData);
    navigate(`/market/${marketId}${option ? `?option=${option}` : ''}`, {
      state: { marketData }
    });
  };

  const [hoveredBtn, setHoveredBtn] = useState<{marketId: string, type: 'yes' | 'no'} | null>(null);


  const formatNumber = (value: number) => {
    const absValue = Math.abs(value)
    const sign = value < 0 ? '-' : ''
    
    if (absValue >= 1000000) {
      return `${sign}${(absValue / 1000000).toFixed(1)}M`
    } else if (absValue >= 1000) {
      return `${sign}${(absValue / 1000).toFixed(0)}K`
    }
    return value.toString()
  }

  const renderMarketCard = (market: any) => {
    const isSingle = market.conditions && market.conditions.length === 1;
    // 在收藏页面中，所有显示的项目都是已收藏的
    const isFavorite = true;

    return (
      <Card 
        className={`market-card ${themeClass}`}
        style={{ borderColor: 'transparent', cursor: 'pointer' }}
        onClick={() => navigateToMarket(market.id, undefined, market)}
      >
        <div
          style={{
            width: '100%',
            height: 180, // 统一卡片高度
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            boxSizing: 'border-box'
          }}
        >
          <div className="card-header-info">
            <img src={market.icon_url || eventTitleImg} style={{ width: '45px', height: '45px' }} alt="Banner" />
            <Paragraph 
              ellipsis={{ rows: 2 }} 
              strong 
              className="market-description"
              style={{ 
                color: theme.textColor, 
                minHeight: 44, 
                fontSize: 14, 
                marginTop: 8, 
                marginBottom: 8 ,
                width: '100%',
                textAlign:'left'
              }}
            >
              {market.description}
            </Paragraph>
            <ChanceArc percent={market.conditions[0].yesBuyPrice/1e16} />
          </div>
          
          {/* 按钮和描述区域 */}
          {isSingle ? (
            <div style={{ width: '100%',height:'100%' }}>
              
              <div style={{ display: 'flex', gap: 8, width: '100%', marginBottom: 0, marginTop: 8 }}>
                <Button
                  className="yes-btn"
                  style={{
                    height: 36,
                    flex: 1,
                    minWidth: 0,
                    backgroundColor: '#25AE60',
                    borderColor: '#25AE60',
                    color: 'white',
                    maxHeight: 36,
                    minHeight: 36,
                    lineHeight: '36px',
                    marginTop: 20,
                    alignSelf: 'flex-start',
                    fontSize: 18
                  }}
                  onMouseEnter={() => setHoveredBtn({marketId: market.id, type: 'yes'})}
                  onMouseLeave={() => setHoveredBtn(null)}
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateToMarket(market.id, 'yes', market);
                  }}
                >
                  Buy Yes
                  <span
                    className="buy-yes-icon"
                    style={{
                      color: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'yes' ? '#fff' : '#25AE60',
                      marginLeft: 6,
                      transition: 'opacity 0.2s, color 0.2s',
                      opacity: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'yes' ? 0.3 : 1,
                      transform: 'rotate(270deg)',
                      animation: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'yes' ? 'icon-blink 0.7s infinite' : 'none'
                    }}
                  >
                    <DoubleRightOutlined />
                  </span>
                </Button>
                <Button
                  className="no-btn"
                  style={{
                    height: 36,
                    flex: 1,
                    minWidth: 0,
                    backgroundColor: '#E74801',
                    borderColor: '#E74801',
                    color: 'white',
                    maxHeight: 36,
                    minHeight: 36,
                    lineHeight: '36px',
                    marginTop: 20,
                    alignSelf: 'flex-start',
                    fontSize: 18
                  }}
                  onMouseEnter={() => setHoveredBtn({marketId: market.id, type: 'no'})}
                  onMouseLeave={() => setHoveredBtn(null)}
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateToMarket(market.id, 'no', market);
                  }}
                >
                  Buy No
                  <span
                    className="buy-no-icon"
                    style={{
                      color: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'no' ? '#fff' : '#E74801',
                      marginLeft: 6,
                      transition: 'opacity 0.2s, color 0.2s',
                      opacity: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'no' ? 0.3 : 1,
                      transform: 'rotate(90deg)',
                      animation: hoveredBtn && hoveredBtn.marketId === market.id && hoveredBtn.type === 'no' ? 'icon-blink 0.7s infinite' : 'none'
                    }}
                  >
                    <DoubleRightOutlined />
                  </span>
                </Button>
              </div>
              <div
                style={{
                  width: '100%',
                  height: 0,
                  background: theme.name === 'dark' ? '#2C3F4F' : '#E7E7E7',
                  margin: '30px 0 2px 0'
                }}
              />
              <div
                style={{
                  width: '100%',
                  textAlign: 'left',
                  color: '#8C939F',
                  fontSize: 12,
                  lineHeight: '16px',
                  marginTop: 2,
                  marginBottom: 0,
                  paddingLeft: 2,
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <span>{'$ '+ formatNumber(market.vol/1e18) + ' Vol.'}</span>
                {/* <Tooltip title={isFavorite ? t('favorite.remove') : t('favorite.add')}> */}
                  <div 
                    onClick={(e) => toggleFavorite(e, market.id)}
                    style={{ 
                      cursor: 'pointer',
                      fontSize: '16px',
                      color: isFavorite ? '#F8CB46' : theme.name === 'dark' ? '#8C939F' : '#BFBFBF',
                      marginRight: '8px'
                    }}
                  >
                    {isFavorite ? <StarFilled /> : <StarOutlined />}
                  </div>
                {/* </Tooltip> */}
              </div>
            </div>
          ) : (
            <div style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
                <div style={{ display: 'flex', width: '65%', flexDirection: 'column' }}>
                  {market.conditions &&
                    market.conditions.slice(0, 3).map((condition: any, index: number) => (
                      <Text
                        key={`price-${index}`}
                        className="price-text"
                        style={{
                          color: theme.textColor,
                          fontSize: 12,
                          height: 22,
                          lineHeight: '22px',
                          marginTop: index > 0 ? 4 : 0,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: 'block'
                        }}
                      >
                        {condition.description}
                      </Text>
                    ))}
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-end',
                    height: 77,
                    overflow: 'hidden'
                  }}
                >
                  {market.conditions &&
                    market.conditions.slice(0, 3).map((condition: any, index: number) => (
                      <div key={`btn-${index}`} style={{ display: 'flex', gap: 4, marginTop: index > 0 ? 4 : 0 }}>
                        <Button
                          className="yes-btn"
                          style={
                            condition.yes_coin_price > 50
                              ? {
                                  backgroundColor: '#25AE60',
                                  borderColor: '#25AE60',
                                  color: 'white',
                                  height: 36,
                                  minWidth: 0,
                                  flex: 1
                                }
                              : { height: 36, minWidth: 0, flex: 1 }
                          }
                          onClick={(e) => {
                            e.stopPropagation();
                            navigateToMarket(market.id, 'yes', market);
                          }}
                        >
                          Yes
                        </Button>
                        <Button
                          className="no-btn"
                          style={
                            condition.no_coin_price > 50
                              ? {
                                  backgroundColor: '#E74801',
                                  borderColor: '#E74801',
                                  color: 'white',
                                  height: 36,
                                  minWidth: 0,
                                  flex: 1
                                }
                              : { height: 36, minWidth: 0, flex: 1 }
                          }
                          onClick={(e) => {
                            e.stopPropagation();
                            navigateToMarket(market.id, 'no', market);
                          }}
                        >
                          No
                        </Button>
                      </div>
                    ))}
                </div>
              </div>
              <div
                style={{
                  width: '100%',
                  height: 1,
                  background: theme.name === 'dark' ? '#2C3F4F' : '#E7E7E7',
                  margin: '8px 0 2px 0'
                }}
              />
              <div
                style={{
                  width: '100%',
                  textAlign: 'left',
                  color: '#8C939F',
                  fontSize: 12,
                  fontWeight: 100,
                  lineHeight: '16px',
                  marginTop: 2,
                  marginBottom: 0,
                  paddingLeft: 2,
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <span>{'$ '+formatNumber(market.vol/1e18) + ' Vol.'}</span>
                {/* <Tooltip title={isFavorite ? t('favorite.remove') : t('favorite.add')}> */}
                  <div 
                    onClick={(e) => toggleFavorite(e, market.id)}
                    style={{ 
                      cursor: 'pointer',
                      fontSize: '16px',
                      color: isFavorite ? '#F8CB46' : theme.name === 'dark' ? '#8C939F' : '#BFBFBF',
                      marginRight: '8px'
                    }}
                  >
                    {isFavorite ? <StarFilled /> : <StarOutlined />}
                  </div>
                {/* </Tooltip> */}
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <Content>
      {/* 添加标题 */}
      <div style={{
        margin: isMobile ? "16px 16px 16px" : "24px 0 24px",
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Typography.Title level={isMobile ? 4 : 3} style={{ margin: 0, color: theme.textColor }}>
          {t('drawer.myFavorites')}
        </Typography.Title>
      </div>

      {contextHolder}

      {/* Market Cards */}
      <Row gutter={[16, 16]} style={{ padding: isMobile ? "0 16px" : "0 0px" }}>
        {displayedData.map((market) => (
          <Col xs={24} sm={12} md={8} lg={6} key={market.id}>
            {renderMarketCard(market)}
          </Col>
        ))}
      </Row>
      {/* Error State */}
      {error && !loading && (
        <div style={{ textAlign: "center", margin: "20px 0", padding: "10px" }}>
          <Empty description={error} />
          <Button
            type="primary"
            onClick={() => {
              setError(null);
              loadGameData();
            }}
            style={{ marginTop: 16 }}
          >
            {t('common.retry') || '重试'}
          </Button>
        </div>
      )}

      {/* Load More Indicator */}
      {hasMore && (
        <div
          ref={loadMoreRef}
          style={{ textAlign: "center", margin: "20px 0", padding: "10px" }}
        >
          {loading && <Spin />}
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && displayedData.length === 0 && (
        <Empty
          description={t("favorite.noBookmarks") || "暂无收藏的活动"}
          style={{ margin: "40px 0" }}
        />
      )}
    </Content>
  );
};

export default FavoritesPage;
