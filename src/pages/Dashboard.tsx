import React, { useState, useEffect } from 'react';
import { Layout, Typography, Card, message } from 'antd';
import { useTheme } from '../theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import { LineChart } from '../components/charts/LineChart';
import { DualLineChart } from '../components/charts/DualLineChart';
import { useMediaQuery, breakpoints } from '../hooks/useMediaQuery';
// import Web3 from 'web3';
import { dappApi } from '../services/request';
import { DAPP_API } from '../services/apiPaths';

import iconB from '../assets/figma_images/wallet_icon_b.svg';
import icon from '../assets/figma_images/wallet_icon.svg';
import icon2B from '../assets/figma_images/earnings_icon_b.svg';
import icon2 from '../assets/figma_images/earnings_icon.svg';
import icon3B from '../assets/figma_images/activity_icon_b.svg';
import icon3 from '../assets/figma_images/activity_icon.svg';
import iconVaultB from '../assets/figma_images/icon-deposit.svg';
import iconVault from '../assets/figma_images/icon-depositL.svg';
import iconFrozenB from '../assets/figma_images/icon-gas.svg';
import iconFrozen from '../assets/figma_images/icon-gasL.svg';
import './Dashboard.css';
import { useUser } from '../context/UserContext';

const { Content } = Layout;
const { Title } = Typography;

const Dashboard: React.FC = () => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';
  const isMobile = useMediaQuery(breakpoints.mobile);
  const [walletAddress, setWalletAddress] = useState('');
  const { address } = useUser();

  // Chart data states
  const [balanceData, setBalanceData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [earningsData, setEarningsData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [activityQuantityData, setActivityQuantityData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [activityAmountData, setActivityAmountData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [availableAssetsData, setAvailableAssetsData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [frozenAssetsData, setFrozenAssetsData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [chartDates, setChartDates] = useState<string[]>(['0']);
  const [chartDates2, setChartDates2] = useState<string[]>(['0']);
  const [chartDates3, setChartDates3] = useState<string[]>(['0']);
  const [chartDates4, setChartDates4] = useState<string[]>(['0']);
  const [chartDates5, setChartDates5] = useState<string[]>(['0']);
  const [messageApi, contextHolder] = message.useMessage();
  
  // 通用消息提示方法，可以传入消息类型
  const showMessage = (msg: String, type: 'success' | 'error' | 'warning' ) => {
    messageApi.open({
      type: type,
      content: msg,
      className: 'custom-class',
      style: {
        // marginTop: '20vh',
        color: '#000',
        zIndex: 9999,
      },
    });
  };
  
  // 保留原有方法，但使用通用方法实现
  const errorMsg = (msg: String) => showMessage(msg, 'error');
  // const warningMsg = (msg: String) => showMessage(msg, 'warning');
  // const successMsg = (msg: String) => showMessage(msg, 'success');

  // 使用web3.js获取钱包地址
  useEffect(() => {
    const getWalletAddress = async () => {
      try {
        if (window.ethereum) {
          // const web3 = new Web3(window.ethereum);
          // await (window.ethereum as any).request({ method: 'eth_requestAccounts' });
          // const accounts = await web3.eth.getAccounts();
          // const address = accounts[0];
          setWalletAddress(address);
        } else {
          // warningMsg('请安装MetaMask或其他以太坊钱包');
        }
      } catch (error) {
        console.error('获取钱包地址失败:', error);
        errorMsg('获取钱包地址失败');
      }
    };

    getWalletAddress();
  }, []);

  // 获取数据
  useEffect(() => {
    const fetchBalanceEarning = async () => {
      if (!walletAddress) return;

      try {
        console.log('Fetching earnings data for address:', walletAddress);
        const response = await dappApi.get(`${DAPP_API.D_EARNING}/${walletAddress}`);
        console.log('Earnings response:', response);
        if (response && response.data && response.data.length > 0) {
          const formattedData = response.data.map((item: any) => ({
            date: new Date(item.date).toLocaleDateString('zh-CN', {
              month: 'numeric',
              day: 'numeric',
            }),
            value: Number(item.value),
          }));

          console.log('Formatted earnings data:', formattedData);
          // 反转数据顺序，使其从大到小排列
          // const reversedData = [...formattedData].reverse();
          setEarningsData(formattedData.map((item: any) => item.value));
          setChartDates(formattedData.map((item: any) => item.date));
        } else {
          console.warn('No earnings data received:', response);
          // 使用默认数据
          setEarningsData([100, 200, 150, 300, 250, 400, 350]);
        }
      } catch (error: any) {
        console.error('获取收益数据失败:', error);
        console.error('Error details:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
        });
        errorMsg('获取收益数据失败');
        // 使用默认数据
        setEarningsData([100, 200, 150, 300, 250, 400, 350]);
      }
    };

    const fetchBalance = async () => {
      if (!walletAddress) return;

      try {
        console.log('Fetching balance data for address:', walletAddress);
        const response = await dappApi.get(`${DAPP_API.D_DAILY}/${walletAddress}`);
        console.log('Balance response:', response);
        if (response && response.data && response.data.length > 0) {
          const formattedData = response.data.map((item: any) => ({
            date: new Date(item.date).toLocaleDateString('zh-CN', {
              month: 'numeric',
              day: 'numeric',
            }),
            value: Number(item.value),
          }));

          console.log('Formatted balance data:', formattedData);
          // 反转数据顺序，使其从大到小排列
          // const reversedData = [...formattedData].reverse();
          setBalanceData(formattedData.map((item: any) => item.value));
          setChartDates2(formattedData.map((item: any) => item.date));
        } else {
          console.warn('No balance data received:', response);
          // 使用默认数据
          setBalanceData([0]);
        }
      } catch (error: any) {
        console.error('获取余额数据失败:', error);
        console.error('Error details:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
        });
        errorMsg('获取余额数据失败');
        // 使用默认数据
        setBalanceData([0]);
      }
    };

    const fetchActivity = async () => {
      if (!walletAddress) return;

      try {
        console.log('Fetching activity data for address:', walletAddress);
        const response = await dappApi.get(`${DAPP_API.D_ORDER}/${walletAddress}`);
        console.log('Activity response:', response);
        if (response && response.data && response.data.length > 0) {
          const formattedData = response.data.map((item: any) => ({
            date: new Date(item.date).toLocaleDateString('zh-CN', {
              month: 'numeric',
              day: 'numeric',
            }),
            value: Number(item.value),
            type: item.type,
          }));

          console.log('Formatted activity data:', formattedData);
          // 反转数据顺序，使其从大到小排列
          // const reversedData = [...formattedData].reverse();
          const quantityData = formattedData.filter((item: any) => item.type === 'order_quantity').map((item: any) => item.value);
          const amountData = formattedData.filter((item: any) => item.type === 'order_amount').map((item: any) => item.value);
          
          // 确保数据长度一致
          const maxLength = Math.max(quantityData.length, amountData.length);
          const paddedQuantityData = [...quantityData, ...Array(maxLength - quantityData.length).fill(0)];
          const paddedAmountData = [...amountData, ...Array(maxLength - amountData.length).fill(0)];
          
          setActivityQuantityData(paddedQuantityData);
          setActivityAmountData(paddedAmountData);
          // const quantityData = reversedData.filter((item: any) => item.type === 'order_quantity').map((item: any) => item.date);
          setChartDates3(formattedData.filter((item: any) => item.type === 'order_quantity').map((item: any) => item.date));
        } else {
          console.warn('No activity data received:', response);
          // 使用默认数据
          setActivityQuantityData([0]);
          setActivityAmountData([0]);
        }
      } catch (error: any) {
        console.error('获取活动数据失败:', error);
        console.error('Error details:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
        });
        errorMsg('获取活动数据失败');
        // 使用默认数据
        setActivityQuantityData([0]);
        setActivityAmountData([0]);
      }
    };

    // 获取可用资产数据
    const fetchAvailableAssets = async () => {
      if (!walletAddress) return;

      try {
        // 这里应该是从API获取可用资产数据
        // 由于API可能尚未实现，使用模拟数据
        const mockDates = ['5/1', '5/2', '5/3', '5/4', '5/5', '5/6', '5/7'];
        const mockData = [500, 550, 600, 580, 650, 700, 750];
        
        setAvailableAssetsData(mockData);
        setChartDates4(mockDates);
      } catch (error: any) {
        console.error('获取可用资产数据失败:', error);
        errorMsg('获取可用资产数据失败');
        // 使用默认数据
        setAvailableAssetsData([500, 550, 600, 580, 650, 700, 750]);
      }
    };

    // 获取冻结资产数据
    const fetchFrozenAssets = async () => {
      if (!walletAddress) return;

      try {
        // 这里应该是从API获取冻结资产数据
        // 由于API可能尚未实现，使用模拟数据
        const mockDates = ['5/1', '5/2', '5/3', '5/4', '5/5', '5/6', '5/7'];
        const mockData = [200, 180, 220, 250, 230, 260, 240];
        
        setFrozenAssetsData(mockData);
        setChartDates5(mockDates);
      } catch (error: any) {
        console.error('获取冻结资产数据失败:', error);
        errorMsg('获取冻结资产数据失败');
        // 使用默认数据
        setFrozenAssetsData([200, 180, 220, 250, 230, 260, 240]);
      }
    };

    fetchBalance();
    fetchBalanceEarning();
    fetchActivity();
    fetchAvailableAssets();
    fetchFrozenAssets();
  }, [walletAddress]);

  // Chart heights
  // const chartHeight = isMobile ? 200 : 220;
  // const activityChartHeight = isMobile ? 350 : 400;

  return (
    <Layout className={`dashboard-layout ${themeClass}`} style={{marginTop: isMobile? '-70px':'' }}>    
      <Content className="dashboard-content" >
      {contextHolder}
        <div className="dashboard-title">
          <Title level={isMobile ? 3 : 2}>{t('dashboard.title')}</Title>
        </div>
        
        <div className="dashboard-charts">
          <Card className={`balance-card ${themeClass}`} bordered={false} style={{height: '260px',marginLeft: '1px'}}>
            <div className="card-header">
              <div className="card-icon-container">
                <img src={theme.name === 'dark' ? icon : iconB} className="card-icon" />
              </div>
              <Title level={4}>{t('dashboard.balance')}</Title>
            </div>
            
            <div className="chart-container">
              <LineChart 
                data={balanceData} 
                xAxisData={chartDates2} 
                theme={theme.name === 'dark' ? 'dark' : 'light'} 
                color="#25AE60"
                showSymbol={true}
              />
            </div>
          </Card>
          
          <Card className={`earnings-card ${themeClass}`} bordered={false} style={{height: '260px'}}>
            <div className="card-header">
              <div className="card-icon-container">
                <img src={theme.name === 'dark' ? icon2 : icon2B} className="card-icon" />
              </div>
              <Title level={4}>{t('dashboard.earnings')}</Title>
            </div>
            
            <div className="chart-container">
              <LineChart 
                data={earningsData} 
                xAxisData={chartDates} 
                theme={theme.name === 'dark' ? 'dark' : 'light'} 
                color="#2C9CDC"
                showSymbol={true}
              />
            </div>
          </Card>
          
          {/* 可用资产卡片 */}
          <Card className={`available-assets-card ${themeClass}`} bordered={false} style={{height: '260px'}}>
            <div className="card-header">
              <div className="card-icon-container">
                <img src={theme.name === 'dark' ? iconVault : iconVaultB} className="card-icon" />
              </div>
              <Title level={4}>{t('dashboard.availableAssets')}</Title>
            </div>
            
            <div className="chart-container">
              <LineChart 
                data={availableAssetsData} 
                xAxisData={chartDates4} 
                theme={theme.name === 'dark' ? 'dark' : 'light'} 
                color="#F5A623"
                showSymbol={true}
              />
            </div>
          </Card>
          
          {/* 冻结资产卡片 */}
          <Card className={`frozen-assets-card ${themeClass}`} bordered={false} style={{height: '260px'}}>
            <div className="card-header">
              <div className="card-icon-container">
                <img src={theme.name === 'dark' ? iconFrozen : iconFrozenB} className="card-icon" />
              </div>
              <Title level={4}>{t('dashboard.frozenAssets')}</Title>
            </div>
            
            <div className="chart-container">
              <LineChart 
                data={frozenAssetsData} 
                xAxisData={chartDates5} 
                theme={theme.name === 'dark' ? 'dark' : 'light'} 
                color="#E74801"
                showSymbol={true}
              />
            </div>
          </Card>
          
          <Card className={`activity-card ${themeClass}`} bordered={false} style={{height: '320px', gridColumn: '1 / -1'}}>
            <div className="card-header">
              <div className="card-icon-container">
                <img src={theme.name === 'dark' ? icon3 : icon3B} className="card-icon" />
              </div>
              <Title level={4}>{t('dashboard.activity')}</Title>
            </div>
            
            <div className="chart-container">
              <div className="activity-legends">
                <div className="legend-item">
                  <span>{t('dashboard.orderQuantity')}</span>
                  <div className="legend-color quantity-color"></div>
                </div>
                <div className="legend-item">
                  <span>{t('dashboard.orderAmount')}</span>
                  <div className="legend-color amount-color"></div>
                </div>
              </div>
              
              <div className="activity-chart" style={{height: '250px'}}>
                <DualLineChart 
                  data1={activityQuantityData} 
                  data2={activityAmountData}
                  xAxisData={chartDates3} 
                  theme={theme.name === 'dark' ? 'dark' : 'light'}
                />
              </div>
            </div>
          </Card>
        </div>
      </Content>
    </Layout>
  );
};

export default Dashboard;