import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Button, Avatar, Typography, Spin, Tooltip } from 'antd';
import { BarChartOutlined, DollarCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useTheme } from '../theme/ThemeProvider';
import { fetchLeaderboardData } from '../services/leaderboardService';
import type { LeaderboardUser } from '../services/leaderboardService';
import './Leaderboard.css';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

const Leaderboard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const [timeFilter, setTimeFilter] = useState<string>('all');
  const [volumeData, setVolumeData] = useState<LeaderboardUser[]>([]);
  const [profitData, setProfitData] = useState<LeaderboardUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [countdown, setCountdown] = useState<string>('');
  
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';

  // 计算倒计时
  useEffect(() => {
    const calculateTimeLeft = () => {
      let targetTime: Date;
      const now = new Date();
      
      switch (timeFilter) {
        case 'day':
          // 倒计时到今天结束
          targetTime = new Date(now);
          targetTime.setHours(23, 59, 59, 999);
          break;
        case 'week': {
          // 倒计时到本周日
          const daysToSunday = 7 - now.getDay();
          targetTime = new Date(now);
          targetTime.setDate(now.getDate() + daysToSunday);
          targetTime.setHours(23, 59, 59, 999);
          break;
        }
        case 'month':
          // 倒计时到本月最后一天
          targetTime = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
          break;
        default:
          // 'all' 不显示倒计时
          setCountdown('');
          return;
      }
      
      const timeDiff = targetTime.getTime() - now.getTime();
      
      if (timeDiff <= 0) {
        setCountdown('已结束');
        return;
      }
      
      // 计算剩余时间
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
      
      // 格式化倒计时
      if (days > 0) {
        setCountdown(`${days}d ${hours}h ${minutes}m ${seconds}s`);
      } else if (hours > 0) {
        setCountdown(`${hours}h ${minutes}m ${seconds}s`);
      } else {
        setCountdown(`${minutes}m ${seconds}s`);
      }
    };
    
    // 初始计算
    calculateTimeLeft();
    
    // 每秒更新倒计时
    const timer = setInterval(calculateTimeLeft, 1000);
    
    // 组件卸载时清除定时器
    return () => clearInterval(timer);
  }, [timeFilter]);

  // Fetch leaderboard data when component mounts or timeFilter changes
  useEffect(() => {
    const getLeaderboardData = async () => {
      setLoading(true);
      try {
        const data = await fetchLeaderboardData(timeFilter);
        setVolumeData(data.volume);
        setProfitData(data.profit);
      } catch (error) {
        console.error('Failed to fetch leaderboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    getLeaderboardData();
  }, [timeFilter]);

  // Generate random avatar colors
  const getRandomColor = (index: number) => {
    const colors = [
      'linear-gradient(135deg, #ff9966, #ff5e62)',
      'linear-gradient(135deg, #4facfe, #00f2fe)',
      'linear-gradient(135deg, #43e97b, #38f9d7)',
      'linear-gradient(135deg, #fa709a, #fee140)',
      'linear-gradient(135deg, #7f7fd5, #86a8e7)',
      'linear-gradient(135deg, #f6d365, #fda085)',
      'linear-gradient(135deg, #5ee7df, #b490ca)',
      'linear-gradient(135deg, #c471f5, #fa71cd)',
      'linear-gradient(135deg, #48c6ef, #6f86d6)'
    ];
    return colors[index % colors.length];
  };

  // Get rank badge class
  const getRankBadgeClass = (rank: number) => {
    if (rank === 1) return 'rank-badge rank-1';
    if (rank === 2) return 'rank-badge rank-2';
    if (rank === 3) return 'rank-badge rank-3';
    return 'rank-badge rank-other';
  };

  // Format number function for leaderboard display
  const formatNumber = (num: number): string => {
    return formatCompactNumber(num);
  };

  // Enhanced compact number formatting
  const formatCompactNumber = (num: number): string => {
    if (num >= 1000000000) {
      return `$${(num / 1000000000).toFixed(1)}b`;
    } else if (num >= 1000000) {
      const formatted = (num / 1000000).toFixed(1);
      return `$${formatted.endsWith('.0') ? formatted.slice(0, -2) : formatted}m`;
    } else if (num >= 1000) {
      const formatted = (num / 1000).toFixed(1);
      return `$${formatted.endsWith('.0') ? formatted.slice(0, -2) : formatted}k`;
    }
    return `$${Math.round(num).toLocaleString()}`;
  };

  // Format full number with commas for tooltips
  const formatFullNumber = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(num);
  };

  // Create user tooltip content
  const createUserTooltip = (user: LeaderboardUser, isVolumeLeaderboard: boolean) => {
    // const joinedDate = 'Jul 2025'; // Mock data - replace with actual joined date from API
    // const positions = '$0'; // Mock data - replace with actual positions from API
    const profitLoss = formatCompactNumber(Math.abs(parseFloat(user.user_earning || '0')));
    const volume = formatCompactNumber(parseFloat(user.volume || '0'));
    
    return (
      <div className="user-tooltip">
        <div className="tooltip-header">
          <Avatar 
            size={60} 
            src={user.profile_image}
            style={{ 
              background: 'linear-gradient(135deg, #ff9966, #ff5e62)',
              marginRight: '12px'
            }}
          />
          <div className="tooltip-user-info">
            <div className="tooltip-username">{user.username || `${user.address.slice(0, 6)}...${user.address.slice(-4)}`}</div>
            {/* <div className="tooltip-joined">Joined {joinedDate}</div> */}
          </div>
        </div>
        <div className="tooltip-stats">
          {/* <div className="stat-item">
            <div className="stat-value">{positions}</div>
            <div className="stat-label">Positions</div>
          </div> */}
          <div className="stat-item">
            <div className="stat-value truncate" style={{ color: parseFloat(user.user_earning || '0') >= 0 ? '#52c41a' : '#ff4d4f' }}>{profitLoss}</div>
            <div className="stat-label">Profit/loss</div>
          </div>
          <div className="stat-item">
            <div className="stat-value truncate">{volume}</div>
            <div className="stat-label">Volume</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`leaderboard-container ${themeClass}`}>
      <Title level={1} style={{ textAlign: 'center', color: theme.textColor, marginBottom: '40px' }}>
        Leaderboard
      </Title>

      {/* Time filter buttons */}
      <div className="time-filter-container">
        <Button
          type={timeFilter === 'day' ? 'primary' : 'default'}
          shape="round"
          onClick={() => setTimeFilter('day')}
          className={`time-filter-btn ${timeFilter === 'day' ? 'active' : ''}`}
        >
          {t('leaderboard.day')}
        </Button>
        <Button
          type={timeFilter === 'week' ? 'primary' : 'default'}
          shape="round"
          onClick={() => setTimeFilter('week')}
          className={`time-filter-btn ${timeFilter === 'week' ? 'active' : ''}`}
        >
          {t('leaderboard.Week')}
        </Button>
        <Button
          type={timeFilter === 'month' ? 'primary' : 'default'}
          shape="round"
          onClick={() => setTimeFilter('month')}
          className={`time-filter-btn ${timeFilter === 'month' ? 'active' : ''}`}
        >
          {t('leaderboard.Month')}
        </Button>
        <Button
          type={timeFilter === 'all' ? 'primary' : 'default'}
          shape="round"
          onClick={() => setTimeFilter('all')}
          className={`time-filter-btn ${timeFilter === 'all' ? 'active' : ''}`}
        >
          {t('leaderboard.All')}
        </Button>
      </div>
      
      {/* 倒计时显示 */}
      {countdown&&(
        <div className="countdown-container">
          <ClockCircleOutlined className="countdown-icon" />
          <Text className="countdown-text">Resets in {countdown}</Text>
        </div>
      )}

      {loading ? (
        <div className="leaderboard-loading">
          <Spin size="large" />
        </div>
      ) : (
        <Row gutter={24}>
          {/* Volume Leaderboard */}
          <Col xs={24} lg={12}>
            <Card 
              className="leaderboard-card"
              title={
                <div className="leaderboard-card-title">
                  <BarChartOutlined style={{ fontSize: '24px', color: '#4096ff' }} />
                  <span>Volume</span>
                </div>
              }
              bordered={false}
            >
              {volumeData.map((user, index) => (
                <div className="leaderboard-item" key={user.address}>
                  <div className="leaderboard-rank-avatar">
                    <div className={getRankBadgeClass(user.rank)}>{user.rank}</div>
                    <Tooltip 
                      title={createUserTooltip(user, true)}
                      placement="top"
                      overlayClassName="user-info-tooltip"
                      mouseEnterDelay={0.5}
                    >
                      <div className="user-info-hover">
                        <Avatar 
                          size={44} 
                          src={user.profile_image} 
                          style={{ 
                            background: getRandomColor(index)
                          }}
                        />
                        <span className="username">{user.username || `${user.address.slice(0, 6)}...${user.address.slice(-4)}`}</span>
                      </div>
                    </Tooltip>
                  </div>
                  <Tooltip 
                    title={formatFullNumber(parseFloat(user.volume || '0'))} 
                    placement="left"
                    overlayClassName="amount-tooltip"
                  >
                    <div className="amount">{formatNumber(parseFloat(user.volume || '0'))}</div>
                  </Tooltip>
                </div>
              ))}
            </Card>
          </Col>

          {/* Profit Leaderboard */}
          <Col xs={24} lg={12}>
            <Card 
              className="leaderboard-card"
              title={
                <div className="leaderboard-card-title">
                  <DollarCircleOutlined style={{ fontSize: '24px', color: '#4096ff' }} />
                  <span>Profit</span>
                </div>
              }
              bordered={false}
            >
              {profitData.map((user, index) => (
                <div className="leaderboard-item" key={user.address}>
                  <div className="leaderboard-rank-avatar">
                    <div className={getRankBadgeClass(user.rank)}>{user.rank}</div>
                    <Tooltip 
                      title={createUserTooltip(user, false)}
                      placement="top"
                      overlayClassName="user-info-tooltip"
                      mouseEnterDelay={0.5}
                    >
                      <div className="user-info-hover">
                        <Avatar 
                          size={44} 
                          src={user.profile_image} 
                          style={{ 
                            background: getRandomColor(index)
                          }}
                        />
                        <span className="username">{user.username || `${user.address.slice(0, 6)}...${user.address.slice(-4)}`}</span>
                      </div>
                    </Tooltip>
                  </div>
                  <Tooltip 
                    title={formatFullNumber(parseFloat(user.earning || '0'))} 
                    placement="left"
                    overlayClassName="amount-tooltip"
                  >
                    <div className="amount">{formatNumber(parseFloat(user.earning || '0'))}</div>
                  </Tooltip>
                </div>
              ))}
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Leaderboard; 