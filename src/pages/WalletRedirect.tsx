import React, { useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
import { useModal } from '@particle-network/connectkit';
import { useUser } from '../context/UserContext';
import { useWallets,useAccount } from '@particle-network/connectkit';
const WalletRedirect: React.FC = () => {
  // const navigate = useNavigate();
  const { setOpen } = useModal();
  const { isLoggedIn } = useUser();
  const { isConnected } = useAccount();
  const [primaryWallet] = useWallets();
  useEffect(() => {
    // 检查是否已经登录成功
    const checkLoginSuccess = () => {
      // 如果已经连接并且有主钱包信息，说明登录成功
      // if (isConnected && primaryWallet) {
      //   // 获取主窗口的引用
      //   const mainWindow = window.opener;
      //   if (mainWindow) {
      //     // 刷新主窗口
      //     mainWindow.location.reload();
      //   }else {
      //     // 移动端/无opener，尝试返回
      //     setTimeout(() => {
      //       window.history.back();
      //     }, 1000);
      //   }
      //   // 关闭当前窗口
      //   window.close();
      // }
    };

    // 如果已经登录，直接处理
    if (isLoggedIn) {
      checkLoginSuccess();
      return;
    }

    // 打开 Particle 登录模态框
    setOpen(true);

    // 监听登录状态变化
    const intervalId = setInterval(checkLoginSuccess, 500);

    // 清理定时器
    return () => clearInterval(intervalId);
  }, [isLoggedIn, isConnected, primaryWallet, setOpen]);

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      backgroundColor: '#1D2B39'
    }}>
      <div style={{ 
        textAlign: 'center',
        color: '#fff'
      }}>
        <h2>正在处理钱包登录...</h2>
        <p>请在弹出的窗口中完成登录操作</p>
      </div>
    </div>
  );
};

export default WalletRedirect; 