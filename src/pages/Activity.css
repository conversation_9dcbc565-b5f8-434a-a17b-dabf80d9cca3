.activity-container {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.dark-theme .activity-container {
  color: #fff;
}

.light-theme .activity-container {
  color: #333;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.activity-filters {
  display: flex;
  gap: 10px;
}

.filter-button {
  border-radius: 20px !important;
  padding: 0 15px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
}

.dark-theme .filter-button {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
  border: none !important;
}

.light-theme .filter-button {
  background-color: rgba(0, 0, 0, 0.05) !important;
  color: #333 !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* 修复下拉菜单在暗黑模式下的样式 */
.dark-theme .ant-dropdown .ant-dropdown-menu {
  background-color: #1f1f1f !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2) !important;
}

.dark-theme .ant-dropdown .ant-dropdown-menu-item {
  color: #fff !important;
}

.dark-theme .ant-dropdown .ant-dropdown-menu-item:hover {
  background-color: #333 !important;
}

.activity-list {
  background-color: transparent;
}

.dark-theme .activity-item {
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.light-theme .activity-item {
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.activity-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.activity-left {
  display: flex;
  align-items: center;
}

.activity-image {
  margin-right: 16px;
}

.activity-details {
  display: flex;
  flex-direction: column;
}

.dark-theme .activity-title {
  font-size: 16px;
  color: #fff;
  margin-bottom: 8px;
}

.light-theme .activity-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.dark-theme .activity-user {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.light-theme .activity-user {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(0, 0, 0, 0.7);
  font-size: 14px;
}

.username {
  color: #1890ff;
  margin-right: 4px;
}

.dark-theme .action, .dark-theme .price, .dark-theme .amount {
  color: rgba(255, 255, 255, 0.7);
}

.light-theme .action, .light-theme .price, .light-theme .amount {
  color: rgba(0, 0, 0, 0.7);
}

.dark-theme .activity-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

.light-theme .activity-time {
  color: rgba(0, 0, 0, 0.5);
  font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .activity-container {
    padding: 15px;
  }
  
  .activity-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .activity-filters {
    width: 100%;
    justify-content: space-between;
  }
  
  .activity-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .activity-time {
    margin-top: 10px;
    margin-left: 64px;
  }
} 