/* 订单页面样式 */
.orders-layout {
  min-height: calc(100vh  - 124px);
}

.orders-layout.light-theme {
  background-color: #ffffff;
}

.orders-layout.dark-theme {
  background-color: #1D2B39;
}

.orders-content {
  padding: 0 24px 24px;
}

.orders-title-section {
  padding: 0px 16px;
  /* margin-bottom: 16px; */
}

.orders-title {
  color: inherit;
  margin: 0;
}

.orders-title.light-theme {
  color: #000000;
}

.orders-title.dark-theme {
  color: #FFFFFF;
}

/* 桌面端表格样式 */
.order-table-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: transparent;
}

.orders-table {
  width: 100%;
  border-radius: 8px;
}

.orders-table.light-theme {
  background-color: #FFFFFF;
}

.orders-table.dark-theme {
  background-color: #1D2B39;
}

.orders-table.light-theme .ant-table {
  background-color: transparent;
  color: #000000;
}

.orders-table.dark-theme .ant-table {
  background-color: transparent;
  color: #FFFFFF;
}

.orders-table.light-theme .ant-table-thead > tr > th {
  background-color: #FFFFFF;
  color: #777E8C;
  border-bottom: 1px solid #E7E7E7;
  font-weight: normal;
}

.orders-table.dark-theme .ant-table-thead > tr > th {
  background-color: #1D2B39;
  color: #97A0A4;
  border-bottom: 1px solid #2C3F4F;
  font-weight: normal;
}

.orders-table.light-theme .ant-table-tbody > tr > td {
  background-color: #FFFFFF;
  color: #000000;
  border-bottom: 1px solid #E7E7E7;
}

.orders-table.dark-theme .ant-table-tbody > tr > td {
  background-color: #1D2B39;
  color: #FFFFFF;
  border-bottom: 1px solid #2C3F4F;
}

.orders-table.light-theme .ant-table-tbody > tr:hover > td {
  background-color: #F5F5F5;
  cursor: pointer;
}

.orders-table.dark-theme .ant-table-tbody > tr:hover > td {
  background-color: #243546;
  cursor: pointer;
}

/* 条件标签样式 */
.condition-tag {
  display: inline-block;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  min-width: 60px;
  font-weight: bold;
  font-size: 12px;
}

.condition-tag.yes {
  background-color: #25AE6020;
  color: #25AE60;
}

.condition-tag.no {
  background-color: #E7480120;
  color: #E74801;
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.status-dot {
  display: inline-block;
  min-width: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0; /* 防止圆点被挤压 */
}

.status-text {
  flex: 1;
  white-space: normal;
  word-break: break-word;
  font-size: 12px;
}

/* 不同状态的颜色 */
.waiting .status-dot {
  background-color: #25AE60;
}

.waiting .status-text {
  color: #25AE60;
}

.terminated .status-dot {
  background-color: #FF4D4F;
}

.terminated .status-text {
  color: #FF4D4F;
}

.partial .status-dot {
  background-color: #FAAD14;
}

.partial .status-text {
  color: #FAAD14;
}

/* 暗色主题样式调整 */
.dark-theme .waiting .status-dot {
  background-color: #25AE60;
}

.dark-theme .waiting .status-text {
  color: #25AE60;
}

.dark-theme .terminated .status-dot {
  background-color: #F5222D;
}

.dark-theme .terminated .status-text {
  color: #F5222D;
}

.dark-theme .partial .status-dot {
  background-color: #FADB14;
}

.dark-theme .partial .status-text {
  color: #FADB14;
}

/* 查看详情按钮 */
.view-detail-btn {
  padding: 0;
  margin: 0;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-detail-btn .anticon {
  color: #2C9CDC;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 16px;
}

.pagination-btn {
  border: none;
  background: transparent;
  cursor: pointer;
  color: #2C9CDC;
  font-size: 14px;
  padding: 0;
}

.pagination-btn:hover {
  text-decoration: underline;
}

.orders-pagination.light-theme .ant-pagination-item {
  border-color: #E7E7E7;
  background-color: #FFFFFF;
}

.orders-pagination.dark-theme .ant-pagination-item {
  border-color: #2C3F4F;
  background-color: #1D2B39;
}

.orders-pagination.light-theme .ant-pagination-item-active {
  border-color: #2C9CDC;
  background-color: #2C9CDC;
}

.orders-pagination.dark-theme .ant-pagination-item-active {
  border-color: #2C9CDC;
  background-color: #2C9CDC;
}

.orders-pagination.light-theme .ant-pagination-item a {
  color: #000000;
}

.orders-pagination.dark-theme .ant-pagination-item a {
  color: #FFFFFF;
}

.orders-pagination.light-theme .ant-pagination-item-active a,
.orders-pagination.dark-theme .ant-pagination-item-active a {
  color: #FFFFFF;
}

/* 移动端样式 */
.mobile-orders-container {
  padding-bottom: 60px;
}

.mobile-order-card {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  cursor: pointer;
}

.mobile-order-card.light-theme {
  background-color: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.mobile-order-card.dark-theme {
  background-color: #243546;
}

.mobile-order-title {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile-order-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.order-condition {
  color: #25AE60;
  font-weight: bold;
}

.order-condition.no {
  color: #E74801;
}

.order-price {
  font-weight: bold;
  font-size: 16px;
}

.mobile-order-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #97A0A4;
}

.order-currency, .order-time {
  font-size: 12px;
  color: #97A0A4;
}

/* 移动端底部导航 */
.mobile-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #1D2B39;
  border-top: 1px solid #2C3F4F;
  z-index: 999;
}

.mobile-tab-bar {
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #97A0A4;
}

.tab-item.active {
  color: #FFFFFF;
}

.icon {
  font-size: 20px;
  margin-bottom: 2px;
}

/* 订单详情样式 */
.order-detail-drawer .ant-drawer-body {
  padding: 0;
}

.order-detail-container {
  padding: 24px;
}

.order-detail-container.light-theme {
  background-color: #FFFFFF;
  color: #000000;
}

.order-detail-container.dark-theme {
  background-color: #1D2B39;
  color: #FFFFFF;
}

.detail-title {
  margin-bottom: 24px;
  font-size: 18px;
  font-weight: bold;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-label {
  display: block;
  font-size: 12px;
  color: #97A0A4;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  display: block;
}

.detail-value.id-value {
  word-break: break-all;
}

.total-amount-section {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 24px;
  padding: 8px 0;
  border-top: 1px solid #2C3F4F;
  border-bottom: 1px solid #2C3F4F;
}

.total-label {
  font-size: 14px;
  color: #97A0A4;
  margin-right: 8px;
}

.total-value {
  font-size: 20px;
  font-weight: bold;
}

/* 媒体查询 */
@media (max-width: 768px) {
  .orders-content {
    padding: 0 16px 16px;
  }
  
  .total-amount-section {
    padding: 6px 0;
  }
  
  .total-value {
    font-size: 18px;
  }
}

/* 取消订单弹窗样式 */
.cancel-order-modal.dark-theme .ant-modal-content {
  background-color: #1D2B39;
  color: #FFFFFF;
}

.cancel-order-modal.dark-theme .ant-modal-header {
  background-color: #1D2B39;
  border-bottom: 1px solid #435363;
}

.cancel-order-modal.dark-theme .ant-modal-title {
  color: #FFFFFF;
}

.cancel-order-modal.dark-theme .ant-modal-close {
  color: #FFFFFF;
}

.cancel-order-modal.dark-theme .ant-modal-footer {
  border-top: 1px solid #435363;
}

.cancel-order-modal.dark-theme .ant-btn {
  background-color: #243546;
  border-color: #435363;
  color: #FFFFFF;
}

.cancel-order-modal.dark-theme .ant-btn-primary {
  background-color: #1677FF;
  border-color: #1677FF;
}

.cancel-order-modal.dark-theme .ant-btn-default:hover {
  background-color: #2C3E4D;
  border-color: #1677FF;
  color: #1677FF;
}

.cancel-order-modal.light-theme .ant-modal-content {
  background-color: #FFFFFF;
  color: #000000;
}

.cancel-order-modal.light-theme .ant-modal-header {
  background-color: #FFFFFF;
  border-bottom: 1px solid #E8E8E8;
}

.cancel-order-modal.light-theme .ant-modal-title {
  color: #000000;
}

.cancel-order-modal.light-theme .ant-modal-close {
  color: #000000;
}

.cancel-order-modal.light-theme .ant-modal-footer {
  border-top: 1px solid #E8E8E8;
}

.cancel-order-modal.light-theme .ant-btn {
  background-color: #FFFFFF;
  border-color: #D9D9D9;
  color: #000000;
}

.cancel-order-modal.light-theme .ant-btn-primary {
  background-color: #1677FF;
  border-color: #1677FF;
  color: #FFFFFF;
}

.cancel-order-modal.light-theme .ant-btn-default:hover {
  background-color: #F5F5F5;
  border-color: #1677FF;
  color: #1677FF;
} 