/* App Layout Styles */
.app-layout {
  display: flex;
  flex-direction: column;
}

.app-content {
  flex: 1;
  padding: 0 24px;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .app-content {
    padding: 0 16px;
    padding-bottom: 60px; /* Space for mobile footer */
  }
}

/* Modal Styles */
.deposit-withdraw-modal .ant-modal-content {
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
}

/* Light Theme Styles */
.dark-drawer.light-theme .ant-drawer-header {
  background-color: #FFFFFF;
  border-bottom-color: #E7E7E7;
}

.dark-drawer.light-theme .ant-drawer-body {
  background-color: #FFFFFF;
}

.deposit-withdraw-container.light-theme {
  background-color: #FFFFFF;
  color: #000000;
}

/* Dark Theme Styles */
.dark-drawer.dark-theme .ant-drawer-header {
  background-color: #1D2B39;
  border-bottom-color: #435363;
}

.dark-drawer.dark-theme .ant-drawer-body {
  background-color: #1D2B39;
  color: #FFFFFF;
} 