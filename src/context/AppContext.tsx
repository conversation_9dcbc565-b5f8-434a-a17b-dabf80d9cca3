import React, { createContext, useState, useContext,useEffect } from 'react';
import type { ReactNode } from 'react';
import { message } from 'antd';
import { useTheme } from '../theme/ThemeProvider';
import { useUser } from './UserContext';
import {useAccount} from '@particle-network/connectkit'; 

declare global {
  interface Window {
    ethereum?: any
  }
}

// Define the type for the app context
interface AppContextType {
  isLoggedIn: boolean;
  isConnecting: boolean;
  handleConnectWallet: () => Promise<boolean>;
  handleLogout: () => void;
  // Theme related properties
  theme: any;
  themeClass: string;
  toggleTheme: () => void;
}

// Create the context with a default value
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const { theme, toggleTheme } = useTheme();
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';
  const { signer } = useUser(); // 使用 UserContext
  const { address } = useAccount();

  useEffect(() => {
    setIsLoggedIn(!!address && !!signer);
  }, [address, signer]);
  const handleConnectWallet = async (): Promise<boolean> => {
    try {
      console.log("isLoggedIn:",isLoggedIn);
      if (isLoggedIn) {
        setIsConnecting(true);
        return true;
      }else{
        return false;
      }
      
      
      // message.info('请在弹出的窗口中连接钱包');
      // 触发钱包连接
      // if (window.ethereum) {
      //   await window.ethereum.request({ method: 'eth_requestAccounts' });
      //   return true;
      // } else {
      //   message.error('未检测到钱包，请安装MetaMask或其他兼容钱包');
      //   setIsConnecting(false);
      //   return false;
      // }
    } catch (error) {
      console.error('钱包连接失败:', error);
      message.error('钱包连接失败，请重试');
      setIsConnecting(false);
      return false;
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    message.success('已断开连接');
  };


  // Value to be provided by the context
  const contextValue: AppContextType = {
    isLoggedIn,
    isConnecting,
    handleConnectWallet,
    handleLogout,
    theme,
    themeClass,
    toggleTheme
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useAppContext = () => {
  const context = useContext(AppContext);
  
  if (context === undefined) {
    throw new Error('useAppContext必须在AppProvider内部使用');
  }
  
  return context;
};

export default AppContext; 