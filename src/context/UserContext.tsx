// src/context/UserContext.tsx
import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { dappApi } from '../services/request';
import { DAPP_API } from '../services/apiPaths';
import { ethers,BrowserProvider } from "ethers";
import type { Eip1193Provider } from 'ethers';
import usdcAbi from '../contracts/mocks/MockERC20.sol/MockERC20.json';
import vaultAbi from '../contracts/Vault.sol/Vault.json';
import betMessageAbi from '../contracts/BetManager.sol/BetManager.json';
import betTokenAbi from '../contracts/BetToken.sol/BetToken.json';
// import { useConnect } from '@particle-network/authkit';
import { useEthereum } from '@particle-network/authkit';
import { useWallets, useParticleAuth,useAccount } from '@particle-network/connectkit';
import {message} from 'antd';
// import { useAccount, useConnect } from 'wagmi';

// 添加文档链接接口
interface DocLink {
  name: string;
  link: string;
}

const UserContext = createContext<any>(null);

export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const [address, setAddress] = useState<string | null>(null);
   // 新增刷新计数器
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [profileData, setProfileData] = useState<any>(null);
  const [signer, setSigner] = useState<ethers.Signer | null>(null);
  // const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const isInitialized = useRef(false);
  const isFetchingUserByToken = useRef(false);
  const isLoggingOut = useRef(false);  // 添加登出标志
  const [usdcContract, setUsdcContract] = useState<ethers.Contract | null>(null);
  const [vaultContract, setVaultContract] = useState<ethers.Contract | null>(null);
  const [betMessageContract, setBetMessageContract] = useState<ethers.Contract | null>(null);
  const [betTokenContract, setBetTokenContract] = useState<ethers.Contract | null>(null);
  // const { connectionStatus, connected } = useConnect();


  // const connectors = useConnectors();
  const [networkName, setNetworkName] = useState<string>('');
  // 添加文档链接状态
  const [docLinks, setDocLinks] = useState<DocLink[]>([]);
  const { provider } = useEthereum();
  const { getUserInfo } = useParticleAuth();
  const { isConnected } = useAccount();
  const [primaryWallet] = useWallets();
  // const { userInfo } = useAuthCore();
  const [isWallet, setIsWallet] = useState("0"); // 0 代表没有任何类型钱包登录  1 代表社交钱包登录  2 代表钱包插件登录

   // 初始化合约
   const initializeContracts = async (newSigner: ethers.Signer) => {
    try {
      console.log("初始化合约开始...");
      console.log("合约地址:", {
        USDC: import.meta.env.VITE_USDC_ADDRESS,
        VAULT: import.meta.env.VITE_VAULT_ADDRESS,
        BET_MANAGER: import.meta.env.VITE_BET_MANAGER,
        BET_TOKEN: import.meta.env.VITE_BET_TOKEN
      });
      
      const userAddress = await newSigner.getAddress();
      console.log("当前用户地址:", userAddress);
      // setAddress(userAddress);
      const usdc = new ethers.Contract(
        import.meta.env.VITE_USDC_ADDRESS,
        usdcAbi.abi,
        newSigner
      );
      console.log("USDC合约初始化成功:", usdc.target);
      
      const vault = new ethers.Contract(
        import.meta.env.VITE_VAULT_ADDRESS,
        vaultAbi.abi,
        newSigner
      );
      console.log("Vault合约初始化成功:", vault.target);
      
      const betMessage = new ethers.Contract(
        import.meta.env.VITE_BET_MANAGER,
        betMessageAbi.abi,
        newSigner
      );
      console.log("BetMessage合约初始化成功:", betMessage.target);

      const betTokenMessage = new ethers.Contract(
        import.meta.env.VITE_BET_TOKEN,
        betTokenAbi.abi,
        newSigner
      );
      console.log("BetToken合约初始化成功:", betTokenMessage.target);
      
      // 尝试查询余额
      try {
        const usdcBalance = await usdc.balanceOf(userAddress);
        console.log("USDC余额查询成功:", usdcBalance.toString());
        
        const vaultBalance = await vault.balanceOf(userAddress);
        console.log("Vault余额查询成功:", vaultBalance.toString());
      } catch (balanceError) {
        console.error("余额查询失败:", balanceError);
      }
      
      setUsdcContract(usdc);
      setVaultContract(vault);
      setBetMessageContract(betMessage);
      setBetTokenContract(betTokenMessage);
      console.log('所有合约初始化完成并设置到状态');
    } catch (error) {
      console.error('初始化合约失败:', error);
    }
  };
  // 初始化 signer
  const initializeSigner = async () => {
    if (!signer && !isInitialized.current) {
      try {
        isInitialized.current = true;
        const providerMain = new ethers.BrowserProvider(provider as Eip1193Provider, "any");
        console.log('providerMain',providerMain);
        const newSigner = await providerMain.getSigner();
        setSigner(newSigner);
        const userAddress = await newSigner.getAddress();
        setAddress(userAddress);
        const network = await providerMain.getNetwork();
        console.log('network',network.name);
        setNetworkName(network.name);
        await initializeContracts(newSigner); // 初始化合约
        console.log('Signer initialized');
      } catch (error) {
        console.error('Failed to initialize signer:', error);
        isInitialized.current = false;
      }
    }
  };

    // 初始化 signer
    const initializeSignerWallet = async () => {
      if (primaryWallet && !signer && !isInitialized.current) {
        try {
          isInitialized.current = true;
          const providerInstance = await primaryWallet.connector.getProvider();
          const provider = new BrowserProvider(providerInstance as Eip1193Provider);
          await provider.send('eth_requestAccounts', []);
          const newSigner = await provider.getSigner();
          setSigner(newSigner);
          const userAddress = await newSigner.getAddress();
          setAddress(userAddress);
          console.log("userAddress:",userAddress);
          const network = await provider.getNetwork();
          console.log('network',network.name);
          setNetworkName(network.name);
          await initializeContracts(newSigner); // 初始化合约
          console.log('Signer initialized');
        } catch (error) {
          console.error('Failed to initialize signer:', error);
          isInitialized.current = false;
        }
      }
    };


    useEffect(() => {
      const fetchUserInfo = async () => {
        // 如果正在登出，不执行登录逻辑
        if (isLoggingOut.current) {
          return;
        }

        // 使用 walletConnectorType 作为条件来避免账户未初始化错误
        if (primaryWallet?.connector?.walletConnectorType === 'particleAuth') {
          const userInfo = await getUserInfo();
          console.log('社交登录用户信息:', userInfo);
          if(isWallet!="1"){
            setIsWallet("1");
            await initializeSigner();
          }
        }else if(primaryWallet?.connector?.walletConnectorType==='evmWallet'){
          console.log('钱包插件登录用户信息:', primaryWallet);
          setIsWallet("2");
          await initializeSignerWallet();
        }
      };
      
      if (isConnected) {
          fetchUserInfo();
      }
  }, [isConnected, getUserInfo, primaryWallet]);

  useEffect(() => {
    fetchDocLinks();
    if(isWallet=="1"){
      initializeSigner();
    }else if(isWallet=="2"){
      initializeSignerWallet();
    }
  }, []);


  // 监听钱包地址变化
  useEffect(() => {
    if (window.ethereum) {
      // 初始化
      window.ethereum.request({ method: 'eth_accounts' }).then(async (accounts: string[]) => {
        if (accounts.length > 0) {
          setAddress(accounts[0]);
          // fetchUserData();
        }
      });

      // 监听事件
      const handleAccountsChanged = async (accounts: string[]) => {
        // 清除旧token，确保不会使用之前账号的token
        localStorage.removeItem('token');
        
        if (accounts.length > 0) {
          setAddress(accounts[0]);
          // fetchUserData();
        } else {
          setAddress(null);
          setSigner(null);
          setProfileData(null);
          setUsdcContract(null);
          setVaultContract(null);
          setBetMessageContract(null);
          setBetTokenContract(null);
          setNetworkName('');
          setIsWallet("0");
          isInitialized.current = false;
        }
      };
      window.ethereum.on('accountsChanged', handleAccountsChanged);

      return () => {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
      };
    }
  }, []);

  // 钱包地址变了就重新拉取用户信息
  useEffect(() => {
    const fetchUserData = async () => {
      if (!address) {
        setProfileData(null);
        return;
      }

      if (isFetchingUserByToken.current) {
        return;
      }

      isFetchingUserByToken.current = true;
      try {
        console.log('开始获取用户数据，地址:', address);
        
        // 先检查连接器类型并初始化 signer
      
        
        // 确保 signer 已经初始化
        if (!signer) {
          console.error('Signer 未初始化');
          return;
        }
        
        // 先检查localStorage中是否有有效的token
        const existingToken = localStorage.getItem('token');
        
        // 如果有token，先尝试用它获取用户信息
        if (existingToken) {
          try {
            console.log('检测到现有token，尝试使用...');
            const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`, {
              headers: {
                'Authorization': `Bearer ${existingToken}`
              }
            });
            
            // 如果成功获取到用户信息，就不需要重新签名登录了
            if (profile?.data) {
              console.log('使用现有token成功获取用户信息');
              setProfileData(profile.data);
              
              // 获取API文档链接
              await fetchDocLinks();
              
              return;
            }
          } catch (tokenError) {
            console.log('现有token已失效，需要重新登录');
            localStorage.removeItem('token');
          }
        }

        if(isWallet=="1"){
          await new Promise(resolve => setTimeout(resolve, 1000)); // 等待4秒
        }
        
        // 如果没有token或token无效，则走完整的认证流程
        // 获取 nonce
        const nonceResp = await dappApi.get(`${DAPP_API.NONCE}/${address}`);
        const nonce = nonceResp?.data?.nonce;
        
        if (!nonce) {
          throw new Error("Failed to get nonce");
        }
        
        console.log('获取到nonce:', nonce);
        
        // 签名
        console.log('开始请求签名...');
        const signature = await signer.signMessage(nonce);
        console.log('签名完成:', signature);
        
        // 登录
        const loginResp = await dappApi.post(DAPP_API.AUTH_LOGIN, {
          address,
          message: nonce,
          signature,
        });
        
        if (!loginResp.data?.access_token) {
          console.error('登录返回数据中缺少token:', loginResp);
          throw new Error('Login response missing access_token');
        }
        
        console.log('登录成功，获取到token');
        // 直接在dappRequest中设置token，确保后续请求可以使用
        const token = loginResp.data.access_token;
        localStorage.setItem('token', token);
        console.log('存储token完成, 长度:', token.length);
        
        // 手动验证token是否正确存储
        const storedToken = localStorage.getItem('token');
        if (!storedToken || storedToken !== token) {
          console.error('Token存储异常，存储的token与原始token不一致');
          console.log('原始token:', token.substring(0, 15) + '...');
          console.log('存储token:', storedToken ? storedToken.substring(0, 15) + '...' : '未找到');
        }
        
        // 短暂延迟，确保token已被保存
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 获取用户信息
        try {
          console.log('准备获取用户资料, 当前token状态:', !!localStorage.getItem('token'));
          // 不使用dappApi.get直接使用axios，绕过可能的缓存问题
          const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`, {
            // 手动传入headers，确保使用最新token
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          setProfileData(profile.data);
          console.log('获取到用户信息:', profile.data);

          // 获取API文档链接
          await fetchDocLinks();
        } catch (profileError: any) {
          console.error('获取用户资料失败:', profileError);
          // 如果是401错误，可能是token问题，尝试清除token并让用户重新登录
          if (profileError.response?.status === 401) {
            console.log('Token无效，需要重新登录');
            console.log('当前请求头:', profileError.config?.headers);
            localStorage.removeItem('token');
          }
          throw profileError;
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        setProfileData(null);
        localStorage.removeItem('token');
      } finally {
        isFetchingUserByToken.current = false;
      }
    };

    fetchUserData();
  }, [address, signer]);

  // 添加获取API文档链接的方法
  const fetchDocLinks = async () => {
    try {
      console.log('开始获取API文档链接');
      const response = await dappApi.get('https://dapp.yc365.io/dapp/api/v1/doc');
      if (response) {
        console.log('获取到API文档链接:', response);
        // 确保返回的数据是数组
        if (Array.isArray(response)) {
          // 格式化链接，确保有http前缀
          const formattedLinks = response.map(item => ({
            ...item,
            link: formatUrl(item.link)
          }));
          setDocLinks(formattedLinks);
        } else {
          console.error('API文档链接数据格式不正确，应为数组:', response);
          // 如果不是数组，尝试转换
          const links = [response].filter(Boolean).map(item => ({
            ...item,
            link: formatUrl(item.link)
          }));
          setDocLinks(links);
        }
      } else {
        console.error('API文档链接返回数据为空');
        // 设置默认链接
        setDocLinks([
          { name: 'contracts', link: 'https://www.google.com' },
          { name: 'arch', link: 'https://www.github.com' },
          { name: 'api', link: 'https://www.github.com' }
        ]);
      }
    } catch (error) {
      console.error('获取API文档链接失败:', error);
      // 设置默认链接
      setDocLinks([
        { name: 'contracts', link: 'https://www.google.com' },
          { name: 'arch', link: 'https://www.github.com' },
          { name: 'api', link: 'https://www.github.com' }
      ]);
    }
  };

  // 辅助函数：确保URL有http前缀
  const formatUrl = (url: string) => {
    if (!url) return '';
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    return `https://${url}`;
  };

  const logout = () => {
    // 设置登出标志
    isLoggingOut.current = true;

    // 清除用户状态
    setAddress(null);
    setSigner(null);
    setProfileData(null);
    setIsWallet("0");
    // 清除合约状态
    setUsdcContract(null);
    setVaultContract(null);
    setBetMessageContract(null);
    setBetTokenContract(null);
    setNetworkName('');
    
    // 重置初始化标志
    isInitialized.current = false;
    
    // 清除本地存储的token
    localStorage.removeItem('token');
    
    console.log('用户已登出');

    // 延迟重置登出标志，确保所有状态都已清除
    setTimeout(() => {
      isLoggingOut.current = false;
    }, 1000);
  };

  const checkWallet = async () => {
    console.log('address',address);
    if(address==null){
      message.error('请先连接钱包');
    }
  }

    // 添加刷新余额的方法
    const refreshBalances = () => {
      setRefreshTrigger(prev => prev + 1);
      console.log("正在刷新所有余额...");
    };

  // 添加手动刷新个人资料的方法
  const refreshProfile = async () => {
    if (!address) {
      console.log('无法刷新资料：没有钱包地址');
      return;
    }
    
    try {
      console.log('手动刷新用户资料, 地址:', address);
      const token = localStorage.getItem('token');
      
      if (!token) {
        console.log('无法刷新资料：没有token');
        return;
      }
      
      const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`, {
        headers: {
          'Authorization': `Bearer ${token}`        }
      });
      
      setProfileData(profile.data);
      console.log('资料刷新成功:', profile.data);
      return profile.data;
    } catch (error) {
      console.error('刷新用户资料失败:', error);
      return null;
    }
  };

  return (
    <UserContext.Provider value={{ 
      profileData, 
      address, 
      provider,
      signer,
      usdcContract,
      vaultContract,
      betMessageContract,
      betTokenContract,
      setProfileData,
      logout,
      checkWallet,
      refreshBalances,   // 暴露刷新方法
      refreshTrigger,    // 暴露刷新计数器
      refreshProfile,    // 暴露资料刷新方法
      networkName,
      docLinks,          // 暴露文档链接
      fetchDocLinks,      // 暴露获取文档链接的方法
      isWallet
    }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => useContext(UserContext);
