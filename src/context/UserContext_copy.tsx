// // src/context/UserContext.tsx
// import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
// import { dappApi } from '../services/request';
// import { DAPP_API } from '../services/apiPaths';
// import { ethers, BrowserProvider } from "ethers";
// import usdcAbi from '../contracts/mocks/MockERC20.sol/MockERC20.json';
// import vaultAbi from '../contracts/Vault.sol/Vault.json';
// import betMessageAbi from '../contracts/BetManager.sol/BetManager.json';
// import betTokenAbi from '../contracts/BetToken.sol/BetToken.json';
// // import {useConnect, useConnectors} from '@particle-network/connectkit'; 
// import { useAccount, useConnect } from 'wagmi';

// // 添加文档链接接口
// interface DocLink {
//   name: string;
//   link: string;
// }

// const UserContext = createContext<any>(null);

// export const UserProvider = ({ children }: { children: React.ReactNode }) => {
//   const [address1, setAddress] = useState<string | null>(null);
//    // 新增刷新计数器
//   const [refreshTrigger, setRefreshTrigger] = useState(0);
//   const [profileData, setProfileData] = useState<any>(null);
//   const [signer, setSigner] = useState<ethers.Signer | null>(null);
//   const isInitialized = useRef(false);
//   const isFetchingUserByToken = useRef(false);
//   const [usdcContract, setUsdcContract] = useState<ethers.Contract | null>(null);
//   const [vaultContract, setVaultContract] = useState<ethers.Contract | null>(null);
//   const [betMessageContract, setBetMessageContract] = useState<ethers.Contract | null>(null);
//   const [betTokenContract, setBetTokenContract] = useState<ethers.Contract | null>(null);
//   const { connect } = useConnect();
//   const connectors = useAccount();
//   const { address } = useAccount();
//   const [networkName, setNetworkName] = useState<string>('');
//   // 添加文档链接状态
//   const [docLinks, setDocLinks] = useState<DocLink[]>([]);

//    // 初始化合约
//    const initializeContracts = async (newSigner: ethers.Signer) => {
//     try {
//       console.log("初始化合约开始...");
//       console.log("合约地址:", {
//         USDC: import.meta.env.VITE_USDC_ADDRESS,
//         VAULT: import.meta.env.VITE_VAULT_ADDRESS,
//         BET_MANAGER: import.meta.env.VITE_BET_MANAGER,
//         BET_TOKEN: import.meta.env.VITE_BET_TOKEN
//       });
      
//       const userAddress = await newSigner.getAddress();
//       console.log("当前用户地址:", userAddress);
      
//       const usdc = new ethers.Contract(
//         import.meta.env.VITE_USDC_ADDRESS,
//         usdcAbi.abi,
//         newSigner
//       );
//       console.log("USDC合约初始化成功:", usdc.target);
      
//       const vault = new ethers.Contract(
//         import.meta.env.VITE_VAULT_ADDRESS,
//         vaultAbi.abi,
//         newSigner
//       );
//       console.log("Vault合约初始化成功:", vault.target);
      
//       const betMessage = new ethers.Contract(
//         import.meta.env.VITE_BET_MANAGER,
//         betMessageAbi.abi,
//         newSigner
//       );
//       console.log("BetMessage合约初始化成功:", betMessage.target);

//       const betTokenMessage = new ethers.Contract(
//         import.meta.env.VITE_BET_TOKEN,
//         betTokenAbi.abi,
//         newSigner
//       );
//       console.log("BetToken合约初始化成功:", betTokenMessage.target);
      
//       // 尝试查询余额
//       try {
//         const usdcBalance = await usdc.balanceOf(userAddress);
//         console.log("USDC余额查询成功:", usdcBalance.toString());
        
//         const vaultBalance = await vault.balanceOf(userAddress);
//         console.log("Vault余额查询成功:", vaultBalance.toString());
//       } catch (balanceError) {
//         console.error("余额查询失败:", balanceError);
//       }
      
//       setUsdcContract(usdc);
//       setVaultContract(vault);
//       setBetMessageContract(betMessage);
//       setBetTokenContract(betTokenMessage);
//       console.log('所有合约初始化完成并设置到状态');
//     } catch (error) {
//       console.error('初始化合约失败:', error);
//     }
//   };
//   // 初始化 signer
//   const initializeSigner = async () => {
//     if (window.ethereum && !signer && !isInitialized.current) {
//       try {
//         isInitialized.current = true;
//         const provider = new BrowserProvider(window.ethereum);
//         await provider.send('eth_requestAccounts', []);
//         const newSigner = await provider.getSigner();
//         setSigner(newSigner);
//         const network = await provider.getNetwork();
//         console.log('network',network.name);
//         setNetworkName(network.name);
//         await initializeContracts(newSigner); // 初始化合约
//         console.log('Signer initialized');
//       } catch (error) {
//         console.error('Failed to initialize signer:', error);
//         isInitialized.current = false;
//       }
//     }
//   };

//   useEffect(() => {
//     if (address) {
//       initializeSigner();
//     }
//   }, [address]);

//   // 监听钱包地址变化
//   useEffect(() => {
//     if (window.ethereum) {
//       // 初始化
//       window.ethereum.request({ method: 'eth_accounts' }).then(async (accounts: string[]) => {
//         if (accounts.length > 0) {
//           setAddress(address);
//           await initializeSigner(); // 初始化 signer
//         }
//       });

//       // 监听事件
//       const handleAccountsChanged = async (accounts: string[]) => {
//         // 清除旧token，确保不会使用之前账号的token
//         localStorage.removeItem('token');
        
//         if (accounts.length > 0) {
//           setAddress(address);
//           await initializeSigner(); // 地址变化时重新初始化 signer
//         } else {
//           setAddress(null);
//           setSigner(null);
//           setProfileData(null);
//           setUsdcContract(null);
//           setVaultContract(null);
//           setBetMessageContract(null);
//           setBetTokenContract(null);
//           setNetworkName('');
//           isInitialized.current = false;
//         }
//       };
//       window.ethereum.on('accountsChanged', handleAccountsChanged);

//       return () => {
//         window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
//       };
//     }
//   }, []);

//   // 钱包地址变了就重新拉取用户信息
//   useEffect(() => {
//     const fetchUserData = async () => {
//       if (!address || !signer) {
//         setProfileData(null);
//         return;
//       }

//       if (isFetchingUserByToken.current) {
//         return;
//       }

//       isFetchingUserByToken.current = true;
//       try {
//         console.log('开始获取用户数据，地址:', address);
        
//         // 先检查localStorage中是否有有效的token
//         const existingToken = localStorage.getItem('token');
        
//         // 如果有token，先尝试用它获取用户信息
//         if (existingToken) {
//           try {
//             console.log('检测到现有token，尝试使用...');
//             const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`, {
//               headers: {
//                 'Authorization': `Bearer ${existingToken}`
//               }
//             });
            
//             // 如果成功获取到用户信息，就不需要重新签名登录了
//             if (profile?.data) {
//               console.log('使用现有token成功获取用户信息');
//               setProfileData(profile.data);
              
//               // 获取API文档链接
//               await fetchDocLinks();
              
//               return;
//             }
//           } catch (tokenError) {
//             console.log('现有token已失效，需要重新登录');
//             localStorage.removeItem('token');
//           }
//         }
        
//         // 如果没有token或token无效，则走完整的认证流程
//         // 获取 nonce
//         const nonceResp = await dappApi.get(`${DAPP_API.NONCE}/${address}`);
//         const nonce = nonceResp?.data?.nonce;
        
//         if (!nonce) {
//           throw new Error("Failed to get nonce");
//         }
        
//         console.log('获取到nonce:', nonce);
        
//         // 签名
//         console.log('开始请求签名...');
//         const signature = await signer.signMessage(nonce);
//         console.log('签名完成:', signature);
        
//         // 登录
//         const loginResp = await dappApi.post(DAPP_API.AUTH_LOGIN, {
//           address,
//           message: nonce,
//           signature,
//         });
        
//         if (!loginResp.data?.access_token) {
//           console.error('登录返回数据中缺少token:', loginResp);
//           throw new Error('Login response missing access_token');
//         }
        
//         console.log('登录成功，获取到token');
//         // 直接在dappRequest中设置token，确保后续请求可以使用
//         const token = loginResp.data.access_token;
//         localStorage.setItem('token', token);
//         console.log('存储token完成, 长度:', token.length);
        
//         // 手动验证token是否正确存储
//         const storedToken = localStorage.getItem('token');
//         if (!storedToken || storedToken !== token) {
//           console.error('Token存储异常，存储的token与原始token不一致');
//           console.log('原始token:', token.substring(0, 15) + '...');
//           console.log('存储token:', storedToken ? storedToken.substring(0, 15) + '...' : '未找到');
//         }
        
//         // 短暂延迟，确保token已被保存
//         await new Promise(resolve => setTimeout(resolve, 100));
        
//         // 获取用户信息
//         try {
//           console.log('准备获取用户资料, 当前token状态:', !!localStorage.getItem('token'));
//           // 不使用dappApi.get直接使用axios，绕过可能的缓存问题
//           const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`, {
//             // 手动传入headers，确保使用最新token
//             headers: {
//               'Authorization': `Bearer ${token}`
//             }
//           });
//           setProfileData(profile.data);
//           console.log('获取到用户信息:', profile.data);
          
//           // 获取API文档链接
//           await fetchDocLinks();
//         } catch (profileError: any) {
//           console.error('获取用户资料失败:', profileError);
//           // 如果是401错误，可能是token问题，尝试清除token并让用户重新登录
//           if (profileError.response?.status === 401) {
//             console.log('Token无效，需要重新登录');
//             console.log('当前请求头:', profileError.config?.headers);
//             localStorage.removeItem('token');
//           }
//           throw profileError;
//         }
//       } catch (error) {
//         console.error('Error fetching user data:', error);
//         setProfileData(null);
//         localStorage.removeItem('token');
//       } finally {
//         isFetchingUserByToken.current = false;
//       }
//     };

//     fetchUserData();
//   }, [address, signer]);

//   // 添加获取API文档链接的方法
//   const fetchDocLinks = async () => {
//     try {
//       console.log('开始获取API文档链接');
//       const response = await dappApi.get('https://dapp.yc365.io/dapp/api/v1/doc');
//       if (response) {
//         console.log('获取到API文档链接:', response);
//         // 确保返回的数据是数组
//         if (Array.isArray(response)) {
//           // 格式化链接，确保有http前缀
//           const formattedLinks = response.map(item => ({
//             ...item,
//             link: formatUrl(item.link)
//           }));
//           setDocLinks(formattedLinks);
//         } else {
//           console.error('API文档链接数据格式不正确，应为数组:', response);
//           // 如果不是数组，尝试转换
//           const links = [response].filter(Boolean).map(item => ({
//             ...item,
//             link: formatUrl(item.link)
//           }));
//           setDocLinks(links);
//         }
//       } else {
//         console.error('API文档链接返回数据为空');
//         // 设置默认链接
//         setDocLinks([
//           { name: 'Google', link: 'https://www.google.com' },
//           { name: 'Github', link: 'https://www.github.com' }
//         ]);
//       }
//     } catch (error) {
//       console.error('获取API文档链接失败:', error);
//       // 设置默认链接
//       setDocLinks([
//         { name: 'Google', link: 'https://www.google.com' },
//         { name: 'Github', link: 'https://www.github.com' }
//       ]);
//     }
//   };

//   // 辅助函数：确保URL有http前缀
//   const formatUrl = (url: string) => {
//     if (!url) return '';
//     if (url.startsWith('http://') || url.startsWith('https://')) {
//       return url;
//     }
//     return `https://${url}`;
//   };

//   // 在组件加载时就获取文档链接，不依赖于用户登录
//   useEffect(() => {
//     fetchDocLinks();
//   }, []);

//   const logout = () => {
//     // 清除用户状态
//     setAddress(null);
//     setSigner(null);
//     setProfileData(null);
    
//     // 清除合约状态
//     setUsdcContract(null);
//     setVaultContract(null);
//     setBetMessageContract(null);
//     setBetTokenContract(null);
//     setNetworkName('');
    
//     // 重置初始化标志
//     isInitialized.current = false;
    
//     // 清除本地存储的token
//     localStorage.removeItem('token');
    
//     console.log('用户已登出');
//   };

//   const checkWallet = async () => {
//     console.log('address',address);
//     if(address==null){
//       const particleConnector = connectors[1];
//       // 提供一个空对象作为参数，使其显示所有连接选项
//       await connect({connector:particleConnector});
//     }
//   }

//     // 添加刷新余额的方法
//     const refreshBalances = () => {
//       setRefreshTrigger(prev => prev + 1);
//       console.log("正在刷新所有余额...");
//     };

//   // 添加手动刷新个人资料的方法
//   const refreshProfile = async () => {
//     if (!address) {
//       console.log('无法刷新资料：没有钱包地址');
//       return;
//     }
    
//     try {
//       console.log('手动刷新用户资料, 地址:', address);
//       const token = localStorage.getItem('token');
      
//       if (!token) {
//         console.log('无法刷新资料：没有token');
//         return;
//       }
      
//       const profile = await dappApi.get(`${DAPP_API.GET_PROFILE}/${address}`, {
//         headers: {
//           'Authorization': `Bearer ${token}`
//         }
//       });
      
//       setProfileData(profile.data);
//       console.log('资料刷新成功:', profile.data);
//       return profile.data;
//     } catch (error) {
//       console.error('刷新用户资料失败:', error);
//       return null;
//     }
//   };

//   return (
//     <UserContext.Provider value={{ 
//       profileData, 
//       address, 
//       signer,
//       usdcContract,
//       vaultContract,
//       betMessageContract,
//       betTokenContract,
//       setProfileData,
//       logout,
//       checkWallet,
//       refreshBalances,   // 暴露刷新方法
//       refreshTrigger,    // 暴露刷新计数器
//       refreshProfile,    // 暴露资料刷新方法
//       networkName,
//       docLinks,          // 暴露文档链接
//       fetchDocLinks      // 暴露获取文档链接的方法
//     }}>
//       {children}
//     </UserContext.Provider>
//   );
// };

// export const useUser = () => useContext(UserContext);