import { Suspense, lazy } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { Spin } from 'antd';
import AppContainer from '../context/AppContainer';

// 布局组件
const Layout = lazy(() => import('../layout/Layout'));

// 懒加载组件
const Home = lazy(() => import('../pages/Home'));
const MarketDetail = lazy(() => import('../pages/MarketDetail'));
const Profile = lazy(() => import('../pages/Profile'));
const Orders = lazy(() => import('../pages/Orders'));
const Setting = lazy(() => import('../pages/Setting'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
// const WalletRedirect = lazy(() => import('../pages/WalletRedirect'));
const Leaderboard = lazy(() => import('../pages/Leaderboard'));
const Activity = lazy(() => import('../pages/Activity'));
const Favorites = lazy(() => import('../pages/Favorites'));
// const NotFound = lazy(() => import('../pages/NotFound'));

// 加载中组件
const LoadingComponent = () => (
  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
    <Spin size="large" />
  </div>
);

// 路由配置
const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AppContainer>
        <Layout />
      </AppContainer>
    ),
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Home />
          </Suspense>
        ),
      },
      {
        path: 'market/:marketId',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <MarketDetail />
          </Suspense>
        ),
      },
      {
        path: 'profile',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Profile />
          </Suspense>
        ),
      },
      {
        path: 'orders',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Orders />
          </Suspense>
        ),
      },
      {
        path: 'setting',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Setting />
          </Suspense>
        ),
      },
      {
        path: 'dashboard',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Dashboard />
          </Suspense>
        ),
      },
      {
        path: 'favorites',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Favorites />
          </Suspense>
        ),
      },
      {
        path: 'leaderboard',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Leaderboard />
          </Suspense>
        ),
      },
      {
        path: 'activity',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Activity />
          </Suspense>
        ),
      },
      {
        path: 'about',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <div>About Page</div>
          </Suspense>
        ),
      },
      {
        path: '*',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <Home />
          </Suspense>
        ),
      },
    ],
  },
  // 添加钱包重定向页面的路由
  // {
  //   path: '/wallet',
  //   element: (
  //     <Suspense fallback={<LoadingComponent />}>
  //       <WalletRedirect />
  //     </Suspense>
  //   ),
  // },
]);

// 路由提供者组件
const AppRouter = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;