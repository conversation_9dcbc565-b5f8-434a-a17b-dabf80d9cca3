import React, { useState, useEffect } from 'react';
import type { KeyboardEvent } from 'react';
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';
import { Layout, Button, Input, Avatar, Space, Dropdown, Tabs, Grid, Typography, Menu } from 'antd';
import { SearchOutlined, MenuOutlined, DownOutlined, UpOutlined, EllipsisOutlined } from '@ant-design/icons';
import { useTheme } from '../theme/ThemeProvider';
import { useMediaQuery } from 'react-responsive';
import userAvatarImage from '../assets/figma_images/user_avatar_image.png';
import yc365Logo from '../assets/figma_images/app_icon.svg';
import yc365LogoB from '../assets/figma_images/app_icon_b.svg';
import GroupAll from '../assets/figma_images/GroupAll.png';
import GroupAllB from '../assets/figma_images/GroupAllBlack.png';
import Wallet from '../assets/figma_images/wallet.png';
import { DropdownMenu } from '../components/shared/MenuComponents';
import './Header.css'; 
import { useNavigate, useLocation } from 'react-router-dom';
import { ethers } from 'ethers';
// import { useModal } from '@particle-network/connectkit';
import { useUser } from '../context/UserContext';
import { DAPP_API } from '../services/apiPaths';
import { eventBus } from '../utils/eventBus';
import { CustomConnectButton } from '../components/CustomConnectButton';

const { Header: AntHeader } = Layout;
const { Title } = Typography;
const { useBreakpoint } = Grid;
const { TabPane } = Tabs;

// 默认分类数据，将被API数据替换
const defaultCategories = [{ id: 0, name: 'All' }];


interface HeaderProps {
  isLoggedIn: boolean;
  handleConnectWallet: () => void;
  handleLogout: () => void;
  showDrawer: () => void;
  showDepositModal?: () => void;
  renderDropdownMenu?: () => React.ReactElement;
}

const AppHeader: React.FC<HeaderProps> = ({
  isLoggedIn,
  handleConnectWallet,
  handleLogout,
  showDrawer,
  showDepositModal,
  renderDropdownMenu
}) => {
  const { t } = useTranslation();
  const { theme, toggleTheme } = useTheme();
  const screens = useBreakpoint();
  const isMobileMediaQuery = useMediaQuery({ maxWidth: 768 });
  const isMobile = !screens.md || isMobileMediaQuery;
  const [menuHover, setMenuHover] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { profileData, usdcContract, vaultContract, signer, address, refreshTrigger } = useUser();
  const [usdcBalance, setUsdcBalance] = useState<string>('0');
  const [vaultBalance, setVaultBalance] = useState<string>('0');
  const [contractsInitialized, setContractsInitialized] = useState<boolean>(false);
  const [categories, setCategories] = useState<{ id: number; name: string }[]>(defaultCategories);
  // 添加搜索状态
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  // const { setOpen } = useModal();
  const isHomePage = location.pathname === '/' || location.pathname === '/home';

  // 获取游戏类型数据
  const fetchGameTypes = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}${DAPP_API.GAME_TYPES}?lang=${i18n.language == "zh" ? "zh-CN" : i18n.language}`);
      const data = await response.json();
      
      if (data && data.code === 200 && Array.isArray(data.data)) {
        // 存储游戏类型数据，包含id和name
        const gameTypes = data.data.map((type: any) => ({
          id: type.id,
          name: type.name || ''
        }));
        
        // 过滤掉空名称
        const filteredTypes = gameTypes.filter((type: {id: number, name: string}) => type.name.trim() !== '');
        
        // 确保始终有"All"分类
        const allCategories = [{ id: 0, name: 'All' }, ...filteredTypes];
        
        setCategories(allCategories);
        console.log('获取到游戏类型:', allCategories);
      } else {
        console.error('获取游戏类型失败:', data);
        // 发生错误时使用默认分类
        setCategories([{ id: 0, name: 'All' }]);
      }
    } catch (error) {
      console.error('获取游戏类型出错:', error);
      // 发生错误时使用默认分类
      setCategories([{ id: 0, name: 'All' }]);
    }
  };

  // 首次加载和语言变化时获取游戏类型
  useEffect(() => {
    fetchGameTypes();
  }, [i18n.language]);

  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = () => {
      fetchGameTypes();
    };

    // 订阅语言变化事件
    eventBus.on('languageChanged', handleLanguageChange);

    // 清理订阅
    return () => {
      eventBus.off('languageChanged', handleLanguageChange);
    };
  }, []);

  // 从URL中获取当前选中的分类
  const getInitialCategory = () => {
    const searchParams = new URLSearchParams(location.search);
    const categoryId = searchParams.get('categoryId');
    const categoryName = searchParams.get('category');
    
    // 优先使用categoryId
    if (categoryId) {
      const foundCategory = categories.find(cat => cat.id === parseInt(categoryId));
      if (foundCategory) {
        return foundCategory;
      }
    }
    
    // 如果没有找到ID，尝试使用名称
    if (categoryName) {
      const foundCategory = categories.find(cat => cat.name === categoryName);
      if (foundCategory) {
        return foundCategory;
      }
    }
    
    // 默认返回"All"分类
    return categories.find(cat => cat.name === 'All') || categories[0];
  };
  
  // 使用回调函数初始化状态，确保只在组件挂载时计算一次
  const [selectedCategory, setSelectedCategory] = useState(() => getInitialCategory());

  // 当URL变化或分类变化时更新选中的分类
  useEffect(() => {
    const category = getInitialCategory();
    if (category && (category.id !== selectedCategory.id || category.name !== selectedCategory.name)) {
      setSelectedCategory(category);
    }
  }, [location.search, categories]);
  
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';
  
  // const changeLanguage = (lang: string) => {
  //   i18n.changeLanguage(lang);
  // };

  const handleNavigateToHome = () => {
    // 导航到首页时保持当前选中的分类
    const currentCategory = selectedCategory || categories[0];
    navigate(`/?categoryId=${currentCategory.id}&category=${currentCategory.name}`);
  };

  const handleCategoryChange = (categoryName: string) => {
    // 根据名称找到对应的分类对象
    const category = categories.find(cat => cat.name === categoryName);
    if (category) {
      // 即使当前类别已被选中，也强制导航以触发刷新
      navigate(`/?categoryId=${category.id}&category=${category.name}`);
    }
  };

  // Create the dropdown menu component if not provided
  const defaultDropdownMenu = () => (
    <DropdownMenu
      theme={theme}
      isLoggedIn={isLoggedIn}
      themeClass={themeClass}
      handleConnectWallet={handleConnectWallet}
      handleLogout={handleLogout}
      toggleTheme={toggleTheme}
    />
  );

  // Use provided renderDropdownMenu or the default one
  const dropdownMenuComponent = renderDropdownMenu || defaultDropdownMenu;

  // 确保Tabs组件中的activeKey使用正确的分类
  const activeCategory = selectedCategory || getInitialCategory();

  // 格式化数字函数
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(2) + 'B';
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(2) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(2) + 'k';
    }
    return num.toFixed(2);
  };

  // Check if contracts are initialized
  useEffect(() => {
    if (usdcContract && vaultContract && signer && address) {
      setContractsInitialized(true);
    } else {
      setContractsInitialized(false);
    }
  }, [usdcContract, vaultContract, signer, address]);

  // 获取余额 - Only fetch when contracts are initialized
  useEffect(() => {
    const fetchBalances = async () => {
      // Skip if contracts aren't initialized yet
      if (!contractsInitialized) {
        console.log("Header.tsx - 等待合约初始化...");
        return;
      }
      
      console.log("Header.tsx - 尝试获取余额", { 
        hasUsdcContract: !!usdcContract, 
        hasVaultContract: !!vaultContract, 
        hasSigner: !!signer,
        hasAddress: !!address
      });
      
      try {
        // 获取 USDC 余额
        console.log("Header.tsx - 调用USDC合约地址:", usdcContract?.target);
        const usdcBalance = await usdcContract?.balanceOf(address);
        console.log("Header.tsx - 原始USDC余额:", usdcBalance?.toString());
        const formattedUsdcBalance = ethers.formatUnits(usdcBalance, 18); // USDC 有 6 位小数
        console.log("Header.tsx - 格式化后USDC余额:", formattedUsdcBalance);
        setUsdcBalance(formatNumber(parseFloat(formattedUsdcBalance)));
        
        // 获取 Vault 余额
        console.log("Header.tsx - 调用Vault合约地址:", vaultContract?.target);
        const vaultBalance = await vaultContract?.balanceOf(address);
        console.log("Header.tsx - 原始Vault余额:", vaultBalance?.toString());
        const formattedVaultBalance = ethers.formatUnits(vaultBalance, 18); // Vault token 有 6 位小数
        console.log("Header.tsx - 格式化后Vault余额:", formattedVaultBalance);
        setVaultBalance(formatNumber(parseFloat(formattedVaultBalance)));
      } catch (error) {
        console.error('Header.tsx - 获取余额失败:', error);
      }
    };

    if (contractsInitialized) {
      fetchBalances();
    }
  }, [contractsInitialized, refreshTrigger]);

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      const searchInput = event.target as HTMLInputElement;
      const searchValue = searchInput.value.trim();
      if (searchValue) {
        // 导航到搜索结果页面
        navigate(`/?search=${encodeURIComponent(searchValue)}`);
      }
    }
  };

  // 处理搜索输入变化
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(e.target.value);
  };

  return (
    <>
      <AntHeader className={`home-header ${themeClass}`}>
        <div className="logo-container" style={{ cursor: 'pointer' }} onClick={handleNavigateToHome}>
          <img src={theme.name === 'dark' ? yc365Logo : yc365LogoB} alt="YC365 Logo" className="logo-img" />
          <Title level={isMobile ? 4 : 3} style={{ color: theme.textColor, margin: 0 }}>YC365</Title>
        </div>
        {!isMobile && (
          <div className="header-search-categories">
            <Input 
              className={`header-search-input ${themeClass}`}
              placeholder={t('home.search')} 
              prefix={<SearchOutlined style={{ color: theme.secondaryTextColor }} />} 
              style={{ 
                backgroundColor: theme.componentBackgroundColor,
                borderColor: theme.borderColor,
              }}
              value={searchKeyword}
              onChange={handleSearchInputChange}
              onKeyDown={handleKeyDown}
            />
          </div>
        )}
        <Space>
        
          

          
          {!isLoggedIn ? (
            <div>
              <div>
                <CustomConnectButton 
                  themeClass={themeClass}
                  primaryColor={theme.name === 'dark' ? '#2C9CDC' : '#0852F0'}
                />
              </div>
            </div>
          ) : (
            <Space size={14} className="user-logged-in-container">
             
              
              {/* Portfolio金额 */}
              {!isMobile && <div className="portfolio-display">
                <div style={{ fontSize: 12, color: theme.secondaryTextColor, marginBottom: -40 }}>{t('app.header.portfolio')}</div>
                <div style={{ fontSize: 16, color: '#25AE60', fontWeight: 'bold' }}>${vaultBalance}</div>
              </div>}
              
              {/* Cash金额 */}
              {!isMobile && <div className="cash-display">
                <div style={{ fontSize: 12, color: theme.secondaryTextColor, marginBottom: -40 }}>{t('app.header.cash')}</div>
                <div style={{ fontSize: 16, color: '#25AE60', fontWeight: 'bold' }}>${usdcBalance}</div>
              </div>}
              
              {/* Deposit按钮 */}
              {!isMobile && showDepositModal && <Button 
                type="primary" 
                className="deposit-button"
                onClick={showDepositModal}
                style={{ 
                  height: '40px', 
                  fontWeight: 'bold', 
                  backgroundColor: theme.primaryColor, 
                  borderColor: theme.primaryColor, 
                  borderRadius: 8,
                  width: '120px',
                  boxShadow: 'none'
                }}
              >
                {t('default.deposit')}
              </Button>}
              
              {/* 用户头像和下拉箭头 */}
              {isMobile ? (
                <div 
                  className="user-avatar-dropdown" 
                  onClick={showDrawer}
                  style={{ 
                    cursor: 'pointer', 
                    display: 'flex', 
                    alignItems: 'center',
                    backgroundColor: theme.componentBackgroundColor,
                    borderRadius: 30,
                    padding: '4px 12px 4px 4px'
                  }}
                >
                  <Avatar src={profileData && profileData.profile_image ? profileData.profile_image : userAvatarImage} size={32} style={{ marginRight: 8 }} />
                  <DownOutlined style={{ fontSize: 12, color: theme.secondaryTextColor }} />
                </div>
              ) : (
                <Dropdown 
                  dropdownRender={dropdownMenuComponent}
                  trigger={['hover']}
                  placement="bottomRight"
                  onOpenChange={visible => setMenuHover(visible)}
                  overlayClassName="header-dropdown-overlay"
                >
                  <div className="user-avatar-dropdown" style={{ 
                    cursor: 'pointer', 
                    display: 'flex', 
                    alignItems: 'center',
                    backgroundColor: theme.componentBackgroundColor,
                    borderRadius: 30,
                    padding: '4px 12px 4px 4px'
                  }}>
                    <Avatar src={profileData && profileData.profile_image ? profileData.profile_image : userAvatarImage} size={32} style={{ marginRight: 8 }} />
                    {menuHover ? <UpOutlined style={{ fontSize: 12, color: theme.secondaryTextColor }} /> : <DownOutlined style={{ fontSize: 12, color: theme.secondaryTextColor }} />}
                  </div>
                </Dropdown>
              )}
            </Space>
          )}
          
          {!isLoggedIn && (
            isMobile ? (
              // 移动设备上使用普通按钮，点击时调用showDrawer
              <Button 
                type="text" 
                icon={<MenuOutlined style={{color: theme.textColor }}/>} 
                onClick={showDrawer}
                className="menu-dropdown-button"
              />
            ) : (
              // 桌面设备上使用Dropdown，支持悬停
              <Dropdown 
                dropdownRender={dropdownMenuComponent}
                trigger={['hover']}
                placement="bottomRight"
                onOpenChange={visible => setMenuHover(visible)}
                overlayClassName="header-dropdown-overlay"
              >
                <Button 
                  type="text" 
                  icon={<MenuOutlined style={{color: theme.textColor }}/>} 
                  className="menu-dropdown-button"
                />
              </Dropdown>
            )
          )}
        </Space>
      </AntHeader>

      {/* 分类标签栏 */}
      <div className={`top-categories-container ${themeClass}`}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Tabs
            className={`categories-tabs ${themeClass}`}
            activeKey={activeCategory.name}
            onChange={handleCategoryChange}
            onTabClick={(key) => {
              // 根据名称找到对应的分类对象
              const category = categories.find(cat => cat.name === key);
              if (category) {
                navigate(`/?categoryId=${category.id}&category=${category.name}`);
              }
            }}
            tabBarGutter={0}
            tabPosition="top"
            animated={true}
            centered={false}
            moreIcon={null}
            tabBarStyle={{
              marginBottom: 0,
              marginLeft: '10px',
              background: 'transparent'
            }}
          >
            {categories.map((cat, index) => (
              <TabPane 
                tab={index === 0 ? (
                  <span>
                    <img src={theme.name === 'dark' ? GroupAll : GroupAllB} alt="All" style={{width:'10px', height:'10px', marginRight: '4px', verticalAlign: 'middle'}} />
                    {cat.name}
                  </span>
                ) : cat.name} 
                key={cat.name}
                style={{ padding: 0 }}
              />
            ))}
          </Tabs>
          
          {/* More dropdown menu */}
          <Dropdown
            overlay={
              <Menu
                items={[
                  {
                    key: 'activity',
                    label: t('activity.activity'),
                    onClick: () => navigate('/activity')
                  },
                  {
                    key: 'leaderboard',
                    label: t('leaderboard.leaderboard'),
                    onClick: () => navigate('/leaderboard')
                  }
                ]}
                style={{
                  backgroundColor: theme.componentBackgroundColor,
                  color: theme.textColor
                }}
              />
            }
            placement="bottomRight"
            trigger={['click','hover']}
          >
            <Button
              type="text"
              icon={<DownOutlined style={{ color: theme.textColor }} />}
              // style={{ marginLeft: '8px' }}
            >
              {t('header.more')}
            </Button>
          </Dropdown>
        </div>
      </div>

      {/* 移动端搜索框 */}
      {isMobile &&  isHomePage &&(
        <div className="mobile-search-categories">
          <Input 
            className={`mobile-search-input ${themeClass}`}
            placeholder={t('home.search')} 
            prefix={<SearchOutlined style={{ color: theme.secondaryTextColor }} />} 
            style={{ 
              backgroundColor: theme.componentBackgroundColor,
              borderColor: theme.borderColor,
            }}
            value={searchKeyword}
            onChange={handleSearchInputChange}
            onKeyDown={handleKeyDown}
          />
        </div>
      )}
    </>
  );
};

export default AppHeader; 