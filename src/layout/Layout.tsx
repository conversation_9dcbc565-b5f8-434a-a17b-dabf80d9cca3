import  { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { Layout as AntLayout, Modal, Drawer, Avatar, Menu} from 'antd';
import { 
  HomeOutlined, 
  UnorderedListOutlined, 
  UserOutlined, 
  CloseOutlined 
} from '@ant-design/icons';
import { useTheme } from '../theme/ThemeProvider';
import { useMediaQuery } from 'react-responsive';
import { useTranslation } from 'react-i18next';
import AppHeader from '../layout/Header';
import { DrawerContent } from '../components/shared/MenuComponents';
import userAvatarImage from '../assets/figma_images/user_avatar_image.png';
import { useAppContext } from '../context/AppContext';
import { ethers } from 'ethers';
import toast , { Toaster }from 'react-hot-toast';
// import usdcAbi from '../contracts/mocks/MockERC20.sol/MockERC20.json';
// import vaultAbi from '../contracts/Vault.sol/Vault.json';
import { useUser } from '../context/UserContext';

const { Content, Footer } = AntLayout;

const Layout = () => {
  const { theme, toggleTheme } = useTheme();
  const { t } = useTranslation();
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const navigate = useNavigate();
  const location = useLocation();
  
  // Use AppContext instead of local state for wallet connection
  const { isLoggedIn, handleConnectWallet, handleLogout } = useAppContext();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [depositModalVisible, setDepositModalVisible] = useState(false);
  const [currentTab, setCurrentTab] = useState("deposit");
  const [amount, setAmount] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  // const [vaultBalance, setVaultBalance] = useState(0);
  // const [usdcBalance, setUsdcBalance] = useState(0);
  // const [signer, setSigner] = useState<ethers.Signer | null>(null);
  // const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  // const [usdcContract, setUsdcContract] = useState<ethers.Contract | null>(null);
  // const [vaultContract, setVaultContract] = useState<ethers.Contract | null>(null);
  const contractAddress = import.meta.env.VITE_VAULT_ADDRESS || 'Not Found';
  // const [walletAddress, setWalletAddress] = useState('');

  const { usdcContract, vaultContract, signer,address,refreshBalances,networkName } = useUser();
  const [usdcBalance, setUsdcBalance] = useState<string>('0');
  const [vaultBalance, setVaultBalance] = useState<string>('0');
  
  // 获取主题类名
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';

  // 初始化 signer
  // const initializeSigner = async () => {
  //   if (window.ethereum) {
  //     try {
  //       // 1. 获取 provider
  //       const provider = new BrowserProvider(window.ethereum);
  //       // setProvider(provider);
        
  //       // 2. 请求用户授权
  //       await provider.send('eth_requestAccounts', []);

  //       // 3. 获取 signer
  //       const signer = await provider.getSigner();
  //       setSigner(signer);

  //       // 4. 获取地址
  //       const address = await signer.getAddress();
  //       console.log('address:', address);
  //       console.log('signer状态:', signer ? '已初始化' : '未初始化');
  //     } catch (error) {
  //       console.error('初始化 signer 失败:', error);
  //       message.error('初始化钱包失败');
  //     }
  //   } else {
  //     message.warning('请安装 MetaMask 或其他以太坊钱包');
  //   }
  // };

   // 获取钱包地址和初始化Web3
  //  useEffect(() => {
  //   const getWalletAddress = async () => {
  //     try {
  //       if (window.ethereum) {
  //         const web3 = new Web3(window.ethereum);
  //         await window.ethereum.request({ method: 'eth_requestAccounts' });
  //         const accounts = await web3.eth.getAccounts();
  //         const address = accounts[0];
  //         setWalletAddress(address);
          
  //         // 获取余额
  //         // const balance = await web3.eth.getBalance(address);
  //         // setBalance(web3.utils.fromWei(balance, 'ether'));
          
  //         // 获取用户数据
  //         // if (address) {
  //         //   await fetchUserData(address);
  //         // }
  //       } else {
  //         message.warning('请安装MetaMask或其他以太坊钱包');
  //       }
  //     } catch (error) {
  //       console.error('获取钱包地址失败:', error);
  //       message.error('获取钱包地址失败');
  //     }
  //   };
  //   getWalletAddress();
  // }, []);

  // 初始化合约
  // useEffect(() => {
  //   const initializeContracts = async () => {
  //     if (signer) {
  //       try {
  //         const usdc = new ethers.Contract(
  //           import.meta.env.VITE_USDC_ADDRESS,
  //           usdcAbi.abi,
  //           signer
  //         );
  //         const vault = new ethers.Contract(
  //           import.meta.env.VITE_VAULT_ADDRESS,
  //           vaultAbi.abi,
  //           signer
  //         );
          
  //         setUsdcContract(usdc);
  //         setVaultContract(vault);
  //       } catch (error) {
  //         console.error('初始化合约失败:', error);
  //       }
  //     }
  //   };

  //   initializeContracts();
  // }, [signer]);

  // 监听钱包变化
  // useEffect(() => {
  //   if (window.ethereum) {
  //     const handleAccountsChanged = async (accounts: string[]) => {
  //       if (accounts.length === 0) {
  //         setSigner(null);
  //         message.info('钱包已断开连接');
  //       } else {
  //         await initializeSigner();
  //       }
  //     };

  //     const handleChainChanged = () => {
  //       window.location.reload();
  //     };

  //     window.ethereum.on('accountsChanged', handleAccountsChanged);
  //     window.ethereum.on('chainChanged', handleChainChanged);

  //     // 初始化
  //     initializeSigner();

  //     return () => {
  //       window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
  //       window.ethereum.removeListener('chainChanged', handleChainChanged);
  //     };
  //   }
  // }, []);

  // 获取合约余额
  useEffect(() => {
    const getVaultBalance = async () => {
      console.log("Layout.tsx - 尝试获取余额", { 
        hasVaultContract: !!vaultContract, 
        hasUsdcContract: !!usdcContract,
        hasSigner: !!signer,
        hasAddress: !!address,
        depositModalVisible 
      });
      
      // Skip if contracts aren't initialized yet
      if (!vaultContract || !usdcContract || !signer || !address) {
        console.log("Layout.tsx - 等待合约初始化...");
        return;
      }
      
      try {
        // 获取 Vault 余额
        console.log("Layout.tsx - 调用Vault合约地址:", vaultContract.target);
        const balance = await vaultContract.balanceOf(address);
        console.log("Layout.tsx - 原始Vault余额:", balance.toString());
        const formattedBalance = ethers.formatUnits(balance, 18); // 使用ethers.formatUnits更准确
        console.log("Layout.tsx - 格式化后Vault余额:", formattedBalance);
        setVaultBalance(formattedBalance);
        
        // 获取 USDC 余额
        console.log("Layout.tsx - 调用USDC合约地址:", usdcContract.target);
        const Ubalance = await usdcContract.balanceOf(address);
        console.log("Layout.tsx - 原始USDC余额:", Ubalance.toString());
        const formattedUBalance = ethers.formatUnits(Ubalance, 18); // 使用ethers.formatUnits更准确
        console.log("Layout.tsx - 格式化后USDC余额:", formattedUBalance);
        setUsdcBalance(formattedUBalance);
      } catch (error) {
        console.error('Layout.tsx - 获取金库余额失败:', error);
      }
    };

    getVaultBalance();
  }, [vaultContract, usdcContract, signer, address, depositModalVisible, refreshBalances]);

  // 获取当前路径，用于设置底部导航的选中状态
  const getCurrentPath = () => {
    const path = location.pathname;
    if (path === '/') return 'home';
    if (path === '/orders') return 'orders';
    if (path.includes('/profile')) return 'person';
    return 'home';
  };

  // Drawer functions - Header wallet functions now come from AppContext

  const showDrawer = () => {
    setDrawerVisible(true);
  };

  const onCloseDrawer = () => {
    setDrawerVisible(false);
  };

  // Deposit/Withdraw Modal相关函数
  const showDepositModal = () => {
    setDepositModalVisible(true);
    setCurrentTab("deposit");
  };

  const handleDepositModalClose = () => {
    setDepositModalVisible(false);
  };
  
  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };
  
  const handleAmountChange = (value: string) => {
    setAmount(value);
  };
  
  const handleSetMaxAmount = () => {
    const valueMax = currentTab === 'deposit' ? Math.floor(Number(usdcBalance)) : vaultBalance;
    setAmount(valueMax?.toString() || '0');
  };

    // 格式化数字函数
    const formatNumber = (num: number): string => {
      if (num >= 1000000000) {
        return (num / 1000000000).toFixed(2) + 'B';
      }
      if (num >= 1000000) {
        return (num / 1000000).toFixed(2) + 'M';
      }
      if (num >= 1000) {
        return (num / 1000).toFixed(2) + 'k';
      }
      return num.toFixed(2);
    };

  // 处理存款
  const handleDeposit = async () => {
    if (!signer || !usdcContract || !vaultContract) {
      console.error('Web3 not ready');
      return;
    }

    // const gasConfig = {
    //   maxFeePerGas: ethers.parseUnits('150', 'gwei'),
    //   maxPriorityFeePerGas: ethers.parseUnits('100', 'gwei'),
    //   gasLimit: 3000000,
    // };

    if (!amount || parseFloat(amount.toString()) <= 0) {
      toast.error(t('default.PleaseEnterTheValidAmount'));
      return;
    }

    try {
      const amountWei = ethers.parseUnits(amount.toString(), 18);
      // 先授权 Vault 合约
      const approveTx = await usdcContract.approve(vaultContract.target, amountWei);
      await approveTx.wait();

      // 调用 Vault 合约的存款方法
      const depositTx = await vaultContract.deposit(amountWei);
      await depositTx.wait();

      setAmount('');
      toast.success(t('default.AmountDepositSuccessfully'));
      handleDepositModalClose();
    } catch (err: any) {
      console.error('❌ Error:', err.message);
      toast.error(err.message);
    }
  };

  // 处理提现
  const handleWithdrawal = async () => {
    if (!signer || !usdcContract || !vaultContract) {
      console.error('Web3 not ready');
      return;
    }

    // const gasConfig = {
    //   maxFeePerGas: ethers.parseUnits('150', 'gwei'),
    //   maxPriorityFeePerGas: ethers.parseUnits('100', 'gwei'),
    //   gasLimit: 3000000,
    // };

    if (!amount || parseFloat(amount.toString()) <= 0) {
      toast.error(t('default.PleaseEnterTheValidAmount'));
      return;
    }

    try {
      const amountWei = ethers.parseUnits(amount.toString(), 18);
      const withdrawTx = await vaultContract.withdraw(amountWei);
      await withdrawTx.wait();
      
      setAmount('');
      toast.success(t('default.amountSuccessfull'));
      handleDepositModalClose();
    } catch (err: any) {
      console.error('❌ Error:', err.message);
      toast.error(t('default.handleSubmitTransactionError'), err.message);
    }
  };

  // 处理交易提交
  const handleSubmitTransaction = async () => {
    if (isLoading) return;

    if (Number(amount)) {
      setIsLoading(true);
      try {
        if (currentTab === 'deposit') {
          await handleDeposit();
          refreshBalances(); // 添加这行
        } else if (currentTab === 'withdraw') {
          await handleWithdrawal();
          refreshBalances(); // 添加这行
        }
      } catch (err) {
        console.error(t('default.handleSubmitTransactionError'), err);
      } finally {
        setIsLoading(false);
      }
    } else {
      toast.error(t('default.enterVaildAmount'));
    }
  };

  const renderMobileFooter = () => (
    <Footer 
      className="mobile-footer" 
      style={{ 
        backgroundColor: theme.name === 'dark' ? '#1D2B39' : '#FFFFFF', 
        borderTop: `1px solid ${theme.name === 'dark' ? '#435363' : '#E8E8E8'}`,
        position: 'fixed',
        bottom: 0,
        left: 0,
        width: '100%',
        zIndex: 10,
        padding: '0',
        boxShadow: '0 -2px 8px rgba(0,0,0,0.15)'
      }}
    >
      <Menu 
        mode="horizontal" 
        defaultSelectedKeys={[getCurrentPath()]} 
        selectedKeys={[getCurrentPath()]}
        className={`mobile-tab-bar ${themeClass}`} 
        theme={theme.name as any}
      >
        <Menu.Item key="home" icon={<HomeOutlined style={{ fontSize: '20px' }} />} onClick={() => navigate('/')}>
          <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.home')}</span>
        </Menu.Item>
        <Menu.Item key="orders" icon={<UnorderedListOutlined style={{ fontSize: '20px' }} />} onClick={() => navigate('/orders')}>
          <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.orders')}</span>
        </Menu.Item>
        {isLoggedIn ? (
          <Menu.Item key="person" icon={<Avatar src={userAvatarImage} size={20} />} onClick={() => navigate('/profile')}>
            <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.person')}</span>
          </Menu.Item>
        ) : (
          <Menu.Item key="person" icon={<UserOutlined style={{ fontSize: '20px' }} />} onClick={() => navigate('/profile')}>
            <span style={{ display: 'block', fontSize: '12px', marginTop: '4px', marginLeft: '-8px' }}>{t('footer.person')}</span>
          </Menu.Item>
        )}
      </Menu>
    </Footer>
  );

  return (
    <AntLayout style={{
      backgroundColor: theme.name === 'dark' ? '#1D2B39' : '#fff',
      width: '100%',
      minHeight: '100vh' // 确保布局至少占满整个视口高度
    }}>
       <Toaster 
        position="bottom-center"  // 可选: top-left, top-center, top-right, bottom-left, bottom-center, bottom-right
        toastOptions={{
          style: {
            marginTop: '60px',  // 为顶部的 Header 留出空间
            zIndex: 2000,       // 确保 z-index 高于 Header
          },
        }}
      />


     {/* 头部固定在顶部 */}
      <div style={{
          width: '100%',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          backgroundColor: theme.name === 'dark' ? '#1D2B39' : '#fff',
          // boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
        }}>
        <div style={{
            width: '100%',
            maxWidth: isMobile ? '100%' : '1200px', // Fixed width according to Figma design
            margin: '0 auto',
            padding: isMobile ? '0' : '0 24px',
          }}>

          {/* 共享 Header */}
          <AppHeader
            isLoggedIn={isLoggedIn}
            handleConnectWallet={handleConnectWallet}
            handleLogout={handleLogout}
            showDrawer={showDrawer}
            showDepositModal={showDepositModal}
          />
        </div>
      </div>
      
      {/* 为固定头部添加占位空间 */}
      <div style={{ height:isMobile ? '170px' : '140px' }}></div>
      
      <Content style={{
        display: 'flex',
        justifyContent: 'center',
        width: '100%',
        backgroundColor: theme.name === 'dark' ? '#1D2B39' : '#fff',
        flex: '1 0 auto', // 让内容区域自动扩展填充剩余空间
        marginTop: '-40px', // 负边距，让内容向上移动一些
      }}>
        <div style={{
          width: '100%',
          maxWidth: isMobile ? '100%' : '1200px', // Fixed width according to Figma design
          margin: '0 auto',
          padding: isMobile ? '0' : '0 24px',
          paddingBottom: isMobile ? '70px' : '0', // 为移动设备增加底部padding，确保内容不被底部导航栏遮挡
        }}>
          {/* 渲染子路由内容 */}
          <Outlet />
        </div>
      </Content>

      {/* 侧边抽屉菜单 */}
      <Drawer
        title={isLoggedIn ? <Avatar src={userAvatarImage} /> : t('drawer.menuTitle')}
        placement="left"
        onClose={onCloseDrawer}
        open={drawerVisible}
        closable={true}
        closeIcon={<CloseOutlined style={{color: theme.textColor}}/>}
        bodyStyle={{ 
          padding: 0, 
          backgroundColor: theme.backgroundColor,
          height: '100%'
        }}
        headerStyle={{ 
          backgroundColor: theme.backgroundColor, 
          borderBottom: `1px solid ${theme.borderColor}`
        }}
        className={`dark-drawer ${themeClass}`}
        style={{ background: theme.backgroundColor }}
      >
        <DrawerContent
          theme={theme}
          isLoggedIn={isLoggedIn}
          themeClass={themeClass}
          handleConnectWallet={handleConnectWallet}
          handleLogout={handleLogout}
          toggleTheme={toggleTheme}
          onClose={onCloseDrawer}
        />
      </Drawer>
      
      {/* 只在移动设备上显示底部导航 */}
      {isMobile && renderMobileFooter()}
      
      {/* Deposit/Withdraw Modal */}
      <Modal
        open={depositModalVisible}
        onCancel={handleDepositModalClose}
        footer={null}
        width={650}
        closable={false}
        destroyOnClose
        centered
        className={`deposit-withdraw-modal ${themeClass}`}
        maskClosable={true}
      >
        <div className={`deposit-withdraw-container ${themeClass}`}>
          <div style={{width: '100%', height: '44px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}> 
            <CloseOutlined 
              className="ant-modal-close" 
              onClick={handleDepositModalClose}
              style={{ 
                color: theme.name === 'dark' ? '#fff' : '#777E8C' 
              }}
            /> 
          </div>

          {/* Tab切换按钮 */}
          <div className="modal-tabs">
            <div 
              className={`tab-button ${currentTab === 'deposit' ? 'active' : ''}`} 
              onClick={() => handleTabChange('deposit')}
              style={{
                backgroundColor: currentTab === 'deposit' 
                  ? (theme.name === 'dark' ? '#243546' : '#1677FF') 
                  : (theme.name === 'dark' ? '#1A2531' : '#F5F5F5'),
                color: currentTab === 'deposit'
                  ? '#FFFFFF'
                  : (theme.name === 'dark' ? '#97A0A4' : '#777E8C')
              }}
            >
              {t('default.deposit')}
            </div>
            <div 
              className={`tab-buttonleft ${currentTab === 'withdraw' ? 'active' : ''}`}
              onClick={() => handleTabChange('withdraw')}
              style={{
                backgroundColor: currentTab === 'withdraw' 
                  ? (theme.name === 'dark' ? '#243546' : '#1677FF') 
                  : (theme.name === 'dark' ? '#1A2531' : '#F5F5F5'),
                color: currentTab === 'withdraw'
                  ? '#FFFFFF'
                  : (theme.name === 'dark' ? '#97A0A4' : '#777E8C')
              }}
            >
              {t('default.withdraw')}
            </div>
          </div>
          
          {/* 表单内容 */}
          <div className="modal-form">
            {/* Network & Currency */}
            <div className="form-field">
              <div className="field-label" style={{ 
                color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
                width: '50%',  // 固定宽度为50%
                textAlign: 'left'  // 文字左对齐
              }}>
                {t('default.network')}
              </div>
              <div className="field-value" style={{ 
                color: theme.textColor,
                width: '50%',  // 固定宽度为50%
                textAlign: 'right'  // 文字右对齐
              }}>
                {networkName}/USDT
              </div>
            </div>
            
            {/* Amount */}
            <div className="form-field">
            <div className="field-label" style={{ 
                color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
                width: '50%',  // 固定宽度为50%
                textAlign: 'left',  // 文字左对齐
              }}>
                {currentTab === 'deposit' ? t('default.depositAmount') : t('default.withdrawAmount')}
              </div>
              <div className="field-value available-balance" style={{ color: '#1677FF', width: '100%',textAlign: 'right'}}>
                {currentTab === 'deposit' ?formatNumber( Number(usdcBalance)) : formatNumber(Number(vaultBalance))} USDT
              </div>
            </div>
            
            {/* Amount Input */}
            <div className="amount-input-container">
              <input
                type="text"
                placeholder={t('default.enterAmount')}
                value={amount}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="amount-input"
                style={{
                  backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                  color: theme.textColor,
                  border: 'none'
                }}
              />
              <button
                className="max-button"
                onClick={handleSetMaxAmount}
                style={{
                  backgroundColor: theme.name === 'dark' ? '#3B4754' : '#FFFFFF',
                  color: theme.name === 'dark' ? '#FFFFFF' : '#000000'
                }}
              >
                {t('default.max')}
              </button>
            </div>

            <div className="form-field">
              <div className="field-label"style={{ 
                color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
                width: '50%',  // 固定宽度为50%
                textAlign: 'left',  // 文字左对齐
              }}>
                {currentTab === 'deposit' ? t('default.paymentAddress') : t('default.receivingAddress')}
              </div>
            </div>
            <input
              type="text"
              value={address}
              readOnly
              className="amount-input"
              style={{
                backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                color: theme.textColor,
                border: 'none'
              }}
            />
            
            {/* Address Fields */}
            <div className="form-field">
              <div className="field-label" style={{ 
                color: theme.name === 'dark' ? '#97A0A4' : '#777E8C',
                width: '50%',  // 固定宽度为50%
                textAlign: 'left',  // 文字左对齐
              }}>
                {t('default.contractAddress')}
              </div>
            </div>
            <input
              type="text"
              value={contractAddress}
              readOnly
              className="amount-input"
              style={{
                backgroundColor: theme.name === 'dark' ? '#243546' : '#F5F5F5',
                color: theme.textColor,
                border: 'none'
              }}
            />

            
            
            {/* Submit Button */}
            <button
              className="submit-button"
              onClick={handleSubmitTransaction}
              disabled={isLoading}
              style={{
                backgroundColor: '#1677FF',
                marginTop: '50px',
              }}
            >
              {isLoading ? t('default.processing') : t('default.submitTrans')}
            </button>
          </div>
        </div>
      </Modal>
    </AntLayout>
  );
};

export default Layout;
