.home-layout {
  min-height: 100vh;
}

.home-layout.light-theme {
  background-color: #ffffff;
}

.home-layout.dark-theme {
  background-color: #1D2B39;
}

.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  z-index: 100;
}

.home-header.light-theme {
  background-color: #ffffff;
  border-bottom: 0px solid #E7E7E7;
}

.home-header.dark-theme {
  background-color: #1D2B39;
  border-bottom: 0px solid #435363;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 32px;
  margin-right: 8px;
}

.header-search-categories {
  display: flex;
  align-items: center;
  flex-grow: 1;
  margin: 0 24px;
}

.header-search-input {
  max-width: 300px;
  margin-right: 24px;
}

.header-search-input.light-theme {
  background-color: #ECECEC;
  border-color: #E7E7E7;
}

.header-search-input.dark-theme {
  background-color: #2C3F50;
  border-color: #375066;
}

/* 顶部分类菜单 */
.top-categories-container {
  overflow-x: auto;
  white-space: nowrap;
  padding: 0;
  margin-bottom: 16px;
  background-color: transparent;
}

.top-categories-container.light-theme {
  border-bottom: 1px solid #E7E7E7;
}

.top-categories-container.dark-theme {
  border-bottom: 1px solid #435363;
}

.top-categories-container::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

.categories-menu-top.ant-menu-horizontal {
  line-height: 46px;
  border-bottom: none;
  display: inline-flex;
  min-width: max-content;
  width: 100%;
  background-color: transparent !important;
}

.categories-menu-top .ant-menu-item {
  padding: 0 20px;
  font-weight: 500;
  font-size: 14px;
  background-color: transparent !important;
}

/* Light theme menu items */
.categories-menu-top.light-theme .ant-menu-item {
  color: #777E8C !important;
}

.categories-menu-top.light-theme .ant-menu-item-selected {
  color: #000000 !important;
  background-color: transparent !important;
}

.categories-menu-top.light-theme .ant-menu-item:hover {
  color: #000000 !important;
  background-color: transparent !important;
}

/* Dark theme menu items */
.categories-menu-top.dark-theme .ant-menu-item {
  color: #97A0A4 !important;
}

.categories-menu-top.dark-theme .ant-menu-item-selected {
  color: #FFFFFF !important;
  background-color: transparent !important;
}

.categories-menu-top.dark-theme .ant-menu-item:hover {
  color: #FFFFFF !important;
  background-color: transparent !important;
}

/* 确保暗色模式下菜单背景透明 */
.categories-menu-top.ant-menu-dark {
  background-color: transparent !important;
}

/* 确保亮色模式下菜单背景透明 */
.categories-menu-top.ant-menu-light {
  background-color: transparent !important;
}

.connect-wallet-btn {
  border-radius: 8px;
  font-weight: 700;
  font-size: 12px;
  /* min-width: 80px !important; */
  min-height: 40px !important;
  box-shadow: none !important;
  margin-left: 10px;
  margin-right: 10px;
}

.connect-wallet-btn.light-theme {
  background-color: #0852F0;
  color: #FFFFFF;
  border-color: #0852F0;
}

.connect-wallet-btn.light-theme:hover {
  background-color: rgba(8, 82, 240, 0.8);
  border-color: #0852F0;
}

.connect-wallet-btn.dark-theme {
  background-color: #2C9CDC;
  color: #FFFFFF;
  border-color: #2C9CDC;
}

.connect-wallet-btn.dark-theme:hover {
  background-color: #2C9CDC;
  border-color: #2C9CDC;
}

.connect-wallet-btn:active {
  filter: brightness(90%);
}

.mobile-search-categories {
  margin-left: 16px;
  margin-right: 16px;
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mobile-search-input.light-theme .ant-input-prefix {
  color: #777E8C;
}

.mobile-search-input.dark-theme .ant-input-prefix {
  color: #97A0A4;
}

/* Carousel / Banner Styles */
.carousel-container {
  position: relative;
  margin: 24px 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.welcome-carousel {
  height: 160px;
}

.carousel-item {
  height: 100%;
  display: flex;
  color: #fff;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s;
}

.carousel-item:hover {
  transform: scale(1.02);
}

.carousel-image-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Section title */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px 0 8px;
  padding: 0 24px;
}

.section-title.light-theme .ant-typography {
  color: #000000;
  margin-bottom: 0;
  font-weight: 600;
}

.section-title.dark-theme .ant-typography {
  color: #FFFFFF;
  margin-bottom: 0;
  font-weight: 600;
}

.section-title .ant-btn-link {
  padding: 0;
  font-weight: 500;
}

.section-title.light-theme .ant-btn-link {
  color: #0852F0;
}

.section-title.dark-theme .ant-btn-link {
  color: #2C9CDC;
}

.market-card.ant-card {
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  margin-bottom: 16px;
  border: 1px solid transparent;
  position: relative;
}

.market-card.ant-card.light-theme {
  background-color: #FFFFFF !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.market-card.ant-card.dark-theme {
  background-color: #375066 !important;
  box-shadow: none;
}

.market-card.ant-card.light-theme:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.market-card.ant-card.dark-theme:hover {
  transform: none;
  box-shadow: none;
  background-color: #97A0A4 !important;
}

.market-card.ant-card.light-theme:hover::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 0px solid #0852F0;
  pointer-events: none;
  z-index: 1;
  border-radius: 10px;
}

.market-card.ant-card.dark-theme:hover::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 0px solid #2C9CDC;
  pointer-events: none;
  z-index: 1;
}

.market-card .ant-card-cover img {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  height: 128px;
  object-fit: cover;
}

.market-card .ant-card-body {
  padding: 12px;
}

.market-card.ant-card.light-theme .ant-card-body {
  background-color: #FFFFFF !important;
}

.market-card.ant-card.dark-theme .ant-card-body {
  background-color: #2c3f50 !important;
}

.market-card.ant-card.light-theme:hover .ant-card-body {
  background-color: #FFFFFF !important;
}

.market-card.ant-card.dark-theme:hover .ant-card-body {
  background-color: #294f63 !important;
}

.card-header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

/* 确保标题和内容在悬停时保持可见 */
.market-card.ant-card.light-theme:hover .ant-typography {
  color: #000000 !important;
}

.market-card.ant-card.dark-theme:hover .ant-typography {
  color: #FFFFFF !important;
}

/* 保持价格文本在悬停时的可见性 */
.market-card.ant-card.light-theme:hover .price-text {
  color: #777E8C !important;
}

.market-card.ant-card.dark-theme:hover .price-text {
  color: #FFFFFF !important;
}

/* 保持价格文本在悬停时的可见性 */
.market-card.ant-card:hover .price-text {
  color: #FFFFFF !important;
}

.mobile-footer {
  height: 50px;
  line-height: 50px;
  padding: 0 !important;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.15);
}

.mobile-tab-bar {
  width: 100%;
  display: flex !important;
  justify-content: space-around;
  background-color: transparent !important;
  border-bottom: none !important;
}

.mobile-tab-bar.light-theme {
  background-color: transparent !important;
}

.mobile-tab-bar.dark-theme {
  background-color: transparent !important;
}

.mobile-tab-bar.ant-menu-dark .ant-menu-item .anticon {
  color: #858D92 !important; /* 未选中图标颜色 */
}

.mobile-tab-bar.ant-menu-dark .ant-menu-item-selected .anticon {
  color: #FFFFFF !important; /* 选中图标颜色 */
}

/* 亮色主题移动端底部导航颜色 */
.mobile-tab-bar.ant-menu-light {
  background-color: transparent !important;
}

.mobile-tab-bar.ant-menu-light .ant-menu-item {
  color: #858D92 !important;
}

.mobile-tab-bar.ant-menu-light .ant-menu-item-selected {
  color: #1D2B39 !important;
  background-color: transparent !important;
}

/* Drawer Styles */
.dark-drawer.light-theme .ant-drawer-header {
  background-color: #FFFFFF !important;
  border-bottom: 1px solid #E7E7E7 !important;
}

.dark-drawer.dark-theme .ant-drawer-header {
  background-color: #1D2B39 !important;
  border-bottom: 1px solid #435363 !important;
}

.dark-drawer.light-theme .ant-drawer-header-title .ant-drawer-close .anticon {
  color: #000000 !important;
}

.dark-drawer.dark-theme .ant-drawer-header-title .ant-drawer-close .anticon {
  color: #FFFFFF !important;
}

.dark-drawer.light-theme .ant-drawer-body {
  background-color: #FFFFFF !important;
  color: #000000 !important;
}

.dark-drawer.dark-theme .ant-drawer-body {
  background-color: #1D2B39 !important;
  color: #FFFFFF !important;
}

.drawer-list-item.ant-list-item {
  padding: 8px 0;
  border-bottom: none !important;
}

.drawer-list-item { justify-content: flex-start !important; }

.drawer-list-item .ant-btn-text {
  padding-left: 0;
  padding-right: 0;
}

/* Ensure theme-based text colors are applied if not directly on Button */
.dark-drawer .ant-list-item .ant-btn-text span {
    color: #FFFFFF;
}

.dark-drawer .ant-list-header {
    color: #435363 !important;
    padding-bottom: 8px;
    border-bottom: none;
}

/* 关闭状态 */
.ant-switch:not(.ant-switch-checked) {
  background-color: #d9d9d9 !important;
}

/* 开启状态 */
.ant-switch-checked {
  background-color: #1890ff !important;
}

/* Specific text colors for drawer items based on Figma */
.drawer-list-item .ant-btn-text span {
    font-size: 18px; /* style_ONECYI */
    font-weight: 600; /* style_ONECYI */
}

.ant-list-header > .ant-typography {
    font-size: 14.47px; /* style_UOP34S */
    font-weight: 600; /* style_UOP34S */
}

/* Disconnect button in drawer */
.dark-drawer .ant-btn-dangerous {
    border-color: #415366; /* stroke_21GA7M */
    color: #97A0A4; /* fill_BYZCHK for text 'disconnect_' */
    background-color: transparent;
}

.dark-drawer .ant-btn-dangerous:hover {
    border-color: #E74801;
    color: #E74801;
}

/* Ensure the logo in the card header is small */
.card-header-info .ant-avatar {
    width: 24px;
    height: 24px;
    line-height: 24px;
}

/* Ensure the logo in the card header is small */
.card-header-info .ant-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Search input placeholder color */
.header-search-input .ant-input::placeholder,
.mobile-search-input .ant-input::placeholder {
    color: #97A0A4; /* fill_TJ5GMY - Search text color from Figma */
}

.dark-drawer .ant-avatar {
    border: 1px solid #478FB8; /* fill_S9C0P8 for logo border in avatar */
}

/* 语言切换按钮样式 */
.language-switch-container {
  margin-right: 10px;
  
}

.language-switch {
  min-width: 62px;
  
}

.language-switch .ant-switch-inner {
  font-weight: 500;
  font-size: 12px;
}

.ant-switch {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.ant-switch-checked {
  background-color: #2C9CDC !important;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .carousel-container {
    margin: 16px;
  }
  
  .welcome-carousel,
  .carousel-item,
  .carousel-image-container,
  .carousel-image,
  .slick-list,
  .slick-track,
  .slick-slide,
  .slick-slide > div {
    height: 80px !important;
    min-height: 80px !important;
    max-height: 80px !important;
  }
  
  .carousel-image-container img {
    height: 80px !important;
    object-fit: cover !important;
  }
  
  .section-title {
    padding: 0 16px;
  }
  
  .market-card.ant-card {
    margin-bottom: 8px;
  }
  
  .top-categories-container {
    margin: 0;
  }
}

/* 顶部分类Tabs */
.categories-tabs {
  /* width: 100%; */
  border-bottom: none;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none;
}

.categories-tabs.light-theme {
  background-color: #FFFFFF;
}

.categories-tabs.dark-theme {
  background-color: #1D2B39;
}

.categories-tabs .ant-tabs-nav {
  margin-bottom: 0 !important;
  padding-left: 12px;
  padding-right: 12px;
  width: 100%;
  background: transparent;
}

.categories-tabs .ant-tabs-nav::-webkit-scrollbar {
  display: none;
}

.categories-tabs .ant-tabs-tab {
  padding: 8px 16px;
  font-size: 14px;
  margin: 0;
  border: none !important;
  background: transparent !important;
  transition: color 0.3s;
}

.categories-tabs.light-theme .ant-tabs-tab {
  color: #777E8C;
}

.categories-tabs.dark-theme .ant-tabs-tab {
  color: #97A0A4;
}

.categories-tabs .ant-tabs-tab-active {
  font-weight: 600;
}

.categories-tabs.light-theme .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #000000 !important;
  font-weight: 600;
}

.categories-tabs.dark-theme .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #FFFFFF !important;
  font-weight: 600;
}

.categories-tabs.ant-tabs-card.ant-tabs-top > .ant-tabs-nav {
  border-bottom: 0;
}

.categories-tabs .ant-tabs-ink-bar {
  height: 3px !important;
  background-color: #2C9CDC00 !important;
}

.categories-tabs .ant-tabs-nav-operations {
  display: none !important;
}

.top-categories-container .ant-tabs-top>.ant-tabs-nav::before {
  border-bottom: none !important;
}

.categories-tabs .ant-tabs-content-holder {
  display: none;
}

.categories-tabs .ant-tabs-nav-list {
  transform: none !important;
}

@media (max-width: 768px) {
  .categories-tabs .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* Yes/No Button Styles */
.yes-btn {
  height: 22px !important;
  font-size: 12px !important;
  padding: 0 8px !important;
  border-radius: 4px !important;
}

.no-btn {
  height: 22px !important;
  font-size: 12px !important;
  padding: 0 8px !important;
  border-radius: 4px !important;
}

/* Light theme Yes/No buttons */
.light-theme .yes-btn {
  border-color: #25AE60 !important;
  color: #25AE60 !important;
  background-color: transparent !important;
}

.light-theme .yes-btn:hover {
  background-color: #25AE60 !important;
  border-color: #25AE60 !important;
  color: white !important;
}

.light-theme .no-btn {
  border-color: #E74801 !important;
  color: #E74801 !important;
  background-color: transparent !important;
}

.light-theme .no-btn:hover {
  background-color: #E74801 !important;
  border-color: #E74801 !important;
  color: white !important;
}

/* Light theme Yes/No active state */
.light-theme .yes-btn.active {
  background-color: #25AE60 !important;
  border-color: #25AE60 !important;
  color: white !important;
}

.light-theme .no-btn.active {
  background-color: #E74801 !important;
  border-color: #E74801 !important;
  color: white !important;
}

/* Dark theme Yes/No buttons - preserving original styles */
.dark-theme .yes-btn {
  border-color: #25AE60 !important;
  color: #25AE60 !important;
  background-color: transparent !important;
}

.dark-theme .yes-btn:hover {
  background-color: #25AE60 !important;
  border-color: #25AE60 !important;
  color: white !important;
}

.dark-theme .no-btn {
  border-color: #E74801 !important;
  color: #E74801 !important;
  background-color: transparent !important;
}

.dark-theme .no-btn:hover {
  background-color: #E74801 !important;
  border-color: #E74801 !important;
  color: white !important;
}

/* Dark theme Yes/No active state */
.dark-theme .yes-btn.active {
  background-color: #25AE60 !important;
  border-color: #25AE60 !important;
  color: white !important;
}

.dark-theme .no-btn.active {
  background-color: #E74801 !important;
  border-color: #E74801 !important;
  color: white !important;
}

.mobile-tab-bar .ant-menu-item {
  margin: 0 !important;
  padding: 0 !important;
  flex: 1;
  text-align: center;
  height: 50px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  line-height: 1.2 !important;
}

.mobile-tab-bar .ant-menu-item .anticon {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}

.mobile-tab-bar .ant-menu-item span {
  display: block;
  color: inherit;
}

/* 移除选中项的背景色 */
.mobile-tab-bar .ant-menu-item-selected {
  background-color: transparent !important;
}

.dark-drawer .ant-drawer-content {
  transition: background-color 0.3s;
}

.dark-drawer.light-theme .ant-drawer-content {
  background-color: #FFFFFF !important;
}

.dark-drawer.dark-theme .ant-drawer-content {
  background-color: #1D2B39 !important;
}

/* 确保Drawer完全填充 */
.ant-drawer-wrapper-body {
  height: 100%;
}

/* 固定切换开关的背景颜色 */
.theme-switch {
  background-color: rgba(0, 0, 0, 0.25);
}

.theme-switch.ant-switch-checked {
  background-color: #0852F0 !important;
}

/* Deposit/Withdraw Modal Styles */
.deposit-withdraw-modal .ant-modal-content {
  background-color: transparent !important;
  box-shadow: none !important;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.deposit-withdraw-modal .ant-modal-close {
  color: #fff;
  top: 12px;
  right: 12px;
  z-index: 100;
  position: absolute;
}

.deposit-withdraw-container {
  border-radius: 12px;
  overflow: hidden;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
}

.deposit-withdraw-container .ant-modal-close {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 100;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.deposit-withdraw-container.light-theme {
  background-color: #FFFFFF;
  color: #000000;
  border: 1px solid #E7E7E7;
}

.deposit-withdraw-container.dark-theme {
  background-color: #1D2B39;
  color: #FFFFFF;
  border: 1px solid #243546;
}

.deposit-withdraw-container.light-theme .ant-modal-close {
  color: #777E8C;
}

.deposit-withdraw-container.dark-theme .ant-modal-close {
  color: #fff;
}

/* Tab按钮样式 */
.modal-tabs {
  display: flex;
  width: 100%;
  padding: 10px;
}

.tab-button {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  border-radius: 8px 0px 0px 8px;
  margin-left: 20px;
  /* margin: 0 5px; */
}

.tab-buttonleft {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  border-radius: 0px 8px 8px 0px;
  margin-right: 20px;
}

/* 表单样式 */
.modal-form {
  padding: 20px 30px 30px;
}

.form-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  width: 300px;
  margin-left: -30px;
}

.field-value {
  font-size: 14px;
  font-weight: 600;
  width: 200px;
  padding-left: 100px;
}

.available-balance {
  color: #1677FF;
}

/* 金额输入框 */
.amount-input-container {
  position: relative;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
}

.amount-input {
  height: 48px;
  width: 100%;
  padding: 0 16px;
  font-size: 16px;
  outline: none;
  border-radius: 8px;
}

.max-button {
  position: absolute;
  right: 8px;
  height: 32px;
  border: none;
  border-radius: 16px;
  padding: 0 12px;
  font-weight: 500;
  cursor: pointer;
}

/* 地址输入框 */
.address-input {
  height: 48px;
  width: 100%;
  padding: 0 16px;
  font-size: 14px;
  outline: none;
  border-radius: 8px;
  margin-bottom: 24px;
  cursor: default;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 48px;
  border: none;
  border-radius: 8px;
  background-color: #1677FF;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  margin-top: 12px;
}

.submit-button:hover {
  opacity: 0.9;
}

/* Particle ConnectButton styling */
:root {
  --particle-connect-button-height: 40px;
  --particle-connect-button-font-weight: 700;
  --particle-connect-button-background-color: #2C9CDC;
  --particle-connect-button-border-color: #2C9CDC;
  --particle-connect-button-border-radius: 8px;
  --particle-connect-button-width: 120px;
  --particle-connect-button-color: #FFFFFF;
  --particle-connect-button-box-shadow: none;
}

/* Light theme override */
.light-theme {
  --particle-connect-button-background-color: #0852F0;
  --particle-connect-button-border-color: #0852F0;
}

/* Override Particle ConnectButton styles with high specificity */
div[class*="connectkit-connect-button"],
button[class*="connectkit-connect-button"],
[data-particle-element="connect-button"] button,
[data-particle-element="connect-button"] div,
.connect-wallet-btn,
button[data-connectkit-button],
div[data-connectkit-button] {
  height: var(--particle-connect-button-height) !important;
  font-weight: var(--particle-connect-button-font-weight) !important;
  background-color: var(--particle-connect-button-background-color) !important;
  border-color: var(--particle-connect-button-border-color) !important;
  border-radius: var(--particle-connect-button-border-radius) !important;
  width: var(--particle-connect-button-width) !important;
  color: var(--particle-connect-button-color) !important;
  box-shadow: var(--particle-connect-button-box-shadow) !important;
}