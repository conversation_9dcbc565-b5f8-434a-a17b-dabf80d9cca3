import axios, { type AxiosInstance, type InternalAxiosRequestConfig, type AxiosResponse, type AxiosRequestConfig, type AxiosProgressEvent } from 'axios';
// import { message } from 'antd';

// 创建主API实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'https://dp.yc365.io/dp/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 创建订单系统API实例
export const orderRequest: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_SECOND_BASE_URL || 'https://dp.yc365.io/orderbook',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 创建DAPP API实例
export const dappRequest: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_THIRD_BASE_URL || 'https://dp.yc365.io/dapp/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 创建文件上传API实例
export const fileRequest: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APIFILE_URL || 'https://dapp.yc365.io/dp/file',
  timeout: 60000, // 文件上传需要更长的超时时间
  headers: {
    'Content-Type': 'multipart/form-data'
  }
});


// 创建文件上传API实例
export const commentRequest: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_COMMENT_URL || 'http://dev-00.qday.ninja',
  timeout: 60000, // 文件上传需要更长的超时时间
  headers: {
    'Content-Type': 'multipart/form-data'
  }
});


// 添加请求拦截器
dappRequest.interceptors.request.use(
  (config) => {
    // 每次请求时重新从localStorage获取token，确保使用最新的token
    const token = localStorage.getItem('token');
    console.log(`dappRequest 拦截器 - 请求URL: ${config.url}, token存在: ${!!token}`);
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      // 记录头部信息，用于调试
      console.log('Authorization头部已添加:', config.headers.Authorization.substring(0, 20) + '...');
    } else {
      console.warn('请求缺少Authorization token:', config.url);
    }
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
dappRequest.interceptors.response.use(
  (response) => {
    console.log('Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error) => {
    console.error('Response error:', {
      url: error.config?.url,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    return Promise.reject(error);
  }
);

// 设置拦截器的函数
const setupInterceptors = (instance: AxiosInstance, isFileUpload: boolean = false) => {
  // 请求拦截器
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // 如果不是文件上传请求，才添加 token
      if (!isFileUpload) {
        // 每次请求时重新获取token，确保使用最新的
        const token = localStorage.getItem('token');
        const url = config.url || '';
        
        if (token && config.headers) {
          // 确保使用正确的格式：Bearer {token}
          (config.headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;
          console.log(`请求 ${url} - 已添加Authorization头部`);
        } else if (!token) {
          console.warn(`请求 ${url} - 没有找到token`);
        }
      }
      console.log('请求配置:', {
        url: config.url,
        method: config.method,
        headers: {
          ...config.headers,
          Authorization: config.headers?.Authorization ? 
                        (config.headers.Authorization as string).substring(0, 15) + '...' : 
                        'none'
        }
      });
      return config;
    },
    error => {
      console.error('请求错误:', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      const res = response.data;
      // 直接返回响应数据，不做额外处理
      return res;
    },
    error => {
      // const errorMessage = error.response?.data?.message || error.message || '网络错误';
      console.error('响应错误:', error);
      if (error.response?.status === 401) {
        // localStorage.removeItem('token');
        // window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );
};

setupInterceptors(request);
setupInterceptors(orderRequest);
setupInterceptors(dappRequest);
setupInterceptors(fileRequest, true);
setupInterceptors(commentRequest, true);

// 请求函数类型
interface RequestFunctions {
  get: <T = any>(url: string, params?: any) => Promise<T>;
  post: <T = any>(url: string, data?: any) => Promise<T>;
  put: <T = any>(url: string, data?: any) => Promise<T>;
  del: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => Promise<T>;
  upload: <T = any>(url: string, file: File, onProgress?: (percent: number) => void, additionalData?: Record<string, any>, options?: { maxSize?: number }) => Promise<T>;
}

// 创建请求函数的工厂
const createRequestFunctions = (instance: AxiosInstance): RequestFunctions => {
  return {
    get: (url, params) => {
      // 直接将params作为配置对象传递给axios，支持直接传headers
      if (params && typeof params === 'object' && 'headers' in params) {
        return instance({
          method: 'get',
          url,
          ...params // 将整个对象展开，包括headers和其他配置
        }).then(res => res as any);
      } else {
        // 传统方式，params作为查询参数
        return instance({
          method: 'get',
          url,
          params
        }).then(res => res as any);
      }
    },
    post: (url, data) => {
      return instance({
        method: 'post',
        url,
        data
      }).then(res => res as any);
    },
    put: (url, data) => {
      return instance({
        method: 'put',
        url,
        data
      }).then(res => res as any);
    },
    del: (url, data, config = {}) => {
      return instance({
        method: 'delete',
        url,
        data,
        ...config
      }).then(res => res as any);
    },
    upload: (url, file, onProgress, additionalData = {}, options = {}) => {
      const maxSize = options.maxSize || 0.5 * 1024 * 1024;
      if (file.size > maxSize) {
        return Promise.reject({
          message: `文件大小超过限制(${(maxSize/1024/1024).toFixed(1)}MB)`,
          code: 'FILE_TOO_LARGE'
        });
      }
      const formData = new FormData();
      formData.append('file', file);
      // 确保 additionalData 中的值都是字符串
      Object.keys(additionalData).forEach(key => {
        formData.append(key, String(additionalData[key]));
      });
      
      return instance({
        method: 'post',
        url,
        data: formData,
        onUploadProgress: onProgress ? (progressEvent: AxiosProgressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(percentCompleted);
          }
        } : undefined
      }).then(res => res as any).catch(error => {
        if (error.response) {
          if (error.response.status === 413) {
            throw new Error('文件大小超过服务器限制，请尝试上传更小的文件');
          } else {
            throw new Error(`服务器错误 (${error.response.status}): ${error.response.data?.message || '未知错误'}`);
          }
        } else if (error.request) {
          if (error.message.includes('Network Error')) {
            throw new Error('网络错误，请检查您的网络连接或服务器是否可用');
          } else {
            throw new Error('请求超时或没有响应');
          }
        } else {
          throw error;
        }
      });
    }
  };
};

export const { get, post, put, del, upload } = createRequestFunctions(request);
export const orderApi = createRequestFunctions(orderRequest);
export const dappApi = createRequestFunctions(dappRequest);
export const fileApi = createRequestFunctions(fileRequest);
export const commentApi = createRequestFunctions(commentRequest);

export default request; 