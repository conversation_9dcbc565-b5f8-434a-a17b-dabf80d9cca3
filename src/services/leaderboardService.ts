import { DAPP_API } from './apiPaths';

export interface LeaderboardUser {
  rank: number;
  address: string;
  username: string | null;
  profile_image: string | null;
  earning?: string;
  volume?: string;
  balance: string;
  user_earning: string;
  user_volume: string;
}

export interface LeaderboardResponse {
  status: string;
  message: string;
  data: {
    type: string;
    items: LeaderboardUser[];
  };
}

export interface LeaderboardData {
  volume: LeaderboardUser[];
  profit: LeaderboardUser[];
}

/**
 * Fetch earning leaderboard data from the server
 * @param type - The time filter for the leaderboard (daily, weekly, monthly, all)
 * @param limit - Number of records to return (default: 20, max: 100)
 * @returns Promise with earning leaderboard data
 */
export const fetchEarningLeaderboard = async (type: string = 'all', limit: number = 20): Promise<LeaderboardUser[]> => {
  try {
    const params = new URLSearchParams();
    if (type !== 'all') params.append('type', type);
    params.append('limit', limit.toString());
    
    const response = await fetch(`${import.meta.env.VITE_THIRD_BASE_URL}/v1/leaderboard/earning?${params}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch earning leaderboard: ${response.status}`);
    }
    
    const data: LeaderboardResponse = await response.json();
    
    if (data.status === 'success') {
      return data.data.items;
    } else {
      console.error('Error fetching earning leaderboard:', data);
      throw new Error('Failed to fetch earning leaderboard');
    }
  } catch (error) {
    console.error('Error fetching earning leaderboard:', error);
    return [];
  }
};

/**
 * Fetch volume leaderboard data from the server
 * @param type - The time filter for the leaderboard (daily, weekly, monthly, all)
 * @param limit - Number of records to return (default: 20, max: 100)
 * @returns Promise with volume leaderboard data
 */
export const fetchVolumeLeaderboard = async (type: string = 'all', limit: number = 20): Promise<LeaderboardUser[]> => {
  try {
    const params = new URLSearchParams();
    if (type !== 'all') params.append('type', type);
    params.append('limit', limit.toString());
    
    const response = await fetch(`${import.meta.env.VITE_THIRD_BASE_URL}/v1/leaderboard/volume?${params}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch volume leaderboard: ${response.status}`);
    }
    
    const data: LeaderboardResponse = await response.json();
    
    if (data.status === 'success') {
      return data.data.items||[];
    } else {
      console.error('Error fetching volume leaderboard:', data);
      throw new Error('Failed to fetch volume leaderboard');
    }
  } catch (error) {
    console.error('Error fetching volume leaderboard:', error);
    return [];
  }
};

/**
 * Fetch leaderboard data from the server (legacy function for backward compatibility)
 * @param timeFilter - The time filter for the leaderboard (day, week, month, all)
 * @returns Promise with leaderboard data
 */
export const fetchLeaderboardData = async (timeFilter: string = 'all'): Promise<LeaderboardData> => {
  try {
    // Map timeFilter to API type parameter
    const apiType = timeFilter === 'day' ? 'daily' : 
                   timeFilter === 'week' ? 'weekly' : 
                   timeFilter === 'month' ? 'monthly' : 'all';
    
    const [volumeData, earningData] = await Promise.all([
      fetchVolumeLeaderboard(apiType),
      fetchEarningLeaderboard(apiType)
    ]);
    
    return {
      volume: volumeData,
      profit: earningData
    };
  } catch (error) {
    console.error('Error fetching leaderboard data:', error);
    return {volume:[],profit:[]};
  }
};

// /**
//  * Get mock volume leaderboard data for development/fallback
//  * @returns Mock volume leaderboard data
//  */
// const getMockVolumeData = (): LeaderboardUser[] => {
//   return [
//     { rank: 1, address: '0x1234567890123456789012345678901234567890', username: 'TheGuru', profile_image: '/avatars/avatar1.png', volume: '397495654', balance: '5000000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 2, address: '0x0987654321098765432109876543210987654321', username: '🔫JustPunched', profile_image: '/avatars/avatar2.png', volume: '383250742', balance: '4500000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 3, address: '0x1111111111111111111111111111111111111111', username: 'interstellaar', profile_image: '/avatars/avatar3.png', volume: '337854532', balance: '4000000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 4, address: '0x2222222222222222222222222222222222222222', username: 'YatSen', profile_image: '/avatars/avatar4.png', volume: '287244433', balance: '3500000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 5, address: '0x3333333333333333333333333333333333333333', username: 'cigarettes', profile_image: '/avatars/avatar5.png', volume: '256333544', balance: '3000000', user_earning: '100.00', user_volume: '1000.00' },
//   ];
// };

// /**
//  * Get mock earning leaderboard data for development/fallback
//  * @returns Mock earning leaderboard data
//  */
// const getMockEarningData = (): LeaderboardUser[] => {
//   return [
//     { rank: 1, address: '0x4444444444444444444444444444444444444444', username: 'Theo4', profile_image: '/avatars/avatar10.png', earning: '22053934', balance: '2500000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 2, address: '0x5555555555555555555555555555555555555555', username: 'Fredi9999', profile_image: '/avatars/avatar11.png', earning: '16620028', balance: '2000000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 3, address: '0x6666666666666666666666666666666666666666', username: 'Len9311238', profile_image: '/avatars/avatar12.png', earning: '8709973', balance: '1500000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 4, address: '0x7777777777777777777777777777777777777777', username: 'zxgngl', profile_image: '/avatars/avatar13.png', earning: '7807266', balance: '1200000', user_earning: '100.00', user_volume: '1000.00' },
//     { rank: 5, address: '0x8888888888888888888888888888888888888888', username: 'RepTrump', profile_image: '/avatars/avatar14.png', earning: '7532410', balance: '1000000', user_earning: '100.00', user_volume: '1000.00' },
//   ];
// };

// /**
//  * Get mock leaderboard data for development/fallback
//  * @returns Mock leaderboard data
//  */
// const getMockLeaderboardData = (): LeaderboardData => {
//   return {
//     volume: getMockVolumeData(),
//     profit: getMockEarningData()
//   };
// }; 