/**
 * API路径常量
 * 集中管理所有API端点
 */

// 主API端点类型
export interface ApiPaths {
  LOGIN: string;
  REGISTER: string;
  LOGOUT: string;
  GET_USER_INFO: string;
  UPDATE_USER_INFO: string;
  UPLOAD_FILE: string;
  UPLOAD_IMAGE: string;
  BYCID: string;

}

export const API: ApiPaths = {
  LOGIN: '/dp/api/login',
  REGISTER: '/dp/api/register',
  LOGOUT: '/dp/api/logout',
  GET_USER_INFO: '/dp/api/user/info',
  UPDATE_USER_INFO: '/dp/api/user/update',
  UPLOAD_FILE: '/dp/api/upload',
  UPLOAD_IMAGE: '/upload',
  BYCID:"/v1/condition/bycid"
};

// 订单系统API端点类型
export interface OrderApiPaths {
  LIST: string;
  DETAIL: string;
  CREATE: string;
  UPDATE: string;
  DELETE: string;
}

export const ORDER_API: OrderApiPaths = {
  LIST: '/orders',
  DETAIL: '/orderbook/orders/detail',
  CREATE: '/orderbook/orders/create',
  UPDATE: '/orderbook/orders/update',
  DELETE: '/orderbook/orders/delete',
};

// DAPP API端点类型
export interface DappApiPaths {
  NONCE: string;
  AUTH_LOGIN: string;
  GET_OVERVIEW:string;
  GET_PROFILE: string;
  UPDATE_PROFILE: string;
  GAMES_LIST: string;
  GAME_DETAIL: string;
  D_EARNING: string;
  D_DAILY: string;
  D_ORDER: string;
  GAME_TYPES: string;
  SEARCH_GAMES: string;
  LEADERBOARD: string;
}

export const DAPP_API: DappApiPaths = {
  NONCE: '/v1/auth/nonce',
  AUTH_LOGIN: '/v1/auth/login',
  GET_OVERVIEW:'/v1/balance/overview',
  GET_PROFILE: '/v1/profile',
  UPDATE_PROFILE: '/v1/profile',
  GAMES_LIST: '/games',
  GAME_DETAIL: '/games/detail',
  D_EARNING:'/v1/balance/earning',
  D_DAILY:'/v1/balance/trend/daily',
  D_ORDER:'/v1/dashboard/order',
  GAME_TYPES: '/v1/gametype/all',
  SEARCH_GAMES: '/v1/game/search',
  LEADERBOARD: '/v1/leaderboard'
};

/**
 * 根据参数构建完整路径
 * @param base - 基础路径
 * @param id - 替换:id的值
 * @returns 完整路径
 */
export const buildPath = (base: string, id?: string): string => {
  if (!id) return base;
  return base.replace(':id', id);
};

const apiPaths = {
  API,
  ORDER_API,
  DAPP_API,
  buildPath,
};

export default apiPaths; 