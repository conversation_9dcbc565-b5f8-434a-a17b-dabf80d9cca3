{"app": {"title": "Framework React", "description": "Framework React dengan dukungan multi-bahasa dan tema", "theme": {"light": "<PERSON><PERSON>", "dark": "<PERSON><PERSON>"}, "language": {"zh": "Bahasa Mandarin", "en": "Bahasa Inggris"}, "header": {"home": "Be<PERSON><PERSON>", "about": "Tentang", "settings": "<PERSON><PERSON><PERSON><PERSON>", "portfolio": "Portofolio", "cash": "<PERSON>"}, "welcome": "Selamat datang di aplikasi kami", "counter": "Penghitung: {{count}}", "increment": "Tambah", "switchTheme": "<PERSON><PERSON> Tema", "switchLanguage": "Ubah Bahasa"}, "home": {"search": "<PERSON><PERSON>", "connectWallet": "Hubungkan Dompet", "welcomeBannerTitle": "Selamat Datang di YC365!", "searchPlaceholder": "Cari...", "featuredMarkets": "<PERSON><PERSON>", "noMarketsFound": "Tidak ada pasar di<PERSON>ukan", "searchResults": "<PERSON><PERSON>", "loadingError": "Gagal memuat data, silakan coba lagi nanti", "retry": "Coba lagi"}, "footer": {"home": "Be<PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "person": "P<PERSON><PERSON><PERSON>"}, "drawer": {"menuTitle": "<PERSON><PERSON>", "personalCenter": "Pusat P<PERSON>badi", "overview": "<PERSON><PERSON><PERSON><PERSON>", "setting": "<PERSON><PERSON><PERSON><PERSON>", "myOrders": "<PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON>", "myFavorites": "<PERSON><PERSON><PERSON><PERSON>", "doc": "Dokumen", "docContracts": "Kontrak Dokumen", "systemArchitecture": "Arsitektur Sistem", "openAI": "API Terbuka", "theme": "<PERSON><PERSON>", "disconnect": "<PERSON><PERSON><PERSON>", "term": "<PERSON><PERSON><PERSON>", "language": "Bahasa"}, "default": {"profileSettings": "Pengaturan Profil", "username": "<PERSON><PERSON>", "nogame": "Tidak ada permainan", "error": "<PERSON><PERSON><PERSON><PERSON>", "errorFetch": "Gagal mengambil data", "keyword": "<PERSON><PERSON><PERSON> masukkan kata kunci", "noData": "Tidak ada data", "assets": "<PERSON><PERSON>", "balance": "<PERSON><PERSON>", "deposit": "<PERSON><PERSON>", "withdraw": "Penarikan", "orders": "<PERSON><PERSON><PERSON>", "connectwallet": "Hubungkan Dompet", "disconnect": "<PERSON><PERSON><PERSON>", "tradeYes": "Volume Perdagangan", "buyyes": "<PERSON><PERSON>", "buyno": "Beli Tidak", "buy": "Bel<PERSON>", "yes": "Ya", "no": "Tidak", "max": "<PERSON><PERSON><PERSON><PERSON>", "none": "Tidak ada", "last": "<PERSON><PERSON><PERSON>", "spread": "Spread", "available": "Tersedia", "byTrading": "<PERSON><PERSON>, <PERSON><PERSON>", "terms": "<PERSON><PERSON><PERSON>", "chance": "<PERSON><PERSON><PERSON>", "withdrawAmount": "<PERSON><PERSON><PERSON>", "depositAmount": "<PERSON><PERSON><PERSON>", "enterAmount": "<PERSON><PERSON><PERSON> masukkan jumlah", "enterVaildAmount": "<PERSON><PERSON><PERSON> masukkan jumlah yang valid", "receivingAddress": "<PERSON><PERSON><PERSON>", "paymentAddress": "<PERSON><PERSON><PERSON>", "contractAddress": "<PERSON><PERSON><PERSON>", "network": "Jaringan & Mata Uang", "submitTrans": "<PERSON><PERSON>", "handleSubmitTransactionError": "❌ <PERSON><PERSON><PERSON> gagal", "amountSuccessfull": "<PERSON><PERSON><PERSON> be<PERSON>", "processing": "Memproses...", "search": "<PERSON><PERSON>", "loading": "Memuat...", "potentialReturn": "<PERSON><PERSON><PERSON>", "PleaseEnterTheValidAmount": "<PERSON><PERSON><PERSON> masukkan jumlah yang valid", "AmountDepositSuccessfully": "<PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>"}, "dashboard": {"title": "<PERSON><PERSON>", "balance": "<PERSON><PERSON>", "earnings": "Pendapatan Saya", "activity": "Aktivitas Saya", "orderQuantity": "<PERSON><PERSON><PERSON>", "orderAmount": "<PERSON><PERSON><PERSON>", "frozenAssets": "<PERSON><PERSON>", "availableAssets": "<PERSON><PERSON>"}, "myOrders": {"title": "<PERSON><PERSON><PERSON>", "events": "<PERSON>car<PERSON>", "conditions": "<PERSON><PERSON><PERSON>", "currency": "<PERSON>", "PSLA": "Harga / Jumlah / Total", "time": "<PERSON><PERSON><PERSON>", "status": "Status", "Previous": "Sebelumnya", "NextPage": "Halaman Be<PERSON>", "order": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membatalkan pesanan ini?", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON>", "CancelOrder": "<PERSON><PERSON><PERSON>", "Price": "<PERSON><PERSON>", "limitPrice": "<PERSON><PERSON>", "limit": "Limit", "MarketValue": "<PERSON><PERSON>", "reward": "<PERSON><PERSON>", "outcome": "<PERSON><PERSON>", "BSOrders": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>", "Shares": "<PERSON><PERSON>", "total": "Total", "orderBook": "<PERSON><PERSON>", "graph": "<PERSON><PERSON>", "Fee": "Biaya", "TotalAmount": "<PERSON><PERSON><PERSON>", "ExecutedVolume": "Volume Dieksekusi", "ExecutedAmount": "<PERSON><PERSON><PERSON>", "ExpectedEarnings": "Pendapatan Aktual", "OrderType": "<PERSON><PERSON>", "OrderID": "<PERSON> Pesanan", "Time": "<PERSON><PERSON><PERSON>", "Completed": "Se<PERSON><PERSON>", "Cancelled": "Di<PERSON><PERSON><PERSON>", "WaitingForTransaction": "Menunggu <PERSON>aksi", "PartiallyExecuted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Terminated": "Dihentikan", "cent": "<PERSON>", "type": "<PERSON><PERSON>"}, "OrderDetail": {"title": "<PERSON><PERSON><PERSON>", "conditions": "<PERSON><PERSON><PERSON>", "currency": "<PERSON>", "price": "<PERSON><PERSON>", "buySellOrders": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>", "buy": "Bel<PERSON>", "sell": "<PERSON><PERSON>", "shares": "<PERSON><PERSON>", "fee": "Biaya", "executedVolume": "Volume Dieksekusi", "executedAmount": "<PERSON><PERSON><PERSON>", "expectedEarnings": "<PERSON><PERSON><PERSON>", "status": "Status", "orderType": "<PERSON><PERSON>", "orderId": "<PERSON> Pesanan", "time": "<PERSON><PERSON><PERSON>", "limitOrder": "<PERSON><PERSON><PERSON>", "noLimitOrder": "<PERSON><PERSON><PERSON>", "unknown": "Tidak Diketahui", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "waitingForTransaction": "Menunggu <PERSON>aksi", "partiallyExecuted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terminated": "Dihentikan", "noAsksData": "Tidak ada data penjualan", "noBidsData": "Tidak ada data pembelian", "noData": "Tidak ada data", "loading": "Memuat...", "post": "<PERSON><PERSON>", "rules": "<PERSON><PERSON><PERSON>", "comments": "Komentar", "orderBook": "<PERSON><PERSON>", "writeComment": "<PERSON><PERSON> komentar...", "writeReply": "<PERSON><PERSON> b<PERSON>...", "Cent": "<PERSON>", "noOrderSelected": "Tidak ada pesanan yang dipilih"}, "comments": {"noComments": "Tidak ada komentar", "fetchError": "Gagal mengambil komentar", "fetchRepliesError": "<PERSON><PERSON> balasan", "loginRequired": "<PERSON><PERSON><PERSON> login terle<PERSON>h dahulu", "emptyComment": "Konten komentar tidak boleh kosong", "emptyReply": "Konten balasan tidak boleh kosong", "submitSuccess": "<PERSON><PERSON><PERSON><PERSON>m komentar", "submitError": "Gagal mengirim komentar", "replySuccess": "<PERSON><PERSON><PERSON><PERSON> balasan", "replyError": "<PERSON><PERSON> men<PERSON>m balasan", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON>", "reply": "<PERSON><PERSON>", "delete": "Hapus", "cancelReply": "Bat<PERSON><PERSON> balasan", "viewReplies": "<PERSON><PERSON> b<PERSON>", "hideReplies": "<PERSON><PERSON><PERSON><PERSON><PERSON> balasan", "noReplies": "Tidak ada balasan"}, "settings": {"walletAddress": "<PERSON><PERSON><PERSON>", "walletSetting": "Pengatura<PERSON>", "BSCRPC": "BSC RPC", "enterAddress": "<PERSON><PERSON><PERSON><PERSON>", "enterRPC": "Masukkan link RPC", "rpcUrlRequired": "Link RPC diperlukan", "usernameRequired": "<PERSON>a pengguna diperlukan", "gasPrice": "<PERSON>rga <PERSON>", "saveChanges": "<PERSON><PERSON><PERSON>"}, "overview": {"assets": "<PERSON><PERSON>", "depositBalance": "<PERSON><PERSON>", "gasBalance": "Saldo Gas", "vaultBalance": "<PERSON><PERSON>", "totalEarning": "Total Pendapatan", "last10": "Hanya menghitung pendapatan 10 hari terakhir", "earning": "Pendapatan", "recentEarning": "Pendapatan Terbaru", "7day": "Pendapatan 7 Hari", "joinedDate": "Bergabung {{date}}"}, "activity": {"activity": "Aktivitas", "profit_loss": "Untung/Rugi", "volume": "Volume", "price": "<PERSON><PERSON>"}, "leaderboard": {"leaderboard": "<PERSON><PERSON>"}}