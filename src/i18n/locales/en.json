{"app": {"title": "React Framework", "description": "A React Framework with i18n and Theme Switching", "theme": {"light": "Light Theme", "dark": "Dark Theme"}, "language": {"zh": "Chinese", "en": "English"}, "header": {"home": "Home", "about": "About", "settings": "Settings", "portfolio": "Portfolio", "cash": "Cash"}, "welcome": "Welcome to our application", "counter": "Counter: {{count}}", "increment": "Increment", "switchTheme": "Theme", "switchLanguage": "Language"}, "home": {"search": "Search", "connectWallet": "CONNECT WALLET", "welcomeBannerTitle": "WELCOME TO YC365!", "searchPlaceholder": "Search...", "featuredMarkets": "Featured Markets", "noMarketsFound": "No markets found", "searchResults": "Search Result", "loadingError": "Failed to load data, please try again later", "retry": "Retry", "sortBy": {"createTime": "Create time", "expireTime": "Expire time", "totalVolume": "Total volume", "volume24h": "24h volume", "liquidity": "Liquidity"}}, "footer": {"home": "Home", "orders": "Orders", "person": "Person"}, "drawer": {"menuTitle": "<PERSON><PERSON>", "personalCenter": "Personal Center", "overview": "Overview", "setting": "Setting", "myOrders": "My Orders", "dashboard": "Dashboard", "myFavorites": "My Favorites", "doc": "Documentation", "docContracts": "Document Contracts", "systemArchitecture": "System Architecture", "openAI": "Open API", "theme": "Theme", "disconnect": "Disconnect", "term": "Terms of Use", "language": "Language"}, "header": {"portfolio": "Portfolio", "cash": "Cash"}, "default": {"profileSettings": "Profile Settings", "username": "Username", "nogame": "There is no game", "error": "There was some error", "errorFetch": "Error fetching data.", "keyword": "Please Enter Keywords", "noData": "no more", "assets": "My Assets", "balance": "Balance", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "orders": "My Orders", "connectwallet": "Connect Wallet", "disconnect": "Disconnect", "tradeYes": "TRADE YES", "buyyes": "Buy Yes", "buyno": "Buy No", "buy": "BUY", "yes": "Yes", "no": "No", "max": "Max", "none": "None", "last": "Last", "spread": "Spread", "available": "Available", "byTrading": "By trading,you agree to the", "terms": "terms of Use", "chance": "CHANCE", "withdrawAmount": "Withdraw Amount", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "enterAmount": "Please Enter Amount", "enterVaildAmount": "Please enter a valid amount", "receivingAddress": "Receiving Address", "paymentAddress": "Payment Address", "contractAddress": "Contract Address", "network": "Network & Currency", "submitTrans": "Submit Transaction", "handleSubmitTransactionError": "❌ Transaction error", "amountSuccessfull": "Amount Withdraw Successfully", "processing": "processing...", "search": "Search", "loading": "Loading...", "potentialReturn": "Potential Return", "PleaseEnterTheValidAmount": "Please enter the valid amount", "AmountDepositSuccessfully": "Amount Deposit Successfully", "amount": "coin amount"}, "dashboard": {"title": "Dashboard", "balance": "My Balance", "earnings": "My Earnings", "activity": "My Activity", "orderQuantity": "Order Quantity", "orderAmount": "Order Amount", "frozenAssets": "<PERSON><PERSON><PERSON>", "availableAssets": "Available Assets"}, "myOrders": {"title": "My Orders", "events": "Events", "conditions": "Conditions", "currency": "<PERSON><PERSON><PERSON><PERSON>", "PSLA": "Price / Shares / Total Amount", "time": "Time", "status": "Status", "Previous": "Previous", "NextPage": "NextPage", "order": "Are you sure you want to withdraw your current order?", "verify": "sure", "Cancel": "Cancel", "CancelOrder": "Cancel an order", "Price": "Price", "limitPrice": "<PERSON>it <PERSON>", "limit": "<PERSON>it <PERSON>", "MarketValue": "Market Price", "reward": "Rewards", "outcome": "Outcome", "BSOrders": "Buy/Sell Orders", "Shares": "Shares", "total": "Total", "orderBook": "Order Book", "graph": "Graph", "Fee": "Fee", "TotalAmount ": "Total Amount", "ExecutedVolume": "Completed Amount", "ExecutedAmount": "<PERSON><PERSON>", "ExpectedEarnings": "Real Revenue", "OrderType": "Order Type", "OrderID": "OrderID", "Time": "Time", "Completed": "Completed", "Cancelled": "Cancelled", "WaitingForTransaction": "Waiting for transaction", "PartiallyExecuted": "Partially executed", "Terminated": "Terminated", "cent": "Cent", "type": "type", "Sell": "SELL", "sampleEvent": "Which Countries Will The U.S. Agree To Trade Deals With Before July?", "orderType": "Order Type", "allTypes": "All Types", "buy": "Buy", "sell": "<PERSON>ll", "allStatus": "All Status", "largeOrdersOnly": "Large Orders Only (>100)"}, "OrderDetail": {"title": "Title", "conditions": "Conditions", "currency": "<PERSON><PERSON><PERSON><PERSON>", "price": "PRICE", "buySellOrders": "Buy/Sell Orders", "buy": "Buy", "sell": "<PERSON>ll", "shares": "SHARES", "fee": "Fee", "executedVolume": "Executed Volume", "executedAmount": "Executed Amount", "expectedEarnings": "Failed Amount", "status": "Status", "orderType": "Order Type", "orderId": "Order ID", "time": "Time", "limitOrder": "Limit Order", "noLimitOrder": "No Limit Order", "unknown": "Unknown", "completed": "Completed", "cancelled": "Cancelled", "waitingForTransaction": "Waiting for transaction", "partiallyExecuted": "Partially executed", "terminated": "Terminated", "noAsksData": "No Asks Data Found", "noBidsData": "No Bids Data Found", "orderBook": "Order Book", "rules": "Rules", "comments": "Comments", "writeComment": "Write a comment...", "writeReply": "Write a reply...", "post": "Post", "noData": "No Data", "loading": "Loading...", "Cent": "Cent", "noOrderSelected": "No order selected"}, "comments": {"noComments": "No comments yet", "fetchError": "Failed to fetch comments", "fetchRepliesError": "Failed to fetch replies", "loginRequired": "Please login to continue", "emptyComment": "Comment cannot be empty", "emptyReply": "Reply cannot be empty", "submitSuccess": "Comment posted successfully", "submitError": "Failed to post comment", "replySuccess": "Reply posted successfully", "replyError": "Failed to post reply", "deleteSuccess": "Deleted successfully", "deleteError": "Failed to delete", "reply": "Reply", "delete": "Delete", "cancelReply": "Cancel Reply", "viewReplies": "View Replies", "hideReplies": "Hide Replies", "noReplies": "No Replies"}, "favorite": {"add": "Add to Favorites", "remove": "Remove from Favorites", "added": "Added to Favorites", "removed": "Removed from Favorites", "noBookmarks": "No bookmarks yet"}, "share": {"twitter": "Share on Twitter"}, "settings": {"walletAddress": "Wallet Address", "walletSetting": "Wallet Setting", "BSCRPC": "BSC RPC", "enterAddress": "Enter Address", "enterRPC": "Enter RPC Url", "rpcUrlRequired": "RPC Url Required", "usernameRequired": "<PERSON>rname Required", "gasPrice": "Gas Price", "saveChanges": "Save Changes", "profileSettings": "Edit Profile"}, "overview": {"assets": "Assets", "depositBalance": "Deposit <PERSON>", "gasBalance": "Gas Balance", "vaultBalance": "<PERSON><PERSON> Balance", "totalEarning": "Total Earning", "last10": "Only count the most recent 10 days of earnings", "earning": "Earning", "recentEarning": "Recent Earning", "7day": "7 Day Earning", "joinedDate": "Joined {{date}}"}, "activity": {"activity": "Activity", "profit_loss": "Profit/Loss", "volume": "Volume", "price": "Price"}, "leaderboard": {"leaderboard": "Leaderboard"}}