import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
// import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// 导入语言包
import translationEN from './locales/en.json';
import translationZH from './locales/zh.json';
import translationZHCN from './locales/zh-CN.json';
import translationVI from './locales/vi.json';
import translationTH from './locales/th.json';
import translationID from './locales/id.json';

const resources = {
  en: {
    translation: translationEN
  },
  'zh-TW': {
    translation: translationZH
  },
  'zh-CN': {
    translation: translationZHCN
  },
  vi: {
    translation: translationVI
  },
  th: {
    translation: translationTH
  },
  id: {
    translation: translationID
  }
};

i18n
  // 使用后端插件加载翻译文件
  .use(Backend)
  // 检测用户语言
  // .use(LanguageDetector)
  // 将i18n实例传递给react-i18next
  .use(initReactI18next)
  // 初始化i18n
  .init({
    resources,
    fallbackLng: 'en', // 设置英文为默认语言
    debug: (import.meta.env.VITE_MODE || 'development') === 'development',
    interpolation: {
      escapeValue: false, // 不需要React转义
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    }
  });

export default i18n;