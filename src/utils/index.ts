/**
 * 合并游戏数据和条件数据
 * @param games 游戏列表数据
 * @param conditions 条件列表数据
 * @returns 合并后的数据
 */
export const mergeGameWithConditions = (games: any[], conditions: any[]) => {
  if (!games || !conditions) {
    return [];
  }
  
  return games.map(game => {
    // 找出当前游戏的所有条件
    const gameConditions = conditions.filter(condition => condition.game_id === game.id);
    
    // 如果有条件数据，则合并
    if (gameConditions && gameConditions.length > 0) {
      return {
        ...game,
        conditions: gameConditions,
        isGameCondition: true,
        // 计算胜率（使用第一个条件的yes_coin_price）
        chance: gameConditions[0]?.yes_coin_price || 0
      };
    }
    
    // 如果没有条件数据，返回原始游戏数据
    return {
      ...game,
      conditions: [],
      isGameCondition: false,
      chance: 0
    };
  });
}; 