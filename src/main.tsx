import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON><PERSON> } from "buffer";
import './index.css'
import { App } from './App'
import { UserProvider } from './context/UserContext'
// 导入国际化配置
import './i18n'
window.Buffer = Buffer;
// 导入Particle Connect
import { ParticleConnectkit } from './ConnectKit'
// const AmoyTestnet = defineChain({
//   id: 80002,
//   name: "Amoy",
//   nativeCurrency: {
//     decimals: 18,
//     name: "MATIC",
//     symbol: "MATIC",
//   },
//   rpcUrls: {
//     default: {
//       http: ["https://rpc-amoy.polygon.technology"],
//     },
//   },
//   blockExplorers: {
//     default: { name: "Explorer", url: "https://amoy.polygonscan.com/" },
//   },
//   testnet: true,
// });
export const BSCTestnet = define<PERSON>hain({
  id: 97,
  name: "BSC Testnet",
  nativeCurrency: {
    decimals: 18,
    name: "BN<PERSON>",
    symbol: "tBNB",
  },
  rpcUrls: {
    default: {
      http: ["https://bsc-testnet.public.blastapi.io"],
    },
  },
  blockExplorers: {
    default: { name: "BscScan", url: "https://testnet.bscscan.com/" },
  },
  testnet: true,
});

import { AuthType } from "@particle-network/auth-core";
import {
  AuthCoreContextProvider,
  PromptSettingType,
} from "@particle-network/authkit"; 
import { defineChain } from 'viem';
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AuthCoreContextProvider
      options={{
        projectId: import.meta.env.VITE_PROJECT_ID,
        clientKey: import.meta.env.VITE_CLIENT_KEY!,
        appId: import.meta.env.VITE_APP_ID!,
        authTypes: [
          AuthType.email,
          AuthType.google,
          AuthType.twitter,
          AuthType.github,
        ],
        themeType: "dark",

        // List the chains you want to include
        chains: [BSCTestnet],

        // Optionally, switches the embedded wallet modal to reflect a smart account
        // erc4337: {
        //   name: "SIMPLE",
        //   version: "2.0.0",
        // },

        // You can prompt the user to set up extra security measures upon login or other interactions
        promptSettingConfig: {
          promptPaymentPasswordSettingWhenSign: PromptSettingType.first,
          promptMasterPasswordSettingWhenLogin: PromptSettingType.first,
        },

        wallet: {
          themeType: "dark", // Wallet modal theme

          // Set to false to remove the embedded wallet modal
          visible: true,
          customStyle: {
            supportUIModeSwitch: true,
            supportLanguageSwitch: false,
          },
        },
      }}
    >
      <ParticleConnectkit>
        <UserProvider>

          <App />
        </UserProvider>
      </ParticleConnectkit>
    </AuthCoreContextProvider>
  </StrictMode>
);
