import React, { useEffect } from 'react';
import { ConnectButton, useAccount } from '@particle-network/connectkit'; 
import { useTranslation } from 'react-i18next';
import '../layout/Header.css'; 
// import { useAccount } from 'wagmi';
// import { ThemeType } from '../theme';
// import { useWeb3Modal } from '@web3modal/wagmi/react';

interface ConnectWalletButtonProps {
  className?: string;
  style?: React.CSSProperties;
  themeClass?: string;
  onConnect?: () => void;
}

const ConnectWalletButton: React.FC<ConnectWalletButtonProps> = ({ 
  className, 
  style,
  onConnect
}) => {
  const { t } = useTranslation();
  const { isConnected, address } = useAccount();
  // const { open } = useWeb3Modal();
  // const { connect } = useConnect();

  // 监听钱包连接状态，当连接成功时调用外部传入的连接方法
  useEffect(() => {
    if (isConnected && address) {
      // 如果有外部传入的连接方法，优先调用外部方法
      if (onConnect) {
        onConnect();
      }
      console.log('钱包已连接，地址:', address);
    }
  }, [isConnected, address, onConnect]);
  
  // 显示缩略地址
  // const formatAddress = (address: string | null) => {
  //   if (!address) return '';
  //   return `${address.slice(0, 6)}...${address.slice(-4)}`;
  // };

  return (
    <div className={className} style={style}>
      {/* {isLoggedIn ? (
        // 已连接状态显示地址
        <Button 
          className={`connect-wallet-btn ${themeClass}`}
          type="primary"
        >
          {formatAddress(address?address:null)}
        </Button>
      ) : (
        // 未连接状态使用Particle的ConnectButton */}
        <ConnectButton label={t('home.connectWallet')} />
      {/* )} */}

      {/* <button className={`connect-wallet-btn ${themeClass}`} onClick={() => open()}>{t('home.connectWallet')}</button> */}
    </div>
  );
};

export default ConnectWalletButton; 