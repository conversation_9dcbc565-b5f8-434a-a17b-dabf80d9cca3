import React from 'react';
import { useTranslation } from 'react-i18next';
import { useModal } from '@particle-network/connectkit';

interface CustomConnectButtonProps {
  themeClass: string;
  primaryColor: string;
}

export const CustomConnectButton: React.FC<CustomConnectButtonProps> = ({ themeClass, primaryColor }) => {
  const { t } = useTranslation();
  const { setOpen } = useModal();

  // 处理连接按钮点击 - 直接使用 Particle Network 的原生功能
  const handleConnectClick = () => {
    setOpen(true);
  };

  return (
    <button 
      onClick={handleConnectClick} 
      style={{
        height: '40px',
        fontWeight: 700,
        backgroundColor: primaryColor,
        borderColor: primaryColor,
        borderRadius: '8px',
        width: '180px',
        color: '#FFFFFF',
        boxShadow: 'none',
        border: 'none',
        cursor: 'pointer',
        whiteSpace: 'nowrap'
      }}
      className={`connect-wallet-btn ${themeClass}`}
    >
      {t('home.connectWallet')}
    </button>
  );
}; 