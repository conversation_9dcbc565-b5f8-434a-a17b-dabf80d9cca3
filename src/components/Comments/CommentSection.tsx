import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Button, Input, Avatar, Pagination, message, Spin } from 'antd';
import { useTheme } from '../../theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import { useUser } from '../../context/UserContext';
import { dappApi } from '../../services/request';
import userAvatarImage from '../../assets/figma_images/user_avatar_image.png';
import {useConnect, useConnectors} from '@particle-network/connectkit'; 
import axios from 'axios';
import { DownOutlined, UpOutlined } from '@ant-design/icons';

// API endpoints
const COMMENT_CREATE = '/v1/comment';
const COMMENT_GET = '/v1/comment';
const COMMENT_REPLIES_GET = '/v1/comment/reply';
const COMMENT_DELETE = '/v1/comment';

const { Text, Title } = Typography;

interface CommentReply {
  id: number;
  game_id: string;
  reply_to: number;
  content: string;
  user_address: string;
  user_name: string;
  create_time: number;
  profile_image: string;
}

interface CommentItem {
  id: number;
  game_id: string;
  reply_to: number;
  content: string;
  user_address: string;
  user_name: string;
  create_time: number;
  replies?: CommentReply[];
  profile_image: string;
}

interface CommentSectionProps {
  gameId: string;
}

const CommentSection: React.FC<CommentSectionProps> = ({ gameId }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { address } = useUser();
  const themeClass = theme.name === 'dark' ? 'dark-theme' : 'light-theme';
  const { connect } = useConnect();
  const connectors = useConnectors();
  const [comments, setComments] = useState<CommentItem[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyContent, setReplyContent] = useState<{ [key: number]: string }>({});
  const [showReplies, setShowReplies] = useState<number[]>([]);
  const [showReplyForms, setShowReplyForms] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [commentPage, setCommentPage] = useState(0);
  const [totalComments, setTotalComments] = useState(0);
  const [pageSize] = useState(15);
  const [submittingComment, setSubmittingComment] = useState(false);
  const [loadingReplies, setLoadingReplies] = useState<number[]>([]);

  // Fetch comments
  const fetchComments = async (page: number = 0) => {
    if (!gameId) return;

    setLoading(true);
    try {
      // Use template literals to manually construct the URL with query parameters
      const response = await dappApi.get(`${COMMENT_GET}?id=${gameId}&page=${page}&count=${pageSize}&reply_count=15`);

      if (response.data && response.status === 'success') {
        const commentData = response.data;
        setComments(commentData.comments || []);
        setTotalComments(commentData.total || 0);
        setCommentPage(commentData.page || 0);
        
        if (commentData.comments && commentData.comments.length > 0) {
          // For first 3 comments, load replies automatically
          const initialComments = commentData.comments.slice(0, 3);
          initialComments.forEach((comment: CommentItem) => {
            fetchReplies(comment.id);
            setShowReplies(prev => [...prev, comment.id]);
          });
        }
      }
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      message.error(t('comments.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  // Fetch more replies for a comment
  const fetchReplies = async (commentId: number) => {
    if (loadingReplies.includes(commentId)) return;
    
    setLoadingReplies(prev => [...prev, commentId]);
    
    try {
      // Use template literals for this API call too
      const response = await dappApi.get(`${COMMENT_REPLIES_GET}?id=${commentId}&page=0&count=100`);

      if (response.data && response.status === 'success') {
        const repliesData = response.data;
        
        if (repliesData && Array.isArray(repliesData)) {
          // Find the correct reply set for this comment
          const replySet = repliesData.find(set => set.reply_to === commentId);
          
          if (replySet && replySet.comments) {
            // Update the comments array with the fetched replies
            setComments(prevComments => 
              prevComments.map(comment => {
                if (comment.id === commentId) {
                  return {
                    ...comment,
                    replies: replySet.comments
                  };
                }
                return comment;
              })
            );
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch replies:', error);
      message.error(t('comments.fetchRepliesError'));
    } finally {
      setLoadingReplies(prev => prev.filter(id => id !== commentId));
    }
  };

  const checkWallet = async () => {
    console.log('address',address);
    if(address==null){
      const particleConnector = connectors[1];
      // 提供一个空对象作为参数，使其显示所有连接选项
      await connect({connector:particleConnector});
      return;
    }
  }
  // Submit a new comment
  const submitComment = async () => {
    console.log('address',address);
    if (address==null) {
      message.warning(t('comments.loginRequired'));
      checkWallet();
      return;
    }

    if (!newComment.trim()) {
      message.warning(t('comments.emptyComment'));
      return;
    }

    setSubmittingComment(true);
    try {
      const response = await dappApi.post(COMMENT_CREATE, {
        game_id: gameId,
        content: newComment.trim(),
        reply_to: 0
      });

      if (response.data && response.status === 'success') {
        message.success(t('comments.submitSuccess'));
        setNewComment('');
        // Refresh comments to include the new one
        fetchComments(commentPage);
      }
    } catch (error) {
      console.error('Failed to submit comment:', error);
      message.error(t('comments.submitError'));
    } finally {
      setSubmittingComment(false);
    }
  };

  // Submit a reply to a comment
  const submitReply = async (commentId: number) => {
    console.log('address',address);
    if (address==null) {
      message.warning(t('comments.loginRequired'));
      console.log('address',address);
      checkWallet();
      return;
    }

    if (!replyContent[commentId]?.trim()) {
      message.warning(t('comments.emptyReply'));
      return;
    }

    try {
      const response = await dappApi.post(COMMENT_CREATE, {
        game_id: gameId,
        content: replyContent[commentId].trim(),
        reply_to: commentId
      });

      if (response.data && response.status === 'success') {
        message.success(t('comments.replySuccess'));
        
        // Clear the reply content
        setReplyContent({
          ...replyContent,
          [commentId]: ''
        });
        
        // Refresh the replies for this comment
        fetchReplies(commentId);
      }
    } catch (error) {
      console.error('Failed to submit reply:', error);
      message.error(t('comments.replyError'));
    }
  };

  // Delete a comment or reply
  const deleteComment = async (commentId: number) => {
    if (!address) {
      message.warning(t('comments.loginRequired'));
      return;
    }

    try {
      // 从localStorage直接获取token
      const token = localStorage.getItem('token');
      
      if (!token) {
        message.error(t('comments.loginRequired'));
        return;
      }
      
      console.log('删除评论，评论ID:', commentId, '当前Token状态:', !!token);
      
      var config = {
        method: 'delete',
        url: import.meta.env.VITE_THIRD_BASE_URL + COMMENT_DELETE,
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        params: {
          "id": commentId
        }
      };
      
      const response = await axios(config);
      console.log('删除评论响应:', response.data);
      
      if (response.data) {
        message.success(t('comments.deleteSuccess'));
        
        // Refresh comments to reflect the deletion
        fetchComments(commentPage);
      }
    } catch (error) {
      console.error('Failed to delete comment:', error);
      message.error(t('comments.deleteError'));
    }
  };

  // Toggle showing/hiding replies for a comment
  const toggleReplies = (commentId: number) => {
    if (showReplies.includes(commentId)) {
      setShowReplies(showReplies.filter(id => id !== commentId));
    } else {
      setShowReplies([...showReplies, commentId]);
      
      // Fetch replies when expanding
      const comment = comments.find(c => c.id === commentId);
      if (!comment?.replies || comment.replies.length === 0) {
        fetchReplies(commentId);
      }
    }
  };

  // Toggle showing/hiding reply form
  const toggleReplyForm = (commentId: number) => {
    if (showReplyForms.includes(commentId)) {
      setShowReplyForms(showReplyForms.filter(id => id !== commentId));
    } else {
      setShowReplyForms([...showReplyForms, commentId]);
    }
  };

  // Handle reply input changes
  const handleReplyChange = (commentId: number, content: string) => {
    setReplyContent({
      ...replyContent,
      [commentId]: content
    });
  };

  // Handle pagination changes
  const handlePageChange = (page: number) => {
    fetchComments(page - 1); // API uses 0-based indexing
  };

  // Initial data fetch
  useEffect(() => {
    if (gameId) {
      fetchComments(0);
    }
  }, [gameId]);

  // Format date from timestamp
  const formatDate = (timestamp: number) => {
    const now = new Date();
    const commentDate = new Date(timestamp * 1000);
    
    const diffInSeconds = Math.floor((now.getTime() - commentDate.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else if (diffInSeconds < 604800) {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    } else {
      return commentDate.toLocaleDateString();
    }
  };

  // Check if user can delete a comment (only their own)
  const canDeleteComment = (userAddress: string) => {
    return address && address.toLowerCase() === userAddress.toLowerCase();
  };

  // 添加一个调试日志来检查地址状态
  useEffect(() => {
    console.log("当前用户地址状态:", address);
  }, [address]);


  return (
    <div className={`comments-section ${themeClass}`}>
      <Title level={4}>{t('OrderDetail.comments')} ({totalComments})</Title>
      <div style={{borderBottom: theme.name === "dark" ? "1px solid #2C3F4F" : "1px solid #E7E7E7", marginBottom: "16px"}}/>
      
      {/* New Comment Input */}
      <div className="new-comment">
        <div className="comment-input-wrapper">
          <div className="comment-input-container">
            <Input.TextArea 
              placeholder={t('OrderDetail.writeComment')} 
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              style={{ 
                backgroundColor: 'transparent', 
                color: theme.textColor,
                border: 'none',
                resize: 'none',
                width: '100%',
                paddingRight: '60px',
              }}
              autoSize={{ minRows: 1 }}
              disabled={submittingComment}
            />
            <Button 
              className="post-btn" 
              onClick={submitComment}
              loading={submittingComment}
              style={{
                position: 'absolute',
                right: '15px',
                top: '50%',
                transform: 'translateY(-50%)',
                border: 'none',
                background: 'transparent',
                color: '#2C9CDC',
                fontWeight: 'bold'
              }}
            >
              {t('OrderDetail.post')}
            </Button>
          </div>
          <Button 
            className="cancel-btn"
            onClick={() => setNewComment('')}
            style={{ 
              background: 'transparent', 
              border: 'none', 
              color: theme.secondaryTextColor,
              marginLeft: '10px'
            }}
          >
            {t('myOrders.Cancel')}
          </Button>
        </div>
      </div>
      
      {/* Comments List */}
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
        </div>
      ) : comments.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px 0', color: theme.secondaryTextColor }}>
          {t('comments.noComments')}
        </div>
      ) : (
        <div className="comment-list">
          {comments.map((comment) => (
            <div className="comment-item" key={comment.id}>
              <div className="comment-avatar">
                <Avatar src={(comment.profile_image === "" || comment.profile_image === null || comment.profile_image === undefined) ? userAvatarImage:comment.profile_image  } size={40} />
              </div>
              <div className="comment-content">
                <div className="comment-header">
                  <Text strong>{
                    comment.user_name && comment.user_name.trim() !== ""
                      ? comment.user_name
                      : (
                          comment.user_address
                            ? `${comment.user_address.substring(0, 4)}...${comment.user_address.substring(comment.user_address.length - 4)}`
                            : ""
                        )
                 }</Text>
                  <Text className="comment-time">{formatDate(comment.create_time)}</Text>
                </div>
                <div className="comment-text">
                  <Text>{comment.content}</Text>
                </div>
                <div className="comment-footer">
                  {((comment.replies && comment.replies.length > 0) || loadingReplies.includes(comment.id)) && (
                    <Button 
                      type="text" 
                      className="view-replies-btn" 
                      onClick={() => toggleReplies(comment.id)}
                      style={{ marginRight: '10px' }}
                      icon={showReplies.includes(comment.id) ? <UpOutlined /> : <DownOutlined />}
                    >
                      {showReplies.includes(comment.id) 
                        ? t('comments.hideReplies', { count: comment.replies?.length || 0 }) 
                        : t('comments.viewReplies', { count: comment.replies?.length || 0 })}
                      {loadingReplies.includes(comment.id) && <Spin size="small" style={{ marginLeft: 8 }} />}
                    </Button>
                  )}
                  
                  <Button 
                    type="text" 
                    className="reply-btn" 
                    onClick={() => toggleReplyForm(comment.id)}
                    style={{ marginRight: '10px' }}
                  >
                    {showReplyForms.includes(comment.id) ? t('comments.cancelReply') : t('comments.reply')}
                  </Button>
                  
                  {canDeleteComment(comment.user_address) && (
                    <Button 
                      type="text" 
                      danger 
                      className="delete-btn"
                      onClick={() => deleteComment(comment.id)}
                    >
                      {t('comments.delete')}
                    </Button>
                  )}
                </div>
                
                {showReplies.includes(comment.id) && (
                  <div className="replies-container">
                    {comment.replies && comment.replies.length > 0 ? (
                      comment.replies.map((reply) => (
                        <div className="reply-item" key={reply.id}>
                          <div className="reply-avatar">
                          <Avatar src={(reply.profile_image === "" || reply.profile_image === null || reply.profile_image === undefined) ? userAvatarImage:reply.profile_image  } size={32} />
                          </div>
                          <div className="reply-content">
                            <div className="reply-header">
                              <Text strong>{reply.user_name || reply.user_address.substring(0, 6) + '...'}</Text>
                              <Text className="reply-time">{formatDate(reply.create_time)}</Text>
                            </div>
                            <div className="reply-text">
                              <Text>{reply.content}</Text>
                            </div>
                            {canDeleteComment(reply.user_address) && (
                              <Button 
                                type="text" 
                                danger 
                                className="delete-btn"
                                onClick={() => deleteComment(reply.id)}
                                size="small"
                              >
                                {t('comments.delete')}
                              </Button>
                            )}
                          </div>
                        </div>
                      ))
                    ) : loadingReplies.includes(comment.id) ? (
                      <div style={{ textAlign: 'center', padding: '10px 0' }}>
                        <Spin size="small" />
                      </div>
                    ) : (
                      <div style={{ padding: '10px 0', color: theme.secondaryTextColor }}>
                        {t('comments.noReplies')}
                      </div>
                    )}
                  </div>
                )}
                
                {showReplyForms.includes(comment.id) && (
                  <div className="reply-input">
                    <div className="comment-input-wrapper">
                      <div className="comment-input-container">
                        <Input.TextArea 
                          placeholder={t('OrderDetail.writeReply')} 
                          value={replyContent[comment.id] || ''}
                          onChange={(e) => handleReplyChange(comment.id, e.target.value)}
                          style={{ 
                            backgroundColor: 'transparent', 
                            color: theme.textColor,
                            border: 'none',
                            resize: 'none',
                            width: '100%',
                            paddingRight: '60px',
                          }}
                          autoSize={{ minRows: 1 }}
                          disabled={false}
                        />
                        <Button 
                          className="post-btn" 
                          onClick={() => submitReply(comment.id)}
                          style={{
                            position: 'absolute',
                            right: '15px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            border: 'none',
                            background: 'transparent',
                            color: '#2C9CDC',
                            fontWeight: 'bold'
                          }}
                        >
                          {t('OrderDetail.post')}
                        </Button>
                      </div>
                      <Button 
                        className="cancel-btn" 
                        onClick={() => toggleReplyForm(comment.id)}
                        style={{ 
                          background: 'transparent', 
                          border: 'none', 
                          color: theme.secondaryTextColor,
                          marginLeft: '10px'
                        }}
                      >
                        {t('myOrders.Cancel')}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Pagination */}
      {totalComments > pageSize && (
        <div style={{ 
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '20px 0'
        }}>
          <Pagination 
            current={commentPage + 1} 
            total={totalComments} 
            pageSize={pageSize}
            onChange={handlePageChange} 
          />
        </div>
      )}
    </div>
  );
};

export default CommentSection;
