import React from 'react'
import ReactECharts from 'echarts-for-react'

interface LineChartProps {
  data: number[]
  xAxisData?: string[]
  theme?: 'dark' | 'light'
  color?: string
  showSymbol?: boolean
  percentages?: string[]
}

export const LineChart: React.FC<LineChartProps> = ({ 
  data, 
  xAxisData, 
  theme = 'dark', 
  color = '#2C9CDC',
  showSymbol = true,
  percentages
}) => {
  const categories = xAxisData || data.map((_, i) => (i + 1).toString())
  
  // 根据主题设置不同的颜色
  const isDarkTheme = theme === 'dark'
  // const backgroundColor = 'transparent'
  const textColor = isDarkTheme ? '#aaa' : '#666'
  const splitLineColor = isDarkTheme ? '#2C3F4F33' : '#ddd'
  const tooltipBgColor = isDarkTheme ? 'rgba(29, 43, 57, 0.9)' : 'rgba(255, 255, 255, 0.9)'
  const tooltipBorderColor = isDarkTheme ? '#2C3F4F' : '#E7E7E7'
  const tooltipTextColor = isDarkTheme ? '#fff' : '#333'

  const formatNumber = (value: number) => {
    const absValue = Math.abs(value)
    const sign = value < 0 ? '-' : ''
    
    if (absValue >= 1000000) {
      return `${sign}${(absValue / 1000000).toFixed(1)}M`
    } else if (absValue >= 1000) {
      return `${sign}${(absValue / 1000).toFixed(0)}K`
    }
    return value.toString()
  }

  // 创建固定的百分比标签
  const markPoints = []
  if (percentages && percentages.length > 0) {
    for (let i = 0; i < Math.min(data.length, percentages.length); i++) {
      markPoints.push({
        name: percentages[i],
        coord: [categories[i], data[i]],
        label: {
          formatter: percentages[i],
          position: 'top',
          distance: 10,
          color: color,
          fontSize: 12
        },
        itemStyle: {
          color: color
        }
      })
    }
  }

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '8%',
      right: '5%',
      bottom: '15%',
      top: '15%',
      containLabel: false
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: categories,
      axisLabel: {
        show: true,
        color: textColor,
        rotate: 0,
        margin: 15
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      position: 'left',
      // min: 0,
      // max: Math.max(...data) > 0 ? Math.max(...data) * 1.2 : 10, // 当最大值为0时，设置默认最大值为10
      // interval: Math.max(...data) > 0 ? Math.max(...data) / 4 : 2, // 当最大值为0时，设置默认间隔为2
      axisLabel: {
        formatter: (value: number) => formatNumber(value),
        color: textColor,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: splitLineColor,
          width: 1,
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: tooltipBgColor,
      borderColor: tooltipBorderColor,
      textStyle: {
        color: tooltipTextColor
      },
      formatter: (params: any) => {
        const dataIndex = params[0].dataIndex
        const date = categories[dataIndex]
        const value = params[0].value
        return `${date}: ${value.toFixed(2)}`
      }
    },
    series: [
      {
        type: 'line',
        data: data,
        smooth: true,
        symbol: showSymbol ? 'circle' : 'none',
        symbolSize: 8,
        showSymbol: showSymbol,
        itemStyle: {
          color: color
        },
        lineStyle: {
          color: color,
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: `${color}20` // 20% opacity
            }, {
              offset: 1, color: `${color}00` // 0% opacity
            }],
          }
        },
        markPoint: {
          symbol: 'circle',
          symbolSize: 0, // 隐藏标记点，只显示标签
          data: markPoints,
          label: {
            show: true,
            color: color,
            fontSize: 12
          }
        }
      }
    ]
  }

  return <ReactECharts 
    option={option} 
    style={{ 
      height: '100%', 
      width: '100%',
      backgroundColor: 'transparent'
    }} 
    opts={{ 
      locale: 'EN',
      renderer: 'canvas'
    }} 
  />
} 