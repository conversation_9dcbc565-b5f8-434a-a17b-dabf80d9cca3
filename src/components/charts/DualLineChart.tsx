import React from 'react'
import ReactECharts from 'echarts-for-react'

interface DualLineChartProps {
  data1: number[] // First line data (red line)
  data2: number[] // Second line data (green line)
  xAxisData?: string[]
  theme?: 'dark' | 'light'
  percentages1?: string[] // Percentages for first line
  percentages2?: string[] // Percentages for second line
  yAxis1Name?: string // 左侧Y轴名称
  yAxis2Name?: string // 右侧Y轴名称
}

export const DualLineChart: React.FC<DualLineChartProps> = ({ 
  data1, 
  data2, 
  xAxisData, 
  theme = 'dark',
  percentages1,
  percentages2,
  yAxis1Name = 'Order Quantity',
  yAxis2Name = 'Order Amount'
}) => {
  const categories = xAxisData || data1.map((_, i) => (i + 1).toString())
  
  // 根据主题设置不同的颜色
  const isDarkTheme = theme === 'dark'
  const backgroundColor = 'transparent'
  const line1Color = '#E74801' // Red for quantity
  const line2Color = '#25AE60' // Green for amount
  const textColor = isDarkTheme ? '#aaa' : '#666'
  const splitLineColor = isDarkTheme ? '#2C3F4F33' : '#ddd'
  const tooltipBgColor = isDarkTheme ? 'rgba(29, 43, 57, 0.9)' : 'rgba(255, 255, 255, 0.9)'
  const tooltipBorderColor = isDarkTheme ? '#2C3F4F' : '#E7E7E7'
  const tooltipTextColor = isDarkTheme ? '#fff' : '#333'

  const formatNumber = (value: number) => {
    const absValue = Math.abs(value)
    const sign = value < 0 ? '-' : ''
    
    if (absValue >= 1000000) {
      return `${sign}${(absValue / 1000000).toFixed(1)}M`
    } else if (absValue >= 1000) {
      return `${sign}${(absValue / 1000).toFixed(0)}K`
    }
    return value.toString()
  }

  // 创建第一条线的百分比标签
  const markPoints1 = []
  if (percentages1 && percentages1.length > 0) {
    for (let i = 0; i < Math.min(data1.length, percentages1.length); i++) {
      markPoints1.push({
        name: percentages1[i],
        coord: [categories[i], data1[i]],
        label: {
          formatter: percentages1[i],
          position: 'top',
          distance: 5,
          color: line1Color,
          fontSize: 12
        },
        itemStyle: {
          color: line1Color
        }
      })
    }
  }

  // 创建第二条线的百分比标签
  const markPoints2 = []
  if (percentages2 && percentages2.length > 0) {
    for (let i = 0; i < Math.min(data2.length, percentages2.length); i++) {
      markPoints2.push({
        name: percentages2[i],
        coord: [categories[i], data2[i]],
        label: {
          formatter: percentages2[i],
          position: 'top',
          distance: 5,
          color: line2Color,
          fontSize: 12
        },
        itemStyle: {
          color: line2Color
        }
      })
    }
  }

  const option = {
    backgroundColor: backgroundColor,
    grid: {
      left: '8%',
      right: '8%', // 增加右侧空间，为右侧Y轴留出位置
      bottom: '15%',
      top: '15%',
      containLabel: false
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: categories,
      axisLabel: {
        show: true,
        color: textColor,
        rotate: 0,
        margin: 15
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [
      {
        // 左侧Y轴 - 用于第一条线
        type: 'value',
        position: 'left',
        name: yAxis1Name,
        nameTextStyle: {
          color: textColor,
          padding: [0, 30, 0, 0] // 右侧内边距，避免与轴线重叠
        },
        // min: 0,
        // max: Math.max(...data1) * 1.2, // 根据第一条线的数据调整最大值
        // interval: Math.max(...data1) / 4,
        axisLabel: {
          formatter: (value: number) => formatNumber(value),
          color: textColor,
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: splitLineColor,
            width: 1,
          },
        },
      },
      {
        // 右侧Y轴 - 用于第二条线
        type: 'value',
        position: 'right',
        name: yAxis2Name,
        nameTextStyle: {
          color: textColor,
          padding: [0, 0, 0, 30] // 左侧内边距，避免与轴线重叠
        },
        // min: 0,
        // max: Math.max(...data2) * 1.2, // 根据第二条线的数据调整最大值
        // interval: Math.max(...data2) / 4,
        axisLabel: {
          formatter: (value: number) => formatNumber(value),
          color: textColor,
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false, // 不显示右侧Y轴的网格线，避免与左侧重叠
        },
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: tooltipBgColor,
      borderColor: tooltipBorderColor,
      textStyle: {
        color: tooltipTextColor
      },
      formatter: (params: any) => {
        const dataIndex = params[0].dataIndex
        const date = categories[dataIndex]
        let result = `${date}<br/>`
        params.forEach((param: any) => {
          const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
          result += `${marker} ${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    series: [
      {
        name: yAxis1Name,
        type: 'line',
        yAxisIndex: 0, // 使用左侧Y轴
        data: data1,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        showSymbol: true,
        itemStyle: {
          color: line1Color
        },
        lineStyle: {
          color: line1Color,
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: `${line1Color}20` // 20% opacity
            }, {
              offset: 1, color: `${line1Color}00` // 0% opacity
            }],
          }
        },
        markPoint: {
          symbol: 'circle',
          symbolSize: 0, // 隐藏标记点，只显示标签
          data: markPoints1,
          label: {
            show: true,
            color: line1Color,
            fontSize: 12
          }
        }
      },
      {
        name: yAxis2Name,
        type: 'line',
        yAxisIndex: 1, // 使用右侧Y轴
        data: data2,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        showSymbol: true,
        itemStyle: {
          color: line2Color
        },
        lineStyle: {
          color: line2Color,
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: `${line2Color}20` // 20% opacity
            }, {
              offset: 1, color: `${line2Color}00` // 0% opacity
            }],
          }
        },
        markPoint: {
          symbol: 'circle',
          symbolSize: 0, // 隐藏标记点，只显示标签
          data: markPoints2,
          label: {
            show: true,
            color: line2Color,
            fontSize: 12
          }
        }
      }
    ],
  }

  return <ReactECharts option={option} style={{ height: '100%', width: '100%' }} opts={{ locale: 'EN' }} />
}