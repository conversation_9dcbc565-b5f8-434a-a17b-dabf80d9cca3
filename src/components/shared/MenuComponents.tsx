import React from 'react';
import { Menu, Avatar,  Typography, Space,  Switch, Select,message } from 'antd';
import { RightOutlined, CopyOutlined, GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDisconnect } from '@particle-network/connectkit';
import userAvatarImage from '../../assets/figma_images/user_avatar_image.png';
import type { ThemeType } from '../../theme';
import { useNavigate } from 'react-router-dom';
import './MenuComponents.css';
// import ConnectWalletButton from '../ConnectWalletButton';
import { useUser } from '../../context/UserContext';
import { eventBus } from '../../utils/eventBus';
// import { useAccount, useConnect, useDisconnect } from 'wagmi';
// import { useWeb3Modal } from '@web3modal/wagmi/react';

const { Title, Text } = Typography;
const { Option } = Select;

interface SharedMenuProps {
  theme: ThemeType;
  isLoggedIn: boolean;
  themeClass: string;
  handleConnectWallet: () => void;
  handleLogout: () => void;
  toggleTheme?: () => void;
  onClose?: () => void;
}

// Convert to React component
export const DropdownMenu: React.FC<SharedMenuProps> = ({
  theme,
  isLoggedIn,
  themeClass,
  // handleConnectWallet,
  handleLogout,
  toggleTheme
}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { disconnect } = useDisconnect();
  const { profileData, address, logout, checkWallet, docLinks } = useUser();


  const handleNavigateToProfile = () => {
    if(address==null){checkWallet()}
    else{
      navigate('/profile');
    }
   
  };
  const handleNavigateToSetting = () => {
    if(address==null){checkWallet()}
    else{
    navigate('/setting');}
  };
  const handleNavigateToOrders = () => {
    if(address==null){checkWallet()}
    else{
    navigate('/orders');}
  };
  const handleNavigateToDashboard = () => {
    if(address==null){checkWallet()}
    else{
    navigate('/dashboard');}
  };
  
  const handleNavigateToFavorites = () => {
    if(address==null){checkWallet()}
    else{
    navigate('/favorites');}
  };
  
  // 处理文档链接点击
  const handleDocLinkClick = (linkName: string) => {
    console.log('点击文档链接:', linkName);
    console.log('可用的文档链接:', docLinks);
    
    // 确保链接正确格式化（添加http或https前缀）
    const formatUrl = (url: string) => {
      if (!url) return '';
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      return `https://${url}`;
    };
    
    // 如果 docLinks 为空或未定义，使用默认链接
    if (!docLinks || docLinks.length === 0) {
      console.log('文档链接未加载，使用默认链接');
      if (linkName === 'contracts') {
        window.open(formatUrl('www.google.com'), '_blank');
        return;
      } else if (linkName === 'arch') {
        window.open(formatUrl('www.github.com'), '_blank');
        return;
      }
      else if (linkName === 'api') {
        window.open(formatUrl('www.github.com'), '_blank');
        return;
      }
    }
    
    const docLink = docLinks.find((doc: any) => doc.name === linkName);
    if (docLink && docLink.link) {
      console.log('找到链接:', docLink.link);
      window.open(formatUrl(docLink.link), '_blank');
    } else {
      console.log(`未找到名为 ${linkName} 的文档链接`);
      // 使用默认映射作为备份
      const defaultLinks: {[key: string]: string} = {
        'contracts': 'www.google.com',
        'arch': 'www.github.com',
        'api': 'www.github.com'
      };
      
      if (defaultLinks[linkName]) {
        console.log('使用默认链接:', defaultLinks[linkName]);
        window.open(formatUrl(defaultLinks[linkName]), '_blank');
      }
    }
  };
  
  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
    // 触发语言变化事件
    eventBus.emit('languageChanged', value);
  };
  
  const handleDisconnect = () => {
    // 调用钱包插件的断开方法
    disconnect();
    // 然后调用应用的登出方法
    handleLogout();
    logout();
  };
  
  return (
    <Menu 
      className={`header-dropdown-menu ${themeClass}`}
      style={{backgroundColor: theme.backgroundColor, width: '266px'}}
    >
      {isLoggedIn  ? (
        <>
          <Menu.Item key="user-info" style={{ padding: '10px' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Avatar src={profileData && profileData.profile_image ? profileData.profile_image :userAvatarImage} size={32} style={{ border: '1px solid #478FB8' }} />
              <div style={{ marginLeft: 15, display: 'flex', flexDirection: 'column' }}>
                <Typography.Title level={5} style={{ margin: '0 0 5px 0', color: theme.textColor }}>
                  {profileData?.username || 'Anonymous'}
                </Typography.Title>
                <div style={{ 
                  backgroundColor: theme.componentBackgroundColor,
                  borderRadius: 16,
                  padding: '2px 8px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%'
                }} onClick={() => {
                  if (address) {
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                      navigator.clipboard.writeText(address);
                      message.success('copy success');
                    } else {
                      // 兼容性降级
                      const textarea = document.createElement('textarea');
                      textarea.value = address;
                      document.body.appendChild(textarea);
                      textarea.select();
                      try {
                        document.execCommand('copy');
                        message.success('copy success');
                      } catch (err) {
                        message.error('copy failed');
                      }
                      document.body.removeChild(textarea);
                    }
                  }
                }}>
                  <Typography.Text style={{ color: theme.secondaryTextColor, fontSize: 12 }}>
                    {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : ''}
                  </Typography.Text>
                  <CopyOutlined style={{ color: theme.secondaryTextColor, fontSize: 12,marginLeft: '5px' }} />
                </div>
              </div>
            </div>
          </Menu.Item>
          
          <div style={{ padding: '0 10px 10px 10px' }}>
            <button 
              onClick={handleDisconnect}
              style={{
                border: '1px solid #2C3F4F',
                borderRadius: '6px',
                color: '#97A0A4',
                backgroundColor: 'transparent',
                width: '75%',
                height: '32px',
                marginLeft: '30px',
                cursor: 'pointer',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                fontSize: '14px',
                padding: '0 15px',
                position: 'relative'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.borderColor = '#E74801';
                e.currentTarget.style.color = '#E74801';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.borderColor = '#2C3F4F';
                e.currentTarget.style.color = '#97A0A4';
              }}
            >
              <div style={{ width: '100%', textAlign: 'center' }}>
               {t('default.disconnect')}
              </div>
              <RightOutlined style={{ fontSize: '11px' }} />
            </button>
          </div>
        </>
      ) : (
        <div style={{ padding: '10px 10px 10px 10px' }}>

{/* <button className={`connect-wallet-btn ${themeClass}`} onClick={() => open()}>{t('home.connectWallet')}</button> */}

          {/* <ConnectWalletButton   onConnect={handleConnectWallet}/> */}
        </div>
      )}
      
     { isLoggedIn && ( <><Menu.ItemGroup title={t('drawer.personalCenter')} style={{ color: theme.textColor1 }}>
        <Menu.Item key="overview" onClick={handleNavigateToProfile} style={{ color: theme.textColor }}>{t('drawer.overview')}</Menu.Item>
        <Menu.Item key="setting" onClick={handleNavigateToSetting} style={{ color: theme.textColor }}>{t('drawer.setting')}</Menu.Item>
        <Menu.Item key="myOrders" onClick={handleNavigateToOrders} style={{ color: theme.textColor }}>{t('drawer.myOrders')}</Menu.Item>
        <Menu.Item key="dashboard" onClick={handleNavigateToDashboard} style={{ color: theme.textColor }}>{t('drawer.dashboard')}</Menu.Item>
        <Menu.Item key="favorites" onClick={handleNavigateToFavorites} style={{ color: theme.textColor }}>{t('drawer.myFavorites')}</Menu.Item>
      </Menu.ItemGroup></>)}
      
      <Menu.ItemGroup title={t('drawer.doc')} style={{ color: theme.textColor1 }}>
         <Menu.Item key="docContracts" onClick={() => handleDocLinkClick('api')} style={{ color: theme.textColor }}>{t('drawer.doc')}</Menu.Item>
        {/* <Menu.Item key="systemArchitecture" onClick={() => handleDocLinkClick('arch')} style={{ color: theme.textColor }}>{t('drawer.systemArchitecture')}</Menu.Item> */}
        <Menu.Item key="openAPI" onClick={() => handleDocLinkClick('arch')} style={{ color: theme.textColor }}>{t('drawer.term')}</Menu.Item>
      </Menu.ItemGroup>

      
      
      {toggleTheme && (
        <>
          <Menu.ItemGroup title={t('drawer.theme')} style={{ color: theme.textColor }}>
            <Menu.Item key="theme" style={{ padding: '10px 20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ color: theme.textColor }}>{t('app.switchTheme')}</span>
                <Switch 
                  checked={theme.name === 'dark'}
                  onChange={toggleTheme}
                  className="theme-switch"
                />
              </div>
            </Menu.Item>
          </Menu.ItemGroup>

         
        </>
      )}

      <Menu.ItemGroup title={t('drawer.language')} style={{ color: theme.textColor }}>
            <Menu.Item key="language" style={{ padding: '10px 20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ color: theme.textColor }}>{t('app.switchLanguage')}</span>
                <Select
                  className={`form-select  ${themeClass}`}
                  defaultValue={i18n.language}
                  onChange={handleLanguageChange}
                  style={{ width: 120, backgroundColor: 'transparent' }}
                  dropdownStyle={{
                    backgroundColor: theme.componentBackgroundColor, // 设置背景色
                    color: theme.textColor             // 设置文字颜色
                  }}
                  suffixIcon={<GlobalOutlined style={{ color: theme.textColor, fontSize: '14px' }} />}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <Option value="en" style={{color:theme.textColor,backgroundColor: i18n.language === "en" ? "#87BEDE" : "transparent"}}>English</Option>
                  <Option value="zh-CN" style={{color:theme.textColor,backgroundColor: i18n.language === "zh-CN" ? "#87BEDE" : "transparent"}}>简体中文</Option>
                  <Option value="zh-TW" style={{color:theme.textColor,backgroundColor: i18n.language === "zh" ? "#87BEDE" : "transparent"}}>繁體中文</Option>
                  <Option value="vi" style={{color:theme.textColor,backgroundColor: i18n.language === "vi" ? "#87BEDE" : "transparent"}}>Tiếng Việt</Option>
                  <Option value="th" style={{color:theme.textColor,backgroundColor: i18n.language === "th" ? "#87BEDE" : "transparent"}}>ไทย</Option>
                  <Option value="id" style={{color:theme.textColor,backgroundColor: i18n.language === "id" ? "#87BEDE" : "transparent"}}>Bahasa Indonesia</Option>
                </Select>
              </div>
            </Menu.Item>
        </Menu.ItemGroup>
    </Menu>
  );
};

// Convert to React component
export const DrawerContent: React.FC<SharedMenuProps> = ({
  theme,
  isLoggedIn, 
  themeClass,
  // handleConnectWallet,
  handleLogout,
  toggleTheme,
  onClose
}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { disconnect } = useDisconnect();
  const { profileData, address, docLinks } = useUser();

  const handleNavigateToProfile = () => {
    navigate('/profile');
    if (onClose) {
      onClose();
    }
  };
  const handleNavigateToSetting = () => {
    navigate('/setting');
    if (onClose) {
      onClose();
    }
  };
  
  const handleNavigateToOrders = () => {
    navigate('/orders');
    if (onClose) {
      onClose();
    }
  };
  const handleNavigateToDashboard = () => {
    navigate('/dashboard');
    if (onClose) {
      onClose();
    }
  };
  
  const handleNavigateToFavorites = () => {
    navigate('/favorites');
    if (onClose) {
      onClose();
    }
  };
  
  // 处理文档链接点击
  const handleDocLinkClick = (linkName: string) => {
    console.log('点击文档链接:', linkName);
    console.log('可用的文档链接:', docLinks);
    
    // 确保链接正确格式化（添加http或https前缀）
    const formatUrl = (url: string) => {
      if (!url) return '';
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      return `https://${url}`;
    };
    
    // 如果 docLinks 为空或未定义，使用默认链接
    if (!docLinks || docLinks.length === 0) {
      console.log('文档链接未加载，使用默认链接');
      console.log('文档链接未加载，使用默认链接');
      if (linkName === 'contracts') {
        window.open(formatUrl('www.google.com'), '_blank');
        return;
      } else if (linkName === 'arch') {
        window.open(formatUrl('www.github.com'), '_blank');
        return;
      }
      else if (linkName === 'api') {
        window.open(formatUrl('www.github.com'), '_blank');
        return;
      }
    }
    
    const docLink = docLinks.find((doc: any) => doc.name === linkName);
    if (docLink && docLink.link) {
      console.log('找到链接:', docLink.link);
      window.open(formatUrl(docLink.link), '_blank');
      if (onClose) {
        onClose();
      }
    } else {
      console.log(`未找到名为 ${linkName} 的文档链接`);
      // 使用默认映射作为备份
      const defaultLinks: {[key: string]: string} = {
        'Google': 'www.google.com',
        'Github': 'www.github.com'
      };
      
      if (defaultLinks[linkName]) {
        console.log('使用默认链接:', defaultLinks[linkName]);
        window.open(formatUrl(defaultLinks[linkName]), '_blank');
        if (onClose) onClose();
      }
    }
  };
  
  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
    // 触发语言变化事件
    eventBus.emit('languageChanged', value);
  };
  
  const handleDisconnect = () => {
    // 调用钱包插件的断开方法
    disconnect();
    // 然后调用应用的登出方法
    handleLogout();
  };
  
  return (
    <div style={{ color: theme.textColor, backgroundColor: theme.backgroundColor, height: '100%' }}>
      {isLoggedIn ? (
        <div>
          <div style={{ padding: '16px', textAlign: 'center', display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
            <Avatar size={32} src={profileData && profileData.profile_image ? profileData.profile_image :userAvatarImage} />
            <div style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'flex-start',
              marginLeft: '15px',
              width: 'calc(100% - 26px)'
            }}>
              <Title 
                level={5} 
                style={{ 
                  width: '100%',
                  marginTop: 10, 
                  color: theme.textColor,
                  textAlign: 'left'
                }}>
                {profileData?.username || 'Anonymous'}
              </Title>
              <div style={{ 
                  backgroundColor: theme.componentBackgroundColor,
                  borderRadius: 16,
                  padding: '2px 8px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '60%'
                }} onClick={() => {
                  if (address) {
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                      navigator.clipboard.writeText(address);
                      message.success('copy success');
                    } else {
                      // 兼容性降级
                      const textarea = document.createElement('textarea');
                      textarea.value = address;
                      document.body.appendChild(textarea);
                      textarea.select();
                      try {
                        document.execCommand('copy');
                        message.success('copy success');
                      } catch (err) {
                        message.error('copy failed');
                      }
                      document.body.removeChild(textarea);
                    }
                  }
                }}>
                  <Typography.Text style={{ color: theme.secondaryTextColor, fontSize: 12 }}>
                    {address ? `${address.slice(0, 8)}...${address.slice(-8)}` : ''}
                  </Typography.Text>
                  <CopyOutlined style={{ color: theme.secondaryTextColor, fontSize: 12,marginLeft: '5px' }} />
                </div>
            </div>
          </div>
          <div style={{ padding: '0 10px 10px 10px' }}>
            <button 
              onClick={handleDisconnect}
              style={{
                border: '1px solid #2C3F4F',
                borderRadius: '6px',
                color: '#97A0A4',
                backgroundColor: 'transparent',
                width: '75%',
                height: '32px',
                marginLeft: '30px',
                cursor: 'pointer',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                fontSize: '14px',
                padding: '0 15px',
                position: 'relative'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.borderColor = '#E74801';
                e.currentTarget.style.color = '#E74801';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.borderColor = '#2C3F4F';
                e.currentTarget.style.color = '#97A0A4';
              }}
            >
              <div style={{ width: '100%', textAlign: 'center' }}>
                  {t('default.disconnect')}
              </div>
              <RightOutlined style={{ fontSize: '11px' }} />
            </button>
          </div>
        </div>
      ) : (
        <div style={{ padding: '10px 10px 10px 10px' }}>
            {/* <button className={`connect-wallet-btn ${themeClass}`} onClick={() => open()}>{t('home.connectWallet')}</button> */}
            {/* <ConnectWalletButton  onConnect={handleConnectWallet}/> */}
        </div>
      )}
      <Menu 
        className={`drawer-menu ${theme.name === 'dark' ? 'dark-theme' : 'light-theme'}`}
        mode="inline"
        defaultSelectedKeys={[]}
        style={{ border: 'none', backgroundColor: 'transparent', marginTop: 20 }}
      >
        { isLoggedIn && ( <><Menu.ItemGroup title={t('drawer.personalCenter')} style={{ color: theme.textColor1 }}>
          <Menu.Item key="overview" onClick={handleNavigateToProfile} style={{ color: theme.textColor }}>{t('drawer.overview')}</Menu.Item>
          <Menu.Item key="setting" onClick={handleNavigateToSetting} style={{ color: theme.textColor }}>{t('drawer.setting')}</Menu.Item>
          <Menu.Item key="myOrders" onClick={handleNavigateToOrders} style={{ color: theme.textColor }}>{t('drawer.myOrders')}</Menu.Item>
          <Menu.Item key="dashboard" onClick={handleNavigateToDashboard} style={{ color: theme.textColor }}>{t('drawer.dashboard')}</Menu.Item>
          <Menu.Item key="favorites" onClick={handleNavigateToFavorites} style={{ color: theme.textColor }}>{t('drawer.myFavorites')}</Menu.Item>
        </Menu.ItemGroup></>)}
        
        <Menu.ItemGroup title={t('drawer.doc')} style={{ color: theme.textColor1 }}>
          <Menu.Item key="docContracts" onClick={() => handleDocLinkClick('contracts')} style={{ color: theme.textColor }}>{t('drawer.docContracts')}</Menu.Item>
          <Menu.Item key="systemArchitecture" onClick={() => handleDocLinkClick('arch')} style={{ color: theme.textColor }}>{t('drawer.systemArchitecture')}</Menu.Item>
          <Menu.Item key="openAPI" onClick={() => handleDocLinkClick('api')} style={{ color: theme.textColor }}>{t('drawer.openAI')}</Menu.Item>
        </Menu.ItemGroup>
      </Menu>
      <div style={{ padding: '24px' }}>
       

        <Space direction="vertical" style={{width: '100%'}} size={16}>
          <Space style={{ justifyContent: 'space-between', width: '100%'}}>
            <Text style={{color: theme.textColor}}>{t('drawer.theme')}</Text>
            <Switch 
              checked={theme.name === 'dark'}
              onChange={toggleTheme}
              style={{ 
                backgroundColor: theme.name === 'dark' ? '#1890ff' : '#d9d9d9'
              }}
              className="theme-switch"
            />
          </Space>
          
          <Space style={{ justifyContent: 'space-between', width: '100%'}}>
            <Text style={{color: theme.textColor}}>{t('drawer.language')}</Text>
            <Select
                  className={`form-select  ${themeClass}`}
                  defaultValue={i18n.language}
                  onChange={handleLanguageChange}
                  style={{ width: 120, backgroundColor: 'transparent' }}
                  dropdownStyle={{
                    backgroundColor: theme.componentBackgroundColor, // 设置背景色
                    color: theme.textColor             // 设置文字颜色
                  }}
                  suffixIcon={<GlobalOutlined style={{ color: theme.textColor, fontSize: '14px',marginTop: '15px' }} />}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <Option value="en" style={{color:theme.textColor,backgroundColor: i18n.language === "en" ? "#87BEDE" : "transparent"}}>English</Option>
                  <Option value="zh-CN" style={{color:theme.textColor,backgroundColor: i18n.language === "zh-CN" ? "#87BEDE" : "transparent"}}>简体中文</Option>
                  <Option value="zh-TW" style={{color:theme.textColor,backgroundColor: i18n.language === "zh-TW" ? "#87BEDE" : "transparent"}}>繁體中文</Option>
                  <Option value="vi" style={{color:theme.textColor,backgroundColor: i18n.language === "vi" ? "#87BEDE" : "transparent"}}>Tiếng Việt</Option>
                  <Option value="th" style={{color:theme.textColor,backgroundColor: i18n.language === "th" ? "#87BEDE" : "transparent"}}>ไทย</Option>
                  <Option value="id" style={{color:theme.textColor,backgroundColor: i18n.language === "id" ? "#87BEDE" : "transparent"}}>Bahasa Indonesia</Option>
                </Select>
        </Space>

        </Space>
      </div>
    </div>
  );
};

// Keep these for backward compatibility
export const renderDropdownMenu = (props: SharedMenuProps) => <DropdownMenu {...props} />;
export const renderDrawerContent = (props: SharedMenuProps) => <DrawerContent {...props} />; 