/* Setting Page Styles */
.setting-layout {
  width: 100%;
  min-height: 100vh;
}

.setting-layout.dark-theme {
  background-color: #1A253100;
  color: #fff;
}

.setting-layout.light-theme {
  background-color: #fff;
  color: #333;
}
/* 选中项无背景色 */
.drawer-menu .ant-menu-item-selected,
.drawer-menu .ant-menu-item-active,
.drawer-menu .ant-menu-item:active {
  background: #00000050 !important;
}

/* Header Styles */
.site-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  background-color: transparent;
  border-bottom: none;
}

.dark-theme .site-header {
  background-color: #1A2531;
  color: #fff;
}

.light-theme .site-header {
  background-color: #fff;
  color: #000;
}

.header-logo {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  margin-left: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

.menu-button {
  background: transparent;
  border: none;
  font-size: 20px;
  color: inherit;
}

.dark-theme .menu-button {
  color: #fff;
}

.light-theme .menu-button {
  color: #000;
}

.user-avatar {
  cursor: pointer;
}

/* Categories Bar */
.categories-container {
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 48px;
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 1px solid;
}

.dark-theme .categories-container {
  background-color: #1A2531;
  border-color: #2C3F4F;
}

.light-theme .categories-container {
  background-color: #fff;
  border-color: #E7E7E7;
}

.category-item {
  padding: 0 16px;
  height: 48px;
  line-height: 48px;
  cursor: pointer;
  position: relative;
  color: #777E8C;
}

.category-item.active {
  color: inherit;
  font-weight: 500;
}

.category-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #0852F0;
}

.category-divider {
  color: #2C3F4F;
  opacity: 0.6;
}

/* Setting Content Styles */
.setting-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.setting-title {
  margin-bottom: 24px;
  padding-left: 16px;
  font-size: 24px;
  font-weight: 500;
}

/* Settings Sections */
.settings-section {
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  margin-bottom: 20px;
  padding-left: 16px;
  font-size: 20px;
  font-weight: 500;
}

/* Avatar Upload Section */
.avatar-section {
  display: flex;
  align-items: center;
  margin-left: 16px;
  margin-bottom: 30px;
}

.profile-avatar {
  background: linear-gradient(135deg, #5A9CED, #6955EE);
  margin-right: 15px;
}

.upload-button {
  background-color: rgba(128, 128, 128, 0.2) !important;
  border: none !important;
  color: white !important;
  display: flex;
  align-items: center;
  padding: 8px 16px !important;
  border-radius: 50px !important;
  font-weight: 500;
}

.upload-button.dark-theme {
  background-color: rgba(151, 160, 164, 0.2) !important;
  color: #97A0A4 !important;
}

.upload-button.light-theme {
  background-color: rgba(119, 126, 140, 0.2) !important;
  color: #777E8C !important;
}

.upload-button:hover {
  opacity: 0.8 !important;
}

/* Form Fields */
.form-field {
  margin-top: 24px;
  margin-bottom: 24px;
  padding: 0 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 16px;
  margin-right: 16px;
}

.field-label {
  display: block;
  margin-bottom: 12px;
  font-size: 16px;
  width: 100px;
  margin-right: 16px;
}

.dark-theme .field-label {
  color: #97A0A4;
}

.light-theme .field-label {
  color: #777E8C;
}

/* Input Styles - Override Ant Design */
.form-input {
  height: 48px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  width: 100%;
  padding: 0 16px !important;
}

.dark-theme .form-input {
  background-color: #1D2B39 !important;
  border: 1px solid #2C3F4F !important;
  color: #fff !important;
}

.light-theme .form-input {
  background-color: #F5F5F5 !important;
  border: 1px solid #E7E7E7 !important;
  color: #333 !important;
}

.dark-theme .form-input::placeholder {
  color: #435363 !important;
}

.light-theme .form-input::placeholder {
  color: #ADB5BD !important;
}

/* Select Styles - Override Ant Design */
.form-select {
  width: 100%;
}

.form-select .ant-select-selector {
  height: 30px !important;
  padding: 0 16px !important;
  display: flex !important;
  align-items: center !important;
  border-radius: 8px !important;
}

.dark-theme .form-select .ant-select-selector {
  background-color: #1D2B39 !important;
  border: 1px solid #2C3F4F !important;
  color: #fff !important;
}

.light-theme .form-select .ant-select-selector {
  background-color: #F5F5F5 !important;
  border: 1px solid #E7E7E7 !important;
  color: #333 !important;
}

.form-select .ant-select-selection-item {
  line-height: 48px !important;
  font-size: 16px !important;
}

.dark-theme .form-select .ant-select-selection-item {
  color: #fff !important;
}

.light-theme .form-select .ant-select-selection-item {
  color: #333 !important;
}

.form-select .ant-select-arrow {
  color: #97A0A4 !important;
  font-size: 14px !important;
}

/* Ant Design Select Dropdown Menu Overrides */
.dark-theme .ant-select-dropdown {
  background-color: #1D2B39 !important;
  border: 1px solid #2C3F4F !important;
}

.light-theme .ant-select-dropdown {
  background-color: #fff !important;
  border: 1px solid #E7E7E7 !important;
}

.dark-theme .ant-select-item {
  color: #fff !important;
}

.light-theme .ant-select-item {
  color: #333 !important;
}

/* Dark theme selected item */
.dark-theme .ant-select-item-option-selected {
  background-color: #1D2B39 !important; /* 与背景相近 */
  color: #25AE60 !important; /* 绿色文字 */
  font-weight: bold !important;
  border-left: 4px solid #25AE60 !important; /* 绿色边框指示选中 */
}

/* Light theme selected item */
.light-theme .ant-select-item-option-selected {
  background-color: #F5F5F5 !important; /* 与背景相近 */
  color: #25AE60 !important; /* 绿色文字 */
  font-weight: bold !important;
  border-left: 4px solid #25AE60 !important; /* 绿色边框指示选中 */
}

.dark-theme .ant-select-item-option-active:not(.ant-select-item-option-selected) {
  background-color: #243546 !important;
}

.light-theme .ant-select-item-option-active:not(.ant-select-item-option-selected) {
  background-color: #F0F0F0 !important;
}

/* Save Button */
.save-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  margin: 0 16px;
  width: 150px;
  margin-bottom: 32px;
  background-color: #2C9CDC !important;
  border: none !important;
  color: white !important;
  box-shadow: none !important;
  margin-top: 24px;
}

.save-button:hover {
  background-color: #2C9CDC !important;
  opacity: 0.9;
}

/* Footer Navigation */
.mobile-footer {
  padding: 0;
}

.footer-menu {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 64px;
}

.footer-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
}

.footer-item span {
  font-size: 12px;
  margin-top: 4px;
}

.dark-theme .footer-item {
  color: #97A0A4;
}

.light-theme .footer-item {
  color: #777E8C;
}

.footer-item.active {
  color: #0852F0;
}
.connect-wallet-btn {
  border-radius: 8px;
  font-weight: 700;
  font-size: 12px;
  box-shadow: none !important;
  width: 100%;
  height: 44px;
}

.connect-wallet-btn.light-theme {
  /* background-color: #0852F0; */
  border: 1px solid #0852F0 !important;
  color: #FFFFFF;
  border-color: #0852F0;
}

.connect-wallet-btn.light-theme:hover {
  /* background-color: rgba(8, 82, 240, 0.8); */
  border: 1px solid rgba(8, 82, 240, 0.8) !important;
  border-color: #0852F0;
}

.connect-wallet-btn.dark-theme {
  /* background-color: #2C9CDC; */
  border: 1px solid #2C9CDC !important;
  color: #2C9CDC;
  /* border-color: #2C9CDC; */
}

.connect-wallet-btn.dark-theme:hover {
  /* background-color: rgba(44, 157, 220, 0.6); */
  border: 1px solid rgba(44, 157, 220, 0.6) !important;
  /* border-color: #2C9CDC; */
}

.connect-wallet-btn:active {
  filter: brightness(90%);
}

/* Media Queries */
@media (max-width: 768px) {
  .site-header {
    padding: 0 16px;
    height: 56px;
  }
  
  .logo-text {
    font-size: 20px;
  }
  
  .categories-container {
    padding: 0 16px;
    height: 40px;
  }
  
  .category-item {
    padding: 0 12px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
  
  .setting-content {
    padding: 16px;
    padding-bottom: 80px; /* Space for mobile footer */
  }
  
  .setting-title {
    font-size: 20px;
    margin-bottom: 16px;
  }
  
  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .form-field {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 0;
  }
  
  .field-label {
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
    font-size: 16px;
  }
  
  .form-input,
  .form-select {
    width: 100%;
  }
  
  .form-input,
  .form-select .ant-select-selector {
    height: 48px !important;
  }
  
  .form-select .ant-select-selection-item {
    line-height: 48px !important;
    font-size: 16px !important;
  }
  
  .save-button {
    height: 44px;
    font-size: 15px;
    margin-bottom: 24px;
  }
} 