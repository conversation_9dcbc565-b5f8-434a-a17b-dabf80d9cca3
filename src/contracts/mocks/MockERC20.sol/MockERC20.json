{"_format": "hh-sol-artifact-1", "contractName": "MockERC20", "sourceName": "contracts/mocks/MockERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}