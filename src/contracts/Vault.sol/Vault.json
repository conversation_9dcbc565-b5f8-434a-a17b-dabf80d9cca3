{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON>", "sourceName": "contracts/Vault.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Asset<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "AssetUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "Deposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "gameId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "GamePurchased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "gameId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}], "name": "GameTraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "InterestPaid", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newRate", "type": "uint256"}], "name": "InterestRateChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "rechargeAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RechargeAddressBound", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "rechargeAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "payer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RechargeCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "TokenReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokenTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"inputs": [], "name": "INITIAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_TOTAL_DEPOSIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "betManager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "deposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "freezeAsset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "frozenAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getBetManager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getFrozenAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_stablecoin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rechargeAddressToUser", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_betManager", "type": "address"}], "name": "setBetManagerAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stablecoin", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unfreezeAsset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "userGameBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userRechargeInfo", "outputs": [{"internalType": "address", "name": "rechargeAddress", "type": "address"}, {"internalType": "uint256", "name": "rechargeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bool", "name": "isUsed", "type": "bool"}, {"internalType": "address", "name": "payer", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x6080806040523460bd576000549060ff8260081c16606b575060ff808216106031575b6040516126db90816100c38239f35b60ff90811916176000557f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498602060405160ff8152a1386022565b62461bcd60e51b815260206004820152602760248201527f496e697469616c697a61626c653a20636f6e747261637420697320696e697469604482015266616c697a696e6760c81b6064820152608490fd5b600080fdfe608080604052600436101561001357600080fd5b600090813560e01c90816306fdde0314611d5057508063095ea7b314611d2957806316e64c7a14611c4557806318160ddd14611c275780631c2fa7c214611c05578063208505a51461120357806323b872dd146119bc5780632e1a7d4d146117395780632eaa224d146116ca5780632ff2e9dc146116ae578063313ce5671461169257806339509351146116355780633f4ba83a1461157a5780635c975abb146115575780636ff1c9bc1461138f57806370a0823114611341578063715018a6146112c15780638456cb591461124857806386602c84146112035780638900a754146111755780638da5cb5b1461114157806395d89b4114611044578063a457c2d714610f72578063a830e73614610de8578063a9059cbb14610d2c578063aab2355c14610cdd578063ae9695d5146102de578063b6b55f2514610b15578063b9455b5214610acd578063c4d66de814610312578063c50f4ead146102de578063dd62ed3e14610281578063e9cbd8221461024d5763f2fde38b1461019757600080fd5b3461024a57602060031936011261024a576101b0611e5a565b6101b86122df565b73ffffffffffffffffffffffffffffffffffffffff8116156101e0576101dd906123e9565b80f35b608460405162461bcd60e51b815260206004820152602660248201527f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160448201527f64647265737300000000000000000000000000000000000000000000000000006064820152fd5b80fd5b503461024a578060031936011261024a57602073ffffffffffffffffffffffffffffffffffffffff60fb5416604051908152f35b503461024a57604060031936011261024a5773ffffffffffffffffffffffffffffffffffffffff60406102b2611e5a565b92826102bc611e82565b9416815260346020522091166000526020526020604060002054604051908152f35b503461024a578060031936011261024a57602073ffffffffffffffffffffffffffffffffffffffff60fc5416604051908152f35b503461024a57602060031936011261024a5761032c611e5a565b815460ff8160081c161591828093610ac0575b8015610aa9575b15610a3f57818360017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0073ffffffffffffffffffffffffffffffffffffffff9516178655610a11575b501680156109cd57604051926103a6604085611ef8565b600b84527f5661756c7420546f6b656e0000000000000000000000000000000000000000006020850152604051936103df604086611ef8565b600285527f5654000000000000000000000000000000000000000000000000000000000000602086015261042260ff835460081c1661041d81612455565b612455565b80519067ffffffffffffffff82116109a0578190610441603654611ea5565b601f81116108f5575b50602090601f831160011461083857849261082d575b50507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8260011b9260031b1c1916176036555b835167ffffffffffffffff8111610800576104af603754611ea5565b601f811161075f575b50602094601f82116001146106a1579482939495829392610696575b50507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8260011b9260031b1c1916176037555b61051b60ff825460081c1661041d81612455565b610524336123e9565b61054f60ff825460081c1661053881612455565b61054181612455565b600160975561041d81612455565b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0060c9541660c95573ffffffffffffffffffffffffffffffffffffffff6065541690811561065257506000818152603360209081526040518281527fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef9190a37fffffffffffffffffffffffff000000000000000000000000000000000000000060fb54161760fb556105fd57005b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00ff600054166000557f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498602060405160018152a1005b606460405162461bcd60e51b815260206004820152601f60248201527f45524332303a206d696e7420746f20746865207a65726f2061646472657373006044820152fd5b0151905038806104d4565b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0821695603784527f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae91845b88811061074757508360019596979810610710575b505050811b01603755610507565b01517fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff60f88460031b161c19169055388080610702565b919260206001819286850151815501940192016106ed565b60378352601f820160051c7f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae0190602083106107d8575b601f0160051c7f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae01905b8181106107cd57506104b8565b8381556001016107c0565b7f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae9150610796565b6024827f4e487b710000000000000000000000000000000000000000000000000000000081526041600452fd5b015190503880610460565b603685527f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8927fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe016855b8181106108dd57509084600195949392106108a6575b505050811b01603655610493565b01517fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff60f88460031b161c19169055388080610898565b92936020600181928786015181550195019301610882565b90915060368452601f830160051c7f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8019060208410610978575b90601f8493920160051c7f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b801905b81811061096a575061044a565b85815584935060010161095d565b7f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8915061092f565b6024837f4e487b710000000000000000000000000000000000000000000000000000000081526041600452fd5b606460405162461bcd60e51b815260206004820152601a60248201527f496e76616c696420737461626c65636f696e20616464726573730000000000006044820152fd5b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0000166101011784553861038f565b608460405162461bcd60e51b815260206004820152602e60248201527f496e697469616c697a61626c653a20636f6e747261637420697320616c72656160448201527f647920696e697469616c697a65640000000000000000000000000000000000006064820152fd5b50303b1580156103465750600160ff831614610346565b50600160ff83161061033f565b503461024a57602060031936011261024a5773ffffffffffffffffffffffffffffffffffffffff604060209282610b02611e5a565b1681526101008452205416604051908152f35b503461024a57602060031936011261024a57600435610b32612344565b610b3a612399565b610b45811515612085565b66038d7ea4c68000610b598260355461217e565b11610c995781602073ffffffffffffffffffffffffffffffffffffffff60fb54166064604051809481937f23b872dd0000000000000000000000000000000000000000000000000000000083523360048401523060248401528760448401525af18015610c8e57610bd1918491610c5f575b5061211b565b331561065257610be38160355461217e565b603555338252603360205260408220818154019055604051818152827fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef60203393a36040519080825260208201527f73a19dd210f1a7f902193214c0ee91dd35ee5b4d920cba8d519eca65a7b488ca60403392a2600160975580f35b610c81915060203d602011610c87575b610c798183611ef8565b810190612166565b38610bcb565b503d610c6f565b6040513d85823e3d90fd5b606460405162461bcd60e51b815260206004820152601960248201527f45786365656473206d617820746f74616c206465706f736974000000000000006044820152fd5b503461024a57604060031936011261024a57604060209173ffffffffffffffffffffffffffffffffffffffff610d11611e5a565b16815260fe8352818120602435825283522054604051908152f35b503461024a57604060031936011261024a57610d46611e5a565b9060243590610d53612344565b610d5b612399565b610dab8273ffffffffffffffffffffffffffffffffffffffff851694610d8286151561203a565b610d8d821515612085565b3384526033602052610da582604086205410156120d0565b336124c6565b506040519081527f9c8515990fd8c61431c4ac8db9b81475f90c292a1dda77731e56c22e64fc764360203392a36001609755602060405160018152f35b503461024a57604060031936011261024a57610e02611e5a565b6024359073ffffffffffffffffffffffffffffffffffffffff6065541633148015610f51575b15610f0d5773ffffffffffffffffffffffffffffffffffffffff1690610e4f821515611f68565b610e5a811515611fb3565b818352603360205280610e7e604085205484865260fd602052604086205490611ffe565b10610ec95760207f16da07c7410a4953d46972497e5dd1e4d29fb8cfaa21788c6bbcc20d31afac499183855260fd825260408520610ebd82825461217e565b9055604051908152a280f35b606460405162461bcd60e51b815260206004820152601e60248201527f496e73756666696369656e7420617661696c61626c652062616c616e636500006044820152fd5b606460405162461bcd60e51b815260206004820152601860248201527f43616c6c6572206973206e6f7420617574686f72697a656400000000000000006044820152fd5b5073ffffffffffffffffffffffffffffffffffffffff60fc54163314610e28565b503461024a57604060031936011261024a57610f8c611e5a565b604060243592338152603460205281812073ffffffffffffffffffffffffffffffffffffffff84168252602052205491808310610fda57610fcf9203903361218b565b602060405160018152f35b608460405162461bcd60e51b815260206004820152602560248201527f45524332303a2064656372656173656420616c6c6f77616e63652062656c6f7760448201527f207a65726f0000000000000000000000000000000000000000000000000000006064820152fd5b503461024a578060031936011261024a5760405190806037549061106782611ea5565b80855291600181169081156110fc575060011461109f575b61109b8461108f81860382611ef8565b60405191829182611df2565b0390f35b603781527f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae939250905b8082106110e25750909150810160200161108f8261107f565b9192600181602092548385880101520191019092916110c9565b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001660208087019190915292151560051b8501909201925061108f915083905061107f565b503461024a578060031936011261024a57602073ffffffffffffffffffffffffffffffffffffffff60655416604051908152f35b503461024a57602060031936011261024a57604060a09173ffffffffffffffffffffffffffffffffffffffff6111a9611e5a565b16815260ff6020522073ffffffffffffffffffffffffffffffffffffffff808254169160018101549060036002820154910154916040519485526020850152604084015260ff81161515606084015260081c166080820152f35b503461024a57602060031936011261024a57604060209173ffffffffffffffffffffffffffffffffffffffff611237611e5a565b16815260fd83522054604051908152f35b503461024a578060031936011261024a576112616122df565b611269612399565b60017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0060c954161760c9557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2586020604051338152a180f35b503461024a578060031936011261024a576112da6122df565b8073ffffffffffffffffffffffffffffffffffffffff6065547fffffffffffffffffffffffff00000000000000000000000000000000000000008116606555167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a380f35b503461024a57602060031936011261024a576020611387611360611e5a565b73ffffffffffffffffffffffffffffffffffffffff16600052603360205260406000205490565b604051908152f35b503461024a57602060031936011261024a576113a9611e5a565b6113b16122df565b73ffffffffffffffffffffffffffffffffffffffff8060fb54169116908114611513576040517f70a08231000000000000000000000000000000000000000000000000000000008152306004820152602081602481855afa8015610c8e5783906114db575b6065546040517fa9059cbb00000000000000000000000000000000000000000000000000000000815273ffffffffffffffffffffffffffffffffffffffff909116600482015260248101829052915060208280604481015b038187875af19081156114d0576114af6020927f2e39961a70a10f4d46383948095ac2752b3ee642a7c76aa827410aaff08c2e519487916114b9575061211b565b604051908152a280f35b610c819150843d8611610c8757610c798183611ef8565b6040513d86823e3d90fd5b506020813d60201161150b575b816114f560209383611ef8565b810103126115075761146e9051611416565b8280fd5b3d91506114e8565b606460405162461bcd60e51b815260206004820152601a60248201527f43616e6e6f7420776974686472617720737461626c65636f696e0000000000006044820152fd5b503461024a578060031936011261024a57602060ff60c954166040519015158152f35b503461024a578060031936011261024a576115936122df565b60c95460ff8116156115f1577fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001660c9557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa6020604051338152a180f35b606460405162461bcd60e51b815260206004820152601460248201527f5061757361626c653a206e6f74207061757365640000000000000000000000006044820152fd5b503461024a57604060031936011261024a57610fcf9061168b6040611658611e5a565b92338152603460205281812073ffffffffffffffffffffffffffffffffffffffff8516825260205220602435905461217e565b903361218b565b503461024a578060031936011261024a57602060405160128152f35b503461024a578060031936011261024a57602090604051908152f35b503461024a57602060031936011261024a5773ffffffffffffffffffffffffffffffffffffffff6116f9611e5a565b6117016122df565b1661170d811515611f68565b7fffffffffffffffffffffffff000000000000000000000000000000000000000060fc54161760fc5580f35b503461024a57602060031936011261024a57600435611756612344565b61175e612399565b80156119785761179a816117933373ffffffffffffffffffffffffffffffffffffffff16600052603360205260406000205490565b10156120d0565b331561190e57338252603360205260408220548181106118a457819033845260336020520360408320558060355403603555816040518281527fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef60203392a360fb546040517fa9059cbb000000000000000000000000000000000000000000000000000000008152336004820152602481018390529060209082906044908290879073ffffffffffffffffffffffffffffffffffffffff165af18015610c8e5761186a918491610c5f575061211b565b6040519080825260208201527f92ccf450a286a957af52509bc1c9939d1a6a481783e142e41e2499f0bb66ebc660403392a2600160975580f35b608460405162461bcd60e51b815260206004820152602260248201527f45524332303a206275726e20616d6f756e7420657863656564732062616c616e60448201527f63650000000000000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b815260206004820152602160248201527f45524332303a206275726e2066726f6d20746865207a65726f2061646472657360448201527f73000000000000000000000000000000000000000000000000000000000000006064820152fd5b606460405162461bcd60e51b815260206004820152601d60248201527f536861726573206d7573742062652067726561746572207468616e20300000006044820152fd5b503461024a57606060031936011261024a576119d6611e5a565b906119df611e82565b916044356119eb612344565b6119f3612399565b73ffffffffffffffffffffffffffffffffffffffff82169283815260fd60205281604082205410611bc15773ffffffffffffffffffffffffffffffffffffffff851694611a4186151561203a565b611a4c831515612085565b611a7b836117938673ffffffffffffffffffffffffffffffffffffffff16600052603360205260406000205490565b84825260346020526040822073ffffffffffffffffffffffffffffffffffffffff3316835260205260408220547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8103611b2f575b508291611b036020946040937f9c8515990fd8c61431c4ac8db9b81475f90c292a1dda77731e56c22e64fc7643976124c6565b85815260fd845220611b16828254611ffe565b9055604051908152a36001609755602060405160018152f35b9291828410611b7d57611b03836040937f9c8515990fd8c61431c4ac8db9b81475f90c292a1dda77731e56c22e64fc764397611b708360209903338361218b565b9750935050509192611ad0565b606460405162461bcd60e51b815260206004820152601d60248201527f45524332303a20696e73756666696369656e7420616c6c6f77616e63650000006044820152fd5b606460405162461bcd60e51b815260206004820152601f60248201527f416d6f756e74206578636565647320756e66726f7a656e2062616c616e6365006044820152fd5b503461024a578060031936011261024a57602060405166038d7ea4c680008152f35b503461024a578060031936011261024a576020603554604051908152f35b503461024a57604060031936011261024a57611c5f611e5a565b73ffffffffffffffffffffffffffffffffffffffff60243591611c806122df565b1690611c8d821515611f68565b611c98811515611fb3565b81835260fd60205280604084205410611ce55760207f93a2467ffcee5de66e78f5df1f7fe34b5427c9e7602d2aa3dcbf07a3f3007d559183855260fd825260408520610ebd828254611ffe565b606460405162461bcd60e51b815260206004820152601a60248201527f496e73756666696369656e742066726f7a656e20616d6f756e740000000000006044820152fd5b503461024a57604060031936011261024a57610fcf611d46611e5a565b602435903361218b565b823461024a578060031936011261024a5760365481611d6e82611ea5565b80855291600181169081156110fc5750600114611d955761109b8461108f81860382611ef8565b603681527f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8939250905b808210611dd85750909150810160200161108f8261107f565b919260018160209254838588010152019101909291611dbf565b9190916020815282519283602083015260005b848110611e445750507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0601f8460006040809697860101520116010190565b8060208092840101516040828601015201611e05565b6004359073ffffffffffffffffffffffffffffffffffffffff82168203611e7d57565b600080fd5b6024359073ffffffffffffffffffffffffffffffffffffffff82168203611e7d57565b90600182811c92168015611eee575b6020831014611ebf57565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b91607f1691611eb4565b90601f7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0910116810190811067ffffffffffffffff821117611f3957604052565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b15611f6f57565b606460405162461bcd60e51b815260206004820152600f60248201527f496e76616c6964206164647265737300000000000000000000000000000000006044820152fd5b15611fba57565b606460405162461bcd60e51b815260206004820152600e60248201527f496e76616c696420616d6f756e740000000000000000000000000000000000006044820152fd5b9190820391821161200b57565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052601160045260246000fd5b1561204157565b606460405162461bcd60e51b815260206004820152601f60248201527f43616e6e6f74207472616e7366657220746f207a65726f2061646472657373006044820152fd5b1561208c57565b606460405162461bcd60e51b815260206004820152601d60248201527f416d6f756e74206d7573742062652067726561746572207468616e20300000006044820152fd5b156120d757565b606460405162461bcd60e51b815260206004820152601460248201527f496e73756666696369656e742062616c616e63650000000000000000000000006044820152fd5b1561212257565b606460405162461bcd60e51b815260206004820152600f60248201527f5472616e73666572206661696c656400000000000000000000000000000000006044820152fd5b90816020910312611e7d57518015158103611e7d5790565b9190820180921161200b57565b73ffffffffffffffffffffffffffffffffffffffff169081156122765773ffffffffffffffffffffffffffffffffffffffff1691821561220c5760207f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925918360005260348252604060002085600052825280604060002055604051908152a3565b608460405162461bcd60e51b815260206004820152602260248201527f45524332303a20617070726f766520746f20746865207a65726f20616464726560448201527f73730000000000000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b8152602060048201526024808201527f45524332303a20617070726f76652066726f6d20746865207a65726f2061646460448201527f72657373000000000000000000000000000000000000000000000000000000006064820152fd5b73ffffffffffffffffffffffffffffffffffffffff60655416330361230057565b606460405162461bcd60e51b815260206004820152602060248201527f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e65726044820152fd5b600260975414612355576002609755565b606460405162461bcd60e51b815260206004820152601f60248201527f5265656e7472616e637947756172643a207265656e7472616e742063616c6c006044820152fd5b60ff60c954166123a557565b606460405162461bcd60e51b815260206004820152601060248201527f5061757361626c653a20706175736564000000000000000000000000000000006044820152fd5b73ffffffffffffffffffffffffffffffffffffffff80606554921691827fffffffffffffffffffffffff0000000000000000000000000000000000000000821617606555167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0600080a3565b1561245c57565b608460405162461bcd60e51b815260206004820152602b60248201527f496e697469616c697a61626c653a20636f6e7472616374206973206e6f74206960448201527f6e697469616c697a696e670000000000000000000000000000000000000000006064820152fd5b73ffffffffffffffffffffffffffffffffffffffff1690811561263b5773ffffffffffffffffffffffffffffffffffffffff169182156125d15781600052603360205260406000205481811061256757817fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef9260209285600052603384520360406000205584600052603382526040600020818154019055604051908152a3565b608460405162461bcd60e51b815260206004820152602660248201527f45524332303a207472616e7366657220616d6f756e742065786365656473206260448201527f616c616e636500000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b815260206004820152602360248201527f45524332303a207472616e7366657220746f20746865207a65726f206164647260448201527f65737300000000000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b815260206004820152602560248201527f45524332303a207472616e736665722066726f6d20746865207a65726f20616460448201527f64726573730000000000000000000000000000000000000000000000000000006064820152fdfea2646970667358221220a4bb00f189a2417cb41aa91257420741b0f40401398665ac4fbe02f0591712c364736f6c634300081c0033", "deployedBytecode": "0x608080604052600436101561001357600080fd5b600090813560e01c90816306fdde0314611d5057508063095ea7b314611d2957806316e64c7a14611c4557806318160ddd14611c275780631c2fa7c214611c05578063208505a51461120357806323b872dd146119bc5780632e1a7d4d146117395780632eaa224d146116ca5780632ff2e9dc146116ae578063313ce5671461169257806339509351146116355780633f4ba83a1461157a5780635c975abb146115575780636ff1c9bc1461138f57806370a0823114611341578063715018a6146112c15780638456cb591461124857806386602c84146112035780638900a754146111755780638da5cb5b1461114157806395d89b4114611044578063a457c2d714610f72578063a830e73614610de8578063a9059cbb14610d2c578063aab2355c14610cdd578063ae9695d5146102de578063b6b55f2514610b15578063b9455b5214610acd578063c4d66de814610312578063c50f4ead146102de578063dd62ed3e14610281578063e9cbd8221461024d5763f2fde38b1461019757600080fd5b3461024a57602060031936011261024a576101b0611e5a565b6101b86122df565b73ffffffffffffffffffffffffffffffffffffffff8116156101e0576101dd906123e9565b80f35b608460405162461bcd60e51b815260206004820152602660248201527f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160448201527f64647265737300000000000000000000000000000000000000000000000000006064820152fd5b80fd5b503461024a578060031936011261024a57602073ffffffffffffffffffffffffffffffffffffffff60fb5416604051908152f35b503461024a57604060031936011261024a5773ffffffffffffffffffffffffffffffffffffffff60406102b2611e5a565b92826102bc611e82565b9416815260346020522091166000526020526020604060002054604051908152f35b503461024a578060031936011261024a57602073ffffffffffffffffffffffffffffffffffffffff60fc5416604051908152f35b503461024a57602060031936011261024a5761032c611e5a565b815460ff8160081c161591828093610ac0575b8015610aa9575b15610a3f57818360017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0073ffffffffffffffffffffffffffffffffffffffff9516178655610a11575b501680156109cd57604051926103a6604085611ef8565b600b84527f5661756c7420546f6b656e0000000000000000000000000000000000000000006020850152604051936103df604086611ef8565b600285527f5654000000000000000000000000000000000000000000000000000000000000602086015261042260ff835460081c1661041d81612455565b612455565b80519067ffffffffffffffff82116109a0578190610441603654611ea5565b601f81116108f5575b50602090601f831160011461083857849261082d575b50507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8260011b9260031b1c1916176036555b835167ffffffffffffffff8111610800576104af603754611ea5565b601f811161075f575b50602094601f82116001146106a1579482939495829392610696575b50507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8260011b9260031b1c1916176037555b61051b60ff825460081c1661041d81612455565b610524336123e9565b61054f60ff825460081c1661053881612455565b61054181612455565b600160975561041d81612455565b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0060c9541660c95573ffffffffffffffffffffffffffffffffffffffff6065541690811561065257506000818152603360209081526040518281527fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef9190a37fffffffffffffffffffffffff000000000000000000000000000000000000000060fb54161760fb556105fd57005b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00ff600054166000557f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498602060405160018152a1005b606460405162461bcd60e51b815260206004820152601f60248201527f45524332303a206d696e7420746f20746865207a65726f2061646472657373006044820152fd5b0151905038806104d4565b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0821695603784527f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae91845b88811061074757508360019596979810610710575b505050811b01603755610507565b01517fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff60f88460031b161c19169055388080610702565b919260206001819286850151815501940192016106ed565b60378352601f820160051c7f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae0190602083106107d8575b601f0160051c7f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae01905b8181106107cd57506104b8565b8381556001016107c0565b7f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae9150610796565b6024827f4e487b710000000000000000000000000000000000000000000000000000000081526041600452fd5b015190503880610460565b603685527f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8927fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe016855b8181106108dd57509084600195949392106108a6575b505050811b01603655610493565b01517fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff60f88460031b161c19169055388080610898565b92936020600181928786015181550195019301610882565b90915060368452601f830160051c7f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8019060208410610978575b90601f8493920160051c7f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b801905b81811061096a575061044a565b85815584935060010161095d565b7f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8915061092f565b6024837f4e487b710000000000000000000000000000000000000000000000000000000081526041600452fd5b606460405162461bcd60e51b815260206004820152601a60248201527f496e76616c696420737461626c65636f696e20616464726573730000000000006044820152fd5b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0000166101011784553861038f565b608460405162461bcd60e51b815260206004820152602e60248201527f496e697469616c697a61626c653a20636f6e747261637420697320616c72656160448201527f647920696e697469616c697a65640000000000000000000000000000000000006064820152fd5b50303b1580156103465750600160ff831614610346565b50600160ff83161061033f565b503461024a57602060031936011261024a5773ffffffffffffffffffffffffffffffffffffffff604060209282610b02611e5a565b1681526101008452205416604051908152f35b503461024a57602060031936011261024a57600435610b32612344565b610b3a612399565b610b45811515612085565b66038d7ea4c68000610b598260355461217e565b11610c995781602073ffffffffffffffffffffffffffffffffffffffff60fb54166064604051809481937f23b872dd0000000000000000000000000000000000000000000000000000000083523360048401523060248401528760448401525af18015610c8e57610bd1918491610c5f575b5061211b565b331561065257610be38160355461217e565b603555338252603360205260408220818154019055604051818152827fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef60203393a36040519080825260208201527f73a19dd210f1a7f902193214c0ee91dd35ee5b4d920cba8d519eca65a7b488ca60403392a2600160975580f35b610c81915060203d602011610c87575b610c798183611ef8565b810190612166565b38610bcb565b503d610c6f565b6040513d85823e3d90fd5b606460405162461bcd60e51b815260206004820152601960248201527f45786365656473206d617820746f74616c206465706f736974000000000000006044820152fd5b503461024a57604060031936011261024a57604060209173ffffffffffffffffffffffffffffffffffffffff610d11611e5a565b16815260fe8352818120602435825283522054604051908152f35b503461024a57604060031936011261024a57610d46611e5a565b9060243590610d53612344565b610d5b612399565b610dab8273ffffffffffffffffffffffffffffffffffffffff851694610d8286151561203a565b610d8d821515612085565b3384526033602052610da582604086205410156120d0565b336124c6565b506040519081527f9c8515990fd8c61431c4ac8db9b81475f90c292a1dda77731e56c22e64fc764360203392a36001609755602060405160018152f35b503461024a57604060031936011261024a57610e02611e5a565b6024359073ffffffffffffffffffffffffffffffffffffffff6065541633148015610f51575b15610f0d5773ffffffffffffffffffffffffffffffffffffffff1690610e4f821515611f68565b610e5a811515611fb3565b818352603360205280610e7e604085205484865260fd602052604086205490611ffe565b10610ec95760207f16da07c7410a4953d46972497e5dd1e4d29fb8cfaa21788c6bbcc20d31afac499183855260fd825260408520610ebd82825461217e565b9055604051908152a280f35b606460405162461bcd60e51b815260206004820152601e60248201527f496e73756666696369656e7420617661696c61626c652062616c616e636500006044820152fd5b606460405162461bcd60e51b815260206004820152601860248201527f43616c6c6572206973206e6f7420617574686f72697a656400000000000000006044820152fd5b5073ffffffffffffffffffffffffffffffffffffffff60fc54163314610e28565b503461024a57604060031936011261024a57610f8c611e5a565b604060243592338152603460205281812073ffffffffffffffffffffffffffffffffffffffff84168252602052205491808310610fda57610fcf9203903361218b565b602060405160018152f35b608460405162461bcd60e51b815260206004820152602560248201527f45524332303a2064656372656173656420616c6c6f77616e63652062656c6f7760448201527f207a65726f0000000000000000000000000000000000000000000000000000006064820152fd5b503461024a578060031936011261024a5760405190806037549061106782611ea5565b80855291600181169081156110fc575060011461109f575b61109b8461108f81860382611ef8565b60405191829182611df2565b0390f35b603781527f42a7b7dd785cd69714a189dffb3fd7d7174edc9ece837694ce50f7078f7c31ae939250905b8082106110e25750909150810160200161108f8261107f565b9192600181602092548385880101520191019092916110c9565b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001660208087019190915292151560051b8501909201925061108f915083905061107f565b503461024a578060031936011261024a57602073ffffffffffffffffffffffffffffffffffffffff60655416604051908152f35b503461024a57602060031936011261024a57604060a09173ffffffffffffffffffffffffffffffffffffffff6111a9611e5a565b16815260ff6020522073ffffffffffffffffffffffffffffffffffffffff808254169160018101549060036002820154910154916040519485526020850152604084015260ff81161515606084015260081c166080820152f35b503461024a57602060031936011261024a57604060209173ffffffffffffffffffffffffffffffffffffffff611237611e5a565b16815260fd83522054604051908152f35b503461024a578060031936011261024a576112616122df565b611269612399565b60017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0060c954161760c9557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2586020604051338152a180f35b503461024a578060031936011261024a576112da6122df565b8073ffffffffffffffffffffffffffffffffffffffff6065547fffffffffffffffffffffffff00000000000000000000000000000000000000008116606555167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a380f35b503461024a57602060031936011261024a576020611387611360611e5a565b73ffffffffffffffffffffffffffffffffffffffff16600052603360205260406000205490565b604051908152f35b503461024a57602060031936011261024a576113a9611e5a565b6113b16122df565b73ffffffffffffffffffffffffffffffffffffffff8060fb54169116908114611513576040517f70a08231000000000000000000000000000000000000000000000000000000008152306004820152602081602481855afa8015610c8e5783906114db575b6065546040517fa9059cbb00000000000000000000000000000000000000000000000000000000815273ffffffffffffffffffffffffffffffffffffffff909116600482015260248101829052915060208280604481015b038187875af19081156114d0576114af6020927f2e39961a70a10f4d46383948095ac2752b3ee642a7c76aa827410aaff08c2e519487916114b9575061211b565b604051908152a280f35b610c819150843d8611610c8757610c798183611ef8565b6040513d86823e3d90fd5b506020813d60201161150b575b816114f560209383611ef8565b810103126115075761146e9051611416565b8280fd5b3d91506114e8565b606460405162461bcd60e51b815260206004820152601a60248201527f43616e6e6f7420776974686472617720737461626c65636f696e0000000000006044820152fd5b503461024a578060031936011261024a57602060ff60c954166040519015158152f35b503461024a578060031936011261024a576115936122df565b60c95460ff8116156115f1577fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001660c9557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa6020604051338152a180f35b606460405162461bcd60e51b815260206004820152601460248201527f5061757361626c653a206e6f74207061757365640000000000000000000000006044820152fd5b503461024a57604060031936011261024a57610fcf9061168b6040611658611e5a565b92338152603460205281812073ffffffffffffffffffffffffffffffffffffffff8516825260205220602435905461217e565b903361218b565b503461024a578060031936011261024a57602060405160128152f35b503461024a578060031936011261024a57602090604051908152f35b503461024a57602060031936011261024a5773ffffffffffffffffffffffffffffffffffffffff6116f9611e5a565b6117016122df565b1661170d811515611f68565b7fffffffffffffffffffffffff000000000000000000000000000000000000000060fc54161760fc5580f35b503461024a57602060031936011261024a57600435611756612344565b61175e612399565b80156119785761179a816117933373ffffffffffffffffffffffffffffffffffffffff16600052603360205260406000205490565b10156120d0565b331561190e57338252603360205260408220548181106118a457819033845260336020520360408320558060355403603555816040518281527fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef60203392a360fb546040517fa9059cbb000000000000000000000000000000000000000000000000000000008152336004820152602481018390529060209082906044908290879073ffffffffffffffffffffffffffffffffffffffff165af18015610c8e5761186a918491610c5f575061211b565b6040519080825260208201527f92ccf450a286a957af52509bc1c9939d1a6a481783e142e41e2499f0bb66ebc660403392a2600160975580f35b608460405162461bcd60e51b815260206004820152602260248201527f45524332303a206275726e20616d6f756e7420657863656564732062616c616e60448201527f63650000000000000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b815260206004820152602160248201527f45524332303a206275726e2066726f6d20746865207a65726f2061646472657360448201527f73000000000000000000000000000000000000000000000000000000000000006064820152fd5b606460405162461bcd60e51b815260206004820152601d60248201527f536861726573206d7573742062652067726561746572207468616e20300000006044820152fd5b503461024a57606060031936011261024a576119d6611e5a565b906119df611e82565b916044356119eb612344565b6119f3612399565b73ffffffffffffffffffffffffffffffffffffffff82169283815260fd60205281604082205410611bc15773ffffffffffffffffffffffffffffffffffffffff851694611a4186151561203a565b611a4c831515612085565b611a7b836117938673ffffffffffffffffffffffffffffffffffffffff16600052603360205260406000205490565b84825260346020526040822073ffffffffffffffffffffffffffffffffffffffff3316835260205260408220547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8103611b2f575b508291611b036020946040937f9c8515990fd8c61431c4ac8db9b81475f90c292a1dda77731e56c22e64fc7643976124c6565b85815260fd845220611b16828254611ffe565b9055604051908152a36001609755602060405160018152f35b9291828410611b7d57611b03836040937f9c8515990fd8c61431c4ac8db9b81475f90c292a1dda77731e56c22e64fc764397611b708360209903338361218b565b9750935050509192611ad0565b606460405162461bcd60e51b815260206004820152601d60248201527f45524332303a20696e73756666696369656e7420616c6c6f77616e63650000006044820152fd5b606460405162461bcd60e51b815260206004820152601f60248201527f416d6f756e74206578636565647320756e66726f7a656e2062616c616e6365006044820152fd5b503461024a578060031936011261024a57602060405166038d7ea4c680008152f35b503461024a578060031936011261024a576020603554604051908152f35b503461024a57604060031936011261024a57611c5f611e5a565b73ffffffffffffffffffffffffffffffffffffffff60243591611c806122df565b1690611c8d821515611f68565b611c98811515611fb3565b81835260fd60205280604084205410611ce55760207f93a2467ffcee5de66e78f5df1f7fe34b5427c9e7602d2aa3dcbf07a3f3007d559183855260fd825260408520610ebd828254611ffe565b606460405162461bcd60e51b815260206004820152601a60248201527f496e73756666696369656e742066726f7a656e20616d6f756e740000000000006044820152fd5b503461024a57604060031936011261024a57610fcf611d46611e5a565b602435903361218b565b823461024a578060031936011261024a5760365481611d6e82611ea5565b80855291600181169081156110fc5750600114611d955761109b8461108f81860382611ef8565b603681527f4a11f94e20a93c79f6ec743a1954ec4fc2c08429ae2122118bf234b2185c81b8939250905b808210611dd85750909150810160200161108f8261107f565b919260018160209254838588010152019101909291611dbf565b9190916020815282519283602083015260005b848110611e445750507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0601f8460006040809697860101520116010190565b8060208092840101516040828601015201611e05565b6004359073ffffffffffffffffffffffffffffffffffffffff82168203611e7d57565b600080fd5b6024359073ffffffffffffffffffffffffffffffffffffffff82168203611e7d57565b90600182811c92168015611eee575b6020831014611ebf57565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b91607f1691611eb4565b90601f7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0910116810190811067ffffffffffffffff821117611f3957604052565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b15611f6f57565b606460405162461bcd60e51b815260206004820152600f60248201527f496e76616c6964206164647265737300000000000000000000000000000000006044820152fd5b15611fba57565b606460405162461bcd60e51b815260206004820152600e60248201527f496e76616c696420616d6f756e740000000000000000000000000000000000006044820152fd5b9190820391821161200b57565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052601160045260246000fd5b1561204157565b606460405162461bcd60e51b815260206004820152601f60248201527f43616e6e6f74207472616e7366657220746f207a65726f2061646472657373006044820152fd5b1561208c57565b606460405162461bcd60e51b815260206004820152601d60248201527f416d6f756e74206d7573742062652067726561746572207468616e20300000006044820152fd5b156120d757565b606460405162461bcd60e51b815260206004820152601460248201527f496e73756666696369656e742062616c616e63650000000000000000000000006044820152fd5b1561212257565b606460405162461bcd60e51b815260206004820152600f60248201527f5472616e73666572206661696c656400000000000000000000000000000000006044820152fd5b90816020910312611e7d57518015158103611e7d5790565b9190820180921161200b57565b73ffffffffffffffffffffffffffffffffffffffff169081156122765773ffffffffffffffffffffffffffffffffffffffff1691821561220c5760207f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925918360005260348252604060002085600052825280604060002055604051908152a3565b608460405162461bcd60e51b815260206004820152602260248201527f45524332303a20617070726f766520746f20746865207a65726f20616464726560448201527f73730000000000000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b8152602060048201526024808201527f45524332303a20617070726f76652066726f6d20746865207a65726f2061646460448201527f72657373000000000000000000000000000000000000000000000000000000006064820152fd5b73ffffffffffffffffffffffffffffffffffffffff60655416330361230057565b606460405162461bcd60e51b815260206004820152602060248201527f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e65726044820152fd5b600260975414612355576002609755565b606460405162461bcd60e51b815260206004820152601f60248201527f5265656e7472616e637947756172643a207265656e7472616e742063616c6c006044820152fd5b60ff60c954166123a557565b606460405162461bcd60e51b815260206004820152601060248201527f5061757361626c653a20706175736564000000000000000000000000000000006044820152fd5b73ffffffffffffffffffffffffffffffffffffffff80606554921691827fffffffffffffffffffffffff0000000000000000000000000000000000000000821617606555167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0600080a3565b1561245c57565b608460405162461bcd60e51b815260206004820152602b60248201527f496e697469616c697a61626c653a20636f6e7472616374206973206e6f74206960448201527f6e697469616c697a696e670000000000000000000000000000000000000000006064820152fd5b73ffffffffffffffffffffffffffffffffffffffff1690811561263b5773ffffffffffffffffffffffffffffffffffffffff169182156125d15781600052603360205260406000205481811061256757817fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef9260209285600052603384520360406000205584600052603382526040600020818154019055604051908152a3565b608460405162461bcd60e51b815260206004820152602660248201527f45524332303a207472616e7366657220616d6f756e742065786365656473206260448201527f616c616e636500000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b815260206004820152602360248201527f45524332303a207472616e7366657220746f20746865207a65726f206164647260448201527f65737300000000000000000000000000000000000000000000000000000000006064820152fd5b608460405162461bcd60e51b815260206004820152602560248201527f45524332303a207472616e736665722066726f6d20746865207a65726f20616460448201527f64726573730000000000000000000000000000000000000000000000000000006064820152fdfea2646970667358221220a4bb00f189a2417cb41aa91257420741b0f40401398665ac4fbe02f0591712c364736f6c634300081c0033", "linkReferences": {}, "deployedLinkReferences": {}}