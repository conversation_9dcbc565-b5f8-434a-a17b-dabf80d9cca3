@font-face {
  font-family: 'OpenSauceOne-Black';
  src: url('./assets/OpenSauceOne-Medium.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

html, body, * {
  font-family: 'OpenSauceOne-Black', sans-serif !important;
}

body {
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden; /* Prevent horizontal scroll */
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  /* 背景颜色由主题控制 */
  background-color: inherit; 
}

/* 深色主题下的根元素背景色 */
body.dark-theme,
body.dark-theme #root {
  background-color: #1D2B39;
}

/* 浅色主题下的根元素背景色 */
body.light-theme,
body.light-theme #root {
  background-color: #ffffff;
}

/* 应用于所有元素的盒模型设置 */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Ensure the main app container within #root also tries to fill height if needed */
/* ThemedContainer already has min-height: 100vh, so this is mostly for robustness */
#root > * {
  /* min-height: 100%; */
  width: 100%;
  padding: 0;
  margin: 0;
}

/* 确保所有布局组件默认占满宽度 */
.ant-layout {
  width: 100%;
  padding: 0;
  margin: 0;
  min-height: 100vh;
}

/* 头部导航样式覆盖 */
.ant-layout-header {
  width: 100% !important;
  padding: 0 16px !important;
  margin: 0 !important;
}

/* 内容区域样式覆盖 */
.ant-layout-content {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  flex: 1;
}

/* 确保页脚背景色与主题一致 */
.ant-layout-footer {
  background-color: inherit !important;
}

body.dark-theme .ant-layout-footer {
  background-color: #1D2B39 !important;
}

body.light-theme .ant-layout-footer {
  background-color: #ffffff !important;
}

/* Smooth scrolling for the entire site */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Dark theme scrollbar */
.dark-theme ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Ensure all antd buttons have proper transitions */
.ant-btn {
  transition: all 0.3s ease;
}

/* Fix ant-menu overflows in mobile view */
.ant-menu-horizontal {
  flex-wrap: nowrap;
  overflow-x: auto;
}

.ant-menu-horizontal::-webkit-scrollbar {
  display: none;
}

/* Responsive font sizes */
@media (max-width: 768px) {
  h1.ant-typography {
    font-size: 28px !important;
  }
  
  h2.ant-typography {
    font-size: 24px !important;
  }
  
  h3.ant-typography {
    font-size: 20px !important;
  }
}

@media (max-width: 576px) {
  h1.ant-typography {
    font-size: 24px !important;
  }
  
  h2.ant-typography {
    font-size: 20px !important;
  }
  
  h3.ant-typography {
    font-size: 18px !important;
  }
}

/* 自定义Modal样式，移除边框 */
.custom-modal-without-borders .ant-modal-header {
  border-bottom: none !important;
}

.custom-modal-without-borders .ant-modal-footer {
  border-top: none !important;
}

.custom-modal-without-borders .ant-btn-primary {
  box-shadow: none !important;
}

/* 覆盖默认的Modal样式 */
.ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}
