import React from "react";
import { ConnectKitProvider, createConfig } from "@particle-network/connectkit";
import { authWalletConnectors } from "@particle-network/connectkit/auth";
import { define<PERSON>hain } from "@particle-network/connectkit/chains";
import { wallet, EntryPosition } from "@particle-network/connectkit/wallet";
import { evmWalletConnectors } from '@particle-network/connectkit/evm';

// 定义Amoy测试网配置
// export const AmoyTestnet = defineChain({
//   id: 80002,
//   name: "Amoy",
//   nativeCurrency: {
//     decimals: 18,
//     name: "MATIC",
//     symbol: "MATIC",
//   },
//   rpcUrls: {
//     default: {
//       http: ["https://rpc-amoy.polygon.technology"],
//     },
//   },
//   blockExplorers: {
//     default: { name: "Explorer", url: "https://amoy.polygonscan.com/" },
//   },
//   testnet: true,
// });

// 定义BSC测试网配置
export const BSCTestnet = define<PERSON>hain({
  id: 97,
  name: "BSC Testnet",
  nativeCurrency: {
    decimals: 18,
    name: "BNB",
    symbol: "tBNB",
  },
  rpcUrls: {
    default: {
      http: ["https://bsc-testnet.public.blastapi.io"],
    },
  },
  blockExplorers: {
    default: { name: "BscScan", url: "https://testnet.bscscan.com/" },
  },
  testnet: true,
});

// 从环境变量获取项目配置
const projectId = import.meta.env.VITE_PROJECT_ID;
const clientKey = import.meta.env.VITE_CLIENT_KEY;
const appId = import.meta.env.VITE_APP_ID;
const walletConnectProjectId = import.meta.env.VITE_WALLETCONNECT_PROJECT_ID;
if (!projectId || !clientKey || !appId) {
  throw new Error('Please configure the Particle project in .env first!');
}

// 创建Particle Connect配置
const config = createConfig({
  projectId,
  clientKey,
  appId,
  appearance: {
    mode: 'auto',
  },
  chains: [BSCTestnet],
  walletConnectors: [
    evmWalletConnectors({
      metadata: { 
        name: "Connectkit Demo",
        description: "Particle Network ConnectKit Demo",
        url: window.location.origin,
      },
      walletConnectProjectId: walletConnectProjectId,
    }),
    authWalletConnectors({
      authTypes: ['google'],
      fiatCoin: 'USD',
      promptSettingConfig: {
        promptMasterPasswordSettingWhenLogin: 1,
        promptPaymentPasswordSettingWhenSign: 1,
      },
    }),
  ],
  plugins: [
    wallet({
      visible: false,
      entryPosition: EntryPosition.BR,
    }),
  ],
});

// 创建ParticleConnectKit组件
export const ParticleConnectkit = ({ children }: React.PropsWithChildren) => {
  return <ConnectKitProvider config={config}>{children}</ConnectKitProvider>;
};

// 导出有用的hooks供其他组件使用
export {
  useAccount,
  useConnect,
  useDisconnect,
  ConnectButton,
  useParticleAuth,
  useWallets,
  usePublicClient
} from "@particle-network/connectkit"; 