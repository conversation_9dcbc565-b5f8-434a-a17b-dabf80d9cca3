:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局背景色设置 */
html, body {
  background-color: #ffffff; /* 默认为浅色背景 */
}

body.dark-theme, 
body.dark-theme html {
  background-color: #1D2B39;
}

body.light-theme,
body.light-theme html {
  background-color: #ffffff;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  /* min-height: 100vh; */
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}



/* 移除媒体查询，由主题系统控制颜色 */
